import constant from '@/common/constant.js'
import {
	iImage,
	iProtocolUrl
} from "@/common/utils.js"
export default {
	data() {
		return {
			// 全局变量
			cons: constant
		}
	},
	methods: {
		// 图片地址
		iImage(name, packagex) {
			return iImage(name, packagex)
		},
		// 协议地址
		iProtocolUrl(type) {
			return iProtocolUrl(type)
		},
		// 移除对应类型红点
		removeMsgDot({
			type = ''
		}) {
			uni.$u.http.post('/user/userClick', {
				type: encodeURIComponent(type)
			});
		},
		// 价格格式化
		// formattedPrice({
		// 	price,
		// 	fontSize = '12px',
		// 	fontWeight = '500',
		// 	highlightSize = '24px',
		// 	highlightWeight = 'bold'
		// }) {
		// 	// 判断是否为空
		// 	if (uni.$u.test.isEmpty(price)) {
		// 		return '-';
		// 	}
		// 	// 将 price 从字符串转换为数字，如果转换失败，设置为默认值 0.00
		// 	const numericPrice = !isNaN(Number(price)) ? Number(price) : 0.00;
		// 	// 将价格格式化为两位小数
		// 	const [integerPart, decimalPart] = numericPrice.toFixed(2).split('.');
		// 	// 判断是否需要显示小数部分（这里可以保持为 true 以强制显示小数部分）
		// 	// const showDecimal = decimalPart !== '00';
		// 	const showDecimal = true;
		// 	// 返回 JSON 结构
		// 	return [{
		// 			name: 'span',
		// 			attrs: {
		// 				style: `font-size: ${fontSize}; font-weight: ${fontWeight};` // ¥ 的样式
		// 			},
		// 			children: [{
		// 				type: 'text',
		// 				text: '¥' // 货币符号
		// 			}]
		// 		},
		// 		{
		// 			name: 'span',
		// 			attrs: {
		// 				style: `font-size: ${highlightSize}; font-weight: ${highlightWeight};` // 整数部分的样式
		// 			},
		// 			children: [{
		// 				type: 'text',
		// 				text: integerPart // 显示整数部分
		// 			}]
		// 		},
		// 		{
		// 			name: 'span',
		// 			attrs: {
		// 				style: `font-size: ${fontSize}; font-weight: ${fontWeight};` // 小数部分和单位的样式
		// 			},
		// 			children: [{
		// 				type: 'text',
		// 				text: showDecimal ? `.${decimalPart}/起` : `/起` // 根据条件显示小数部分
		// 			}]
		// 		}
		// 	];
		// },
		onShareAppMessage(res) {
			return {
				title: '',
				// #ifdef MP-TOUTIAO
				desc: '',
				// #endif
				imageUrl: '',
				path: '/pages/index/index'
			}
		},
		onShareTimeline(res) {
			return {
				title: '',
				imageUrl: '',
				path: '/pages/index/index'
			}
		}
	}
}