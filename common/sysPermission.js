import permision from '@/plugins/permission.js'

const perMaps = {
	'album': 'android.permission.READ_EXTERNAL_STORAGE',
	'camera': 'android.permission.CAMERA',
	'microp': 'android.permission.RECORD_AUDIO',
	'call': 'android.permission.CALL_PHONE',
	'location': 'android.permission.ACCESS_COARSE_LOCATION'
}

const perTipMaps = {
	'album': '请在设置中允许访问相册权限，以便读取和保存照片。',
	'camera': '请在设置中允许访问摄像头权限，以便拍摄照片或视频。',
	'microp': '请在设置中允许访问麦克风权限，以便录音。',
	'call': '请在设置中允许拨打电话权限，以便直接拨打电话号码。',
	'location': '请在设置中允许位置权限，以便获取您的位置信息。'
};


/**
 * @param {Object} permissionID
 * -------microphoneAuthorized 允许使用麦克风的权限
 * -------cameraAuthorized 允许使用摄像头的权限
 * -------notificationAuthorized 允许通知的开关
 * @param {Object} title    权限使用告知的标题
 * @param {Object} content  权限使用告知的描述
 * @param {Object} callback 授权回调 [1已授权  2未获得授权  3被永久拒绝权限]
 */
const judgePermission = (permissionID, title, descr, callback) => {
	// #ifdef MP
	callback && callback(1);
	return;
	// #endif

	const appAuthorizeSetting = uni.getAppAuthorizeSetting()
	var authorizedState;
	if (permissionID == "album") { // iOS支持
		if (plus.os.name == 'Android') {
			plus.android.checkPermission(perMaps[permissionID],
				granted => {
					if (granted.checkResult == 0) {
						callback && callback(1);
					} else {
						let params = {
							title: title || '',
							content: descr || ''
						};
						showTopPopup(params, () => {});
						requestPermission(permissionID, authorizedState, callback)
					}
					console.log("检测权限", granted)
				},
				error => {
					console.log("检测权限Error", error.message)
				}
			);
			return
		} else {
			// iOS支持
			authorizedState = appAuthorizeSetting.albumAuthorized;
		}
	} else if (permissionID == "camera") {
		// Android + iOS支持
		authorizedState = appAuthorizeSetting.cameraAuthorized;
	} else if (permissionID == "microp") {
		// Android + iOS支持
		authorizedState = appAuthorizeSetting.microphoneAuthorized;
	} else if (permissionID == "push") {
		// Android + iOS支持
		authorizedState = appAuthorizeSetting.notificationAuthorized;
	} else if (permissionID == 'location') {
		let isOpen = checkOpenGPS()
		if (!isOpen) {
			callback && callback(0);
			return
		}
		authorizedState = appAuthorizeSetting.locationAuthorized;
	} else if (permissionID == "call") {
		if (plus.os.name == 'Android') {
			plus.android.checkPermission(perMaps[permissionID],
				granted => {
					if (granted.checkResult == 0) {
						callback && callback(1);
					} else {
						let params = {
							title: title || '',
							content: descr || ''
						};
						showTopPopup(params, () => {});
						requestPermission(permissionID, authorizedState, callback)
					}
					console.log("检测权限", granted)
				},
				error => {
					console.log("检测权限Error", error.message)
				}
			);
		} else {
			callback && callback(1);
		}
		return
	}
	console.log("权限-------", appAuthorizeSetting)
	console.log("权限状态", authorizedState);
	if (authorizedState && authorizedState == 'authorized') {
		// 标识已授权
		callback && callback(1);
	} else {
		let params = {
			title: title || '',
			content: descr || ''
		};
		if (authorizedState !== 'denied') {
			showTopPopup(params, () => {});
		}

		requestPermission(permissionID, authorizedState, callback)
	}
	console.log(authorizedState)

}

/**
 * @param {Object} permissionID  需要的授权标识
 * @param {Object} authorizedState   权限状态
 * @param {Object} callback  回调
 */
function requestPermission(permissionID, authorizedState, callback) {
	if (plus.os.name == 'Android') {
		// android的权限是动态申请
		permision.requestAndroidPermission(perMaps[permissionID]).then(result => {
			setTimeout(() => {
				closePopup()
			}, 500)
			console.log("授权结果", result)
			if (result == 1) {
				// 已获得授权
				callback && callback(1);
			} else if (result == 0) {
				// 未获得授权
				callback && callback(2);
			} else {
				// 被永久拒绝权限
				uni.dialog.confirm("提示", perTipMaps[permissionID], () => {
					permision.gotoAppPermissionSetting();
				}, () => {
					// 取消
				}, '去开通', '先不了');
				callback && callback(3);
			}
		})
	} else if (plus.os.name == 'iOS') {
		setTimeout(() => {
			closePopup()
		}, 5000)
		if (authorizedState == 'not determined') {
			callback && callback(1);
		} else {
			// uni.showModal({
			uni.dialog.confirm("提示", perTipMaps[permissionID], () => {
				permision.gotoAppPermissionSetting();
			}, () => {
				// 取消
			}, '去开通', '先不了');
			callback && callback(3);
		}
	}

}

// 关闭弹窗
const closePopup = () => {
	let mask = plus.nativeObj.View.getViewById('MaskLayer');
	let popup = plus.nativeObj.View.getViewById('PopupView');
	if (mask) {
		mask.hide();
		mask.close();

	}
	if (popup) {
		popup.hide();
		popup.close();
	}
}

// 授权描述告知弹窗
const showTopPopup = (data, callback) => {
	// 弹窗遮罩层
	let maskLayer = new plus.nativeObj.View("MaskLayer", { //先创建遮罩层
		top: '0px',
		left: '0px',
		height: '100%',
		width: '100%',
		backgroundColor: 'rgba(0,0,0,0.5)'
		// backgroundColor: '#FFFFFF'
	});

	// 以下为计算菜单的nview绘制布局，为固定算法，使用者无关关心
	const screenWidth = plus.screen.resolutionWidth;
	const screenHeight = plus.screen.resolutionHeight;
	//弹窗容器宽度
	const popupViewWidth = screenWidth;
	// 弹窗容器的Padding
	const viewContentPadding = 20;
	// 弹窗容器的宽度
	const viewContentWidth = (plus.os.name == 'Android' ? (screenWidth - 2 * viewContentPadding) : (screenWidth -
		3 *
		viewContentPadding));
	// 描述的列表
	const descriptionList = drawtext(data.content, viewContentWidth);
	// 弹窗容器高度
	let popupViewHeight = 80 + 20 + 20 + 20 + 10;

	let popupViewContentList = [{
		tag: 'font',
		id: 'title',
		text: data.title,
		textStyles: {
			size: '18px',
			color: "#000",
			weight: "500",
			whiteSpace: "normal"
		},
		position: {
			top: '60px',
			left: viewContentPadding + "px",
			width: viewContentWidth + "px",
			height: "30px",
		}
	}];
	const textHeight = 20;
	let contentTop = 105;
	descriptionList.forEach((item, index) => {
		if (index > 0) {
			popupViewHeight += textHeight;
			contentTop += textHeight;
		}
		popupViewContentList.push({
			tag: 'font',
			id: 'content' + index + 1,
			text: item.content,
			textStyles: {
				size: '15px',
				color: "#666",
				lineSpacing: "50%",
				align: "left"
			},
			position: {
				top: contentTop + "px",
				left: viewContentPadding + "px",
				width: viewContentWidth + "px",
				height: textHeight + "px",
			}
		});
		if (item.type == "break") {
			contentTop += 10;
			popupViewHeight += 10;
		}
	});

	// 弹窗内容
	let popupView = new plus.nativeObj.View("PopupView", {
		tag: "rect",
		top: '0px',
		left: '0px',
		height: popupViewHeight + "px",
		width: "100%"
	});
	// 绘制白色背景
	popupView.drawRect({
		color: "#FFFFFF",
		radius: "8px"
	}, {
		top: "40px",
		height: popupViewHeight - 40 + "px",
	});

	popupView.draw(popupViewContentList);

	// 显示弹窗
	maskLayer.show();
	popupView.show();
}

// 授权描述告知弹窗
const showBottomPopup = (data, callback) => {
	// 弹窗遮罩层
	let maskLayer = new plus.nativeObj.View("MaskLayer", { //先创建遮罩层
		top: '0px',
		left: '0px',
		height: '100%',
		width: '100%',
		backgroundColor: 'rgba(0,0,0,0.5)'
	});

	// 以下为计算菜单的nview绘制布局，为固定算法，使用者无关关心
	const screenWidth = plus.screen.resolutionWidth;
	const screenHeight = plus.screen.resolutionHeight;
	//弹窗容器宽度
	const popupViewWidth = screenWidth;
	// 弹窗容器的Padding
	const viewContentPadding = 20;
	// 弹窗容器的宽度
	const viewContentWidth = (plus.os.name == 'Android' ? (screenWidth - 2 * viewContentPadding) : (screenWidth -
		3 *
		viewContentPadding));
	// 描述的列表
	const descriptionList = drawtext(data.content, viewContentWidth);
	// 弹窗容器高度
	let popupViewHeight = 80 + 20 + 20 + 90 + 10;

	let popupViewContentList = [{
		tag: 'font',
		id: 'title',
		text: data.title,
		textStyles: {
			size: '18px',
			color: "#000",
			weight: "500",
			whiteSpace: "normal"
		},
		position: {
			top: '60px',
			left: viewContentPadding + "px",
			width: viewContentWidth + "px",
			height: "30px",
		}
	}];
	const textHeight = 20;
	let contentTop = 105;
	descriptionList.forEach((item, index) => {
		if (index > 0) {
			popupViewHeight += textHeight;
			contentTop += textHeight;
		}
		popupViewContentList.push({
			tag: 'font',
			id: 'content' + index + 1,
			text: item.content,
			textStyles: {
				size: '15px',
				color: "#666",
				lineSpacing: "50%",
				align: "left"
			},
			position: {
				top: contentTop + "px",
				left: viewContentPadding + "px",
				width: viewContentWidth + "px",
				height: textHeight + "px",
			}
		});
		if (item.type == "break") {
			contentTop += 10;
			popupViewHeight += 10;
		}
	});

	//绘制底边按钮
	popupViewContentList.push({
		tag: 'rect',
		rectStyles: {
			radius: "10px",
			color: '#383CBD'
		},
		position: {
			bottom: viewContentPadding + 'px',
			left: viewContentPadding + "px",
			width: viewContentWidth + "px",
			height: "50px"
		}
	});
	popupViewContentList.push({
		tag: 'font',
		id: 'confirmText',
		text: "我知道了",
		textStyles: {
			size: '18px',
			color: "#FFF",
			weight: "bold",
			lineSpacing: "0%",
		},
		position: {
			bottom: viewContentPadding + 'px',
			left: viewContentPadding + "px",
			width: viewContentWidth + "px",
			height: "50px"
		}
	});

	// 弹窗内容
	let popupView = new plus.nativeObj.View("PopupView", {
		tag: "rect",
		bottom: '0px',
		left: '0px',
		height: popupViewHeight + "px",
		width: "100%"
	});
	// 绘制白色背景
	popupView.drawRect({
		color: "#FFFFFF",
		radius: "8px"
	}, {
		top: "40px",
		height: popupViewHeight - 40 + "px",
	});

	popupView.draw(popupViewContentList);
	popupView.addEventListener("click", function(e) {
		let maxTop = popupViewHeight - viewContentPadding;
		let maxLeft = popupViewWidth - viewContentPadding;
		let buttonWidth = (viewContentWidth - viewContentPadding) / 2;
		if (e.clientY > maxTop - 50 && e.clientY < maxTop) {
			if (e.clientX > viewContentPadding && e.clientX < maxLeft) {
				// 立即升级
				maskLayer.hide();
				popupView.hide();
				callback && callback();
			}
		}
	});
	// 点击遮罩层
	maskLayer.addEventListener("click", function() { //处理遮罩层点击
		maskLayer.hide();
		popupView.hide();
	});
	maskLayer.addEventListener("back", function() { //处理遮罩层点击
		maskLayer.hide();
		popupView.hide();
	});
	// 显示弹窗
	maskLayer.show();
	popupView.show();
}

// 文字换行
const drawtext = (text, maxWidth) => {
	let textArr = text.split("");
	let len = textArr.length;
	// 上个节点
	let previousNode = 0;
	// 记录节点宽度
	let nodeWidth = 0;
	// 文本换行数组
	let rowText = [];
	// 如果是字母，侧保存长度
	let letterWidth = 0;
	// 汉字宽度
	let chineseWidth = 15;
	// otherFont宽度
	let otherWidth = 7;
	for (let i = 0; i < len; i++) {
		if (/[\u4e00-\u9fa5]|[\uFE30-\uFFA0]/g.test(textArr[i])) {
			if (letterWidth > 0) {
				if (nodeWidth + chineseWidth + letterWidth * otherWidth > maxWidth) {
					rowText.push({
						type: "text",
						content: text.substring(previousNode, i)
					});
					previousNode = i;
					nodeWidth = chineseWidth;
					letterWidth = 0;
				} else {
					nodeWidth += chineseWidth + letterWidth * otherWidth;
					letterWidth = 0;
				}
			} else {
				if (nodeWidth + chineseWidth > maxWidth) {
					rowText.push({
						type: "text",
						content: text.substring(previousNode, i)
					});
					previousNode = i;
					nodeWidth = chineseWidth;
				} else {
					nodeWidth += chineseWidth;
				}
			}
		} else {
			if (/\n/g.test(textArr[i])) {
				rowText.push({
					type: "break",
					content: text.substring(previousNode, i)
				});
				previousNode = i + 1;
				nodeWidth = 0;
				letterWidth = 0;
			} else if (textArr[i] == "\\" && textArr[i + 1] == "n") {
				rowText.push({
					type: "break",
					content: text.substring(previousNode, i)
				});
				previousNode = i + 2;
				nodeWidth = 0;
				letterWidth = 0;
			} else if (/[a-zA-Z0-9]/g.test(textArr[i])) {
				letterWidth += 1;
				if (nodeWidth + letterWidth * otherWidth > maxWidth) {
					rowText.push({
						type: "text",
						content: text.substring(previousNode, i + 1 - letterWidth)
					});
					previousNode = i + 1 - letterWidth;
					nodeWidth = letterWidth * otherWidth;
					letterWidth = 0;
				}
			} else {
				if (nodeWidth + otherWidth > maxWidth) {
					rowText.push({
						type: "text",
						content: text.substring(previousNode, i)
					});
					previousNode = i;
					nodeWidth = otherWidth;
				} else {
					nodeWidth += otherWidth;
				}
			}
		}
	}
	if (previousNode < len) {
		rowText.push({
			type: "text",
			content: text.substring(previousNode, len)
		});
	}
	return rowText;
}

/**
 * 检查设备是否打开了GPS
 */
export const checkOpenGPS = (tip = true) => {
	// #ifdef APP-PLUS
	let system = uni.getSystemInfoSync();
	if (system.platform === 'android') {
		var context = plus.android.importClass("android.content.Context");
		var locationManager = plus.android.importClass("android.location.LocationManager");
		var main = plus.android.runtimeMainActivity();
		var mainSvr = main.getSystemService(context.LOCATION_SERVICE);
		if (!mainSvr.isProviderEnabled(locationManager.GPS_PROVIDER)) {
			if (tip) {
				uni.dialog.confirm("提示", "请打开定位服务功能~", () => {
					if (!mainSvr.isProviderEnabled(locationManager.GPS_PROVIDER)) {
						openGPS()
					} else {
						console.log('GPS功能已开启');
					}
				}, () => {
					// 取消
				}, '去打开', '先不了');
			}
			return false
		} else {
			return true
		}
	} else if (system.platform === 'ios') {
		var cllocationManger = plus.ios.import("CLLocationManager");
		var enable = cllocationManger.locationServicesEnabled();
		var status = cllocationManger.authorizationStatus();
		plus.ios.deleteObject(cllocationManger);
		if (enable && status != 2) {
			return true
		} else {
			if (tip) {
				uni.dialog.confirm("提示", "请前往设置-隐私-定位服务打开定位服务功能~", () => {
					openGPS()
				}, () => {
					// 取消
				}, '去打开', '先不了');
			}
			return false
		}
	}
	// #endif

	// #ifdef MP
	return true
	// #endif
}

/**
 *  打开系统设置GPS服务页面
 */
export const openGPS = () => {
	// #ifdef APP-PLUS
	const system = uni.getSystemInfoSync();
	if (system.platform === 'android') {
		var main = plus.android.runtimeMainActivity();
		var Intent = plus.android.importClass('android.content.Intent');
		var Settings = plus.android.importClass('android.provider.Settings');
		var intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
		main.startActivity(intent); // 打开系统设置GPS服务页面
	} else if (system.platform === 'ios') {
		var UIApplication = plus.ios.import("UIApplication");
		var application2 = UIApplication.sharedApplication();
		var NSURL2 = plus.ios.import("NSURL");
		var setting2 = NSURL2.URLWithString("App-Prefs:root=Privacy&path=LOCATION");
		application2.openURL(setting2);
		plus.ios.deleteObject(setting2);
		plus.ios.deleteObject(NSURL2);
		plus.ios.deleteObject(application2);
	}
	// #endif
}

module.exports = {
	judgePermission: judgePermission,
	closePopup: closePopup,
	checkOpenGPS: checkOpenGPS,
	openGPS: openGPS
}