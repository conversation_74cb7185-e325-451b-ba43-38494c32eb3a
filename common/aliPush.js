/**
 Android 推送通知
 Android推送 区分设备是否在线
 1、在线，通过阿里云自有通道推送，uniapp在registerPush的回调中，接收到推送相关的数据和事件，sdk会创建相关的通知
 2、离线，如果配置了厂商通道，通过厂商通道推送，当用户点击通知时，通过registerThirdPush的回调获取推送数据
 **注意：使用厂商通道需要设置一个辅助弹窗，本插件写死辅助弹窗为com.alibaba.uniplugin.android.third.push.ThirdPushPopupActivity，效果为用户点击通知之后拉起应用。
 
 iOS 推送通知
 iOS 推送 主要区分设备是否在前台
 1、在前台，通过setNotificationCallback注册的回调接口接收推送数据，通知不展示
 2、在后台或者应用未运行，通知直接展示，当用户点击通知时，拉起应用，通过setNotificationResponseCallback注册的回调接口接收推送数据；如果用户直接移除通知，应用无感知
 */

/**
 * 推送参数记录
 * 服务端参数
 * Title、Body、AndroidExtparameters全场景生效。
 * AndroidOpenType、AndroidActivity作用于在线设备和非小米、华为等厂商通道设备。
 * AndroidPopupTitle、AndroidPopupBody、AndroidPopupActivity仅作用于辅助通道。
 * ### 辅助通道送达的通知，通知中标题和内容是服务端设置的AndroidPopupTitle、AndroidPopupBody，点击通知唤起辅助弹窗AndroidPopupActivity，在onSysNoticeOpened中回调的是服务端设置的Title、Body、AndroidExtParameters
 * 
 * PHP推送字段
 * "appType" => "*********",
 * "pushType" => "MESSAGE",
   "deviceType" => "ANDROID",
   "target" => "DEVICE",
   "targetValue" => "daed4445e1314d63957457e2e260465f",
   "storeOffline" => true,
   "title" => "今天很冷是吧",
   "body" => "今天很冷",
   "androidNotifyType" => "BOTH",
   "androidRemind" => true,
   "androidNotificationHuaweiChannel" => "NORMAL",
   "androidMessageHuaweiCategory" => "ACCOUNT",
   "androidTargetUserType" => 1,
   "androidNotificationChannel" => "gong_zhuang_clue",
   "androidPopupActivity" => "com.alibaba.uniplugin.android.third.push.ThirdPushPopupActivity",
   "androidPopupTitle" => "测试title",
   "androidPopupBody" => "测试body",
   "androidExtParameters" => "{\"key1\":\"value1\",\"event\":\"onThirdNotificationOpened\"}",
   "androidNotificationHonorChannel" => "NORMAL",
   "androidMessageVivoCategory" => "MARKETING"
 * 
 */


// #ifdef APP-PLUS
import $store from '@/store';
import $conf from '@/common/config.js';
import {
	onJump
} from '@/common/route.js';
import {
	openMiniWx,
	iWxMiniProgram
} from '@/common/utils.js'
import {
	judgeLogin
} from '@/common/login.js'
import $cons from '@/common/constant.js'
const channel = uni.requireNativePlugin('Aliyun-Push-NotificationChannel');
const aliyunPush = uni.requireNativePlugin('Aliyun-Push');
const aliyunThirdPush = uni.requireNativePlugin('Aliyun-ThirdPush');

// 安卓推送通道标识  
export const androidNoticeChannelId = 'kjdzb'

const aliyunPushHelper = {
	/**
	 * 推送初始化
	 */
	init() {
		// 开启日志
		aliyunPushHelper.registerLog()

		if (plus.os.name == 'Android') {
			// 1、通知通道创建 (根据需要创建不同的NotificationChannel)
			// Android 8.0开始，通知展示需要设置NotificationChannel，所以需要先创建NotificationChannel
			channel.createChannel({
				id: androidNoticeChannelId,
				name: '线索消息通知',
				desc: '新线索、预约线索、推送线索消息提醒',
				importance: 3,
				vibration: true
			});
			// 2、注册推送
			// 注意：注册推送的回调，同时负责接收推送相关的数据
			aliyunPush.registerPush({}, result => {
				const event = result.event;
				if (event === 'registerPush') {
					if (result.code === 'success') {
						console.warn("***********注册推送 成功 ***********");
					} else {
						console.warn("***********注册推送 ***********" + result.code + " " + result.msg);
					}
				} else {
					const data = JSON.stringify(result.data);
					console.warn("***********接受Push事件***********: " + event);
					console.warn("***********接受Push数据***********: " + data);
				}
				aliyunPushHelper.handleResult("registerPush", result);
			});
			// 3、注册厂商通道
			// 注册之后，才可以接收到厂商通道推送的数据。注意：厂商通道是厂商控制的，所以此回调只有在用户点击通知之后才能触发。 用户未点击或者直接移除，应用是无法感知的。
			aliyunThirdPush.registerThirdPush({}, result => {
				const data = JSON.stringify(result);
				console.warn("***********注册厂商通道推送***********: " + data);
				const newResult = {
					...result,
					event: "onThirdNotificationOpened"
				};
				aliyunPushHelper.handleResult("registerThirdPush", newResult);

			});
		} else if (plus.os.name == 'iOS') {
			// iOS的初始化默认在应用启动时执行，不需要特别调用初始化方法，但是需要注册一些回调接口，用于接收推送数据

			// * 设置iOS侧，应用在前台时，是否展示推送通知，默认false不展示，通过setNotificationCallback注册的回调接收推送通知数据。
			// * 当设置为true时，应用在前台时，推送通知，不会回调setNotificationCallback注册的回调接口，而是展示通知，当用户点击通知时，会回调setNotificationResponseCallback注册的回调接口
			aliyunPush.showNoticeWhenForeground({
				enable: true
			});

			// 1、注册前台推送通知的回调接口
			aliyunPush.setNotificationCallback({}, result => {
				aliyunPushHelper.handleIOSResult("NotificationCallback", result);
			});
			// 2、注册用户点击推送通知的回调接口
			aliyunPush.setNotificationResponseCallback({}, result => {
				aliyunPushHelper.handleIOSResult("NotificationResponseCallback", result);
			});
			// 2、注册接收推送消息的回调接口
			aliyunPush.setMessageCallback({}, result => {
				aliyunPushHelper.handleIOSResult("MessageCallback", result);
			});
		}
	},

	/**
	 * 打开日志功能
	 * 开发测试过程中排查问题
	 */
	registerLog() {
		if (plus.os.name == 'Android') {
			aliyunPush.registerLog({}, result => {
				console.warn("***********注册日志***********: : " + result);
			});
			aliyunThirdPush.registerLog({}, result => {
				console.warn("***********注册厂商通道日志***********: " + result);
			})
		} else if (plus.os.name == 'iOS') {
			aliyunPush.turnOnDebug({});
		}
	},

	/**
	 * Android & IOS
	 * 推送初始化之后，可以通过api获取设备ID，需要将设备ID发给业务方服务器，方便后端服务通过设备ID进行推送
	 */
	getDeviceId() {
		const result = aliyunPush.getDeviceId();
		return result.data.deviceId
	},

	/**
	 * 判断通知通道是否开启
	 * IOS:
	 * 
	 *
	 * Androkd:
	 * 判断通知通道是否开启。由于用户可以在设置中管理应用的通知通道，包括关闭通知通道，我们需要判断，并在合适的时机提示用户
	 * channelId为NotificationChannel的ID，可以不传，当不传时，判断的是应用通知的总开关是否开启。有channelId时，判断的是对应NotificationChannel是否开启。
	 */
	isNotificationEnabled(channelId) {
		if (plus.os.name == 'Android') {
			let params = {}
			if (channelId != null && channel != undefined && channelId != '') {
				params.id = channelId
			}
			const result = channel.isNotificationEnabled(params);
			if (result.code == 'success') {
				return result.data
			}
			return false
		} else if (plus.os.name == 'iOS') {

		}
		return false
	},

	/**
	 * 跳转到通知的设置界面，用于在需要用户修改通知状态时使用。
	 * IOS :
	 *
	 *
	 * Android:
	 * channelId为NotificationChannel的ID，可以不传，当不传时，跳转应用通知管理界面。有channelId时，跳转对应NotificationChannel的管理界面。
	 */
	goNotificationSettings(channelId) {
		if (plus.os.name == 'Android') {
			channel.goNotificationSettings({
				id: channelId,
			});
		} else if (plus.os.name == 'iOS') {

		}
	},

	/**
	 * ********************************************************************************
	 * 处理业务逻辑
	 * ********************************************************************************
	 * IOS
	 * 分为推送通知 | 推送消息
	 * 
	 * Android:
	 * 分为推送通知 | 推送消息
	 * 推送通知：在通知栏有横幅并且可以携带额外参数. 对应Event【onNotification，onNotificationOpened】
	 * 推送消息：在通知栏没有提示并且不可以携带额外参数,只是一个消息提示作用. 对应Event【onMessage】消息可以设置保存离线保存时间，进入app后回接受到该消息
	 */

	handleIOSResult(label, result) {
		console.log("推送----【推送】----【handleIOSResult】", label, result)
		switch (label) {
			case 'NotificationCallback':
				// 接收推送通知的回调
				break
			case 'NotificationResponseCallback':
				// 推送通知被用户点击时的回调
				let params = result.data.userInfo
				aliyunPushHelper.jumpPage(params)
				break
			case 'MessageCallback':
				// 接收推送消息的回调
				let obj = JSON.parse(result.data.content)
				// 应用内部弹窗
				// #ifdef APP-NVUE
				getCurrentPages()[0].$vm.$store.commit('setNotifyPopup', {
					show: true,
					...obj
				});
				// #endif
				// #ifndef APP-NVUE
				$store.commit('setNotifyPopup', {
					show: true,
					...obj
				});
				// #endif
				break
		}
	},

	/**
	 * ********************************************************************************
	 * 处理业务逻辑
	 * ********************************************************************************
	 * Android:
	 * 分为推送通知 | 推送消息
	 * 推送通知：在通知栏有横幅并且可以携带额外参数. 对应Event【onNotification，onNotificationOpened】
	 * 推送消息：在通知栏没有提示并且不可以携带额外参数,只是一个消息提示作用. 对应Event【onMessage】消息可以设置保存离线保存时间，进入app后回接受到该消息
	 */
	handleResult(label, result, action) {
		console.log("推送----【推送】----【handleResult】", label, result)
		switch (result.event) {
			// 接受到消息
			case 'onMessage': {
				let obj = JSON.parse(result.data.content)
				console.log("接受到消息", obj)
				// 应用内部弹窗
				// #ifdef APP-NVUE
				getCurrentPages()[0].$vm.$store.commit('setNotifyPopup', {
					show: true,
					...obj
				});
				// #endif
				// #ifndef APP-NVUE
				$store.commit('setNotifyPopup', {
					show: true,
					...obj
				});
				// #endif
			}
			break
			// 接受到通知
			case 'onNotification': {
				console.log("接受到通知", result.data.extra)
				// 设置APP角标 【仅仅小米|华为设备有效】
				plus.runtime.setBadgeNumber(1);

				// 应用内部弹窗
				// #ifdef APP-NVUE
				getCurrentPages()[0].$vm.$store.commit('setNotifyPopup', {
					show: true,
					...result.data.extra
				});
				// #endif
				// #ifndef APP-NVUE
				$store.commit('setNotifyPopup', {
					show: true,
					...result.data.extra
				});
				// #endif
			}
			break
			// 点击离线推送的通知【厂商通道通知】
			case 'onThirdNotificationOpened': {
				aliyunPushHelper.jumpPage(result)
			}
			break
			// 点击通知栏通知
			case 'onNotificationOpened': {
				console.log("点击通知数据====", result.data.extraStr)
				let params = JSON.parse(result.data.extraStr);
				aliyunPushHelper.jumpPage(params)
			}
			break
		}
	},

	/**
	 * 根据消息参数跳转界面
	 * 
	 * @param {Object} params
	 */
	jumpPage(params) {
		switch (params.type) {
			case 'order_acc': // 师傅已接单
			case 'visit_conf': // 师傅确认上门
			case 'svc_comp': // 师傅确认完成
			case 'refund_end': // 退单结束
			case 'return_req': //申请退回大厅
				onJump({
					url: '/pagesOrder/order/order-detail',
					params: {
						id: params?.product_id
					}
				});
				break;
			case 'follow_pub':
				// 关注公众号
				judgeLogin(() => {
					openMiniWx(iWxMiniProgram('oa', $conf.miniProgramId));
				});
				break;
		}
	}
};

export default aliyunPushHelper;
// #endif