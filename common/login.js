import $store from '@/store';
import $cons from '@/common/constant.js'
// #ifdef APP-PLUS
import loginUiConfig from '@/common/aliLoginUI.js'
const aLiSDKModule = uni.requireNativePlugin('AliCloud-NirvanaPns');
// #endif

// 登录锁  防止多次调起一键登录弹窗
var appLoginLock = false

// APP号码认证初始化
function setupLogin() {
	// #ifdef APP-PLUS
	let secret = '';
	if (plus.os.name == 'Android') {
		//开启SDK日志打印
		aLiSDKModule.setLoggerEnable(true);
		aLiSDKModule.expandAuthPageCheckedScope(true);
		//禁用物理返回键
		//aLiSDKModule.closeAuthPageReturnBack(true);
		//开启区分界面返回及物理返回功能，自动控制后续返回事件
		aLiSDKModule.userControlAuthPageCancel();
		//是否跟随系统深色模式
		aLiSDKModule.setAuthPageUseDayLight(false);
		secret =
			'5m0Qw255Nevf7liOISXgmUo0oHnFdcsCRMpjpJr30h8PtQ6tWU4359XzCfxEk0WqsD5VK44NnTF+nsUqdYIJIdWj3/8Oac7110fi5F3Pd17vIlkSHegNjCu/oZ6nIEmuneH4ghvt7/xryAVhRPNmk79DJs0KRXaYNbyYGYbH/R5YXIUOm0l7jJKaTX/l1OUX9/wXLR/DEpXwvBPHtRe88nsMfFlcWppHYj7/vfN156qeN+bCWlKpqN/LxxlEGAuI0/XJ2t+15YZUJrp86asXb1+1T+638U/TQKvFVK1vfudk76YAEAmChw=='
	} else if (plus.os.name == 'iOS') {
		secret =
			'j3/Cm+KLio9oxtlMmPI0Ava5K0JwHX3xW3ftv4mJJOevWGdazRKVcgkKpzlSSOJTBeTcakcP1eoxd+Zm8umfm9fAr9+2yU8x9dWI32jzL8fyfzTa1N9SVS05iG45OLPEXsZ6j0Mt0s5jch5mxRNlmZkEXbmac/EFcVsSFSz9in7ryVswibRUByDw7BXdRVBnbKuesCQ03DpNu4/+/Y1Mfb62eKREs1V4e+kmm81I1hCpqLLmo7fWn33SmhQ7pIe/OLwkOUfIyoE='
	}
	aLiSDKModule.setAuthSDKInfo(secret);

	aLiSDKModule.accelerateLoginPage(5000, result => {
		if ("600000" == result.resultCode) {
			console.log("阿里号码认证加速成功")
		}
	})
	// #endif
}

// 通用登录
function loginPopup(callback) {
	// #ifdef APP-PLUS
	aLiSDKModule.quitLoginPage();
	// #endif
	// 注意：直接使用$store.commit('SET_LOGIN_POPUP', {})会导致在NVUE页面中无法监听到设置的数据
	// #ifdef APP-NVUE
	getCurrentPages()[0].$vm.$store.commit('SET_LOGIN_POPUP', {
		show: true,
		callback: callback
	});
	// #endif
	// #ifndef APP-NVUE
	$store.commit('SET_LOGIN_POPUP', {
		show: true,
		callback: callback
	});
	// #endif
}

// APP一键登录
function appLogin(callback) {
	if (appLoginLock) {
		return;
	}
	// 打开一键登录弹窗之前关闭验证码/密码登录弹窗
	// #ifdef APP-NVUE
	getCurrentPages()[0].$vm.$store.commit('SET_LOGIN_POPUP', {
		show: false,
		callback: null
	});
	// #endif
	// #ifndef APP-NVUE
	$store.commit('SET_LOGIN_POPUP', {
		show: false,
		callback: null
	});
	// #endif

	// 添加登录锁
	appLoginLock = true
	uni.showLoading({
		title: "登录授权中.",
		mask: true
	});
	const config = loginUiConfig.buildSheet();
	aLiSDKModule.getLoginToken(
		5000,
		config,
		tokenResult => {
			uni.hideLoading();
			console.log(JSON.stringify(tokenResult));
			if ("600001" == tokenResult.resultCode) {
				console.log("授权页拉起成功");
			} else if ("600000" == tokenResult.resultCode) {
				console.log("获取Token成功");
				aLiSDKModule.hideLoginLoading()
				uni.showLoading({title: "登录中.",mask: true});
				uni.$u.http.post('/Alilogin/AlibabaLogin', {
					AccessToken: encodeURIComponent(tokenResult.token)
				}).then(res => {
					let mstore = $store
					// #ifdef APP-NVUE
					mstore = getCurrentPages()[0].$vm.$store
					// #endif
					mstore.commit('SET_TOKEN', res);
					// 更新用户配置信息
					mstore.dispatch('updateUserProfile')
					// #ifdef APP-PLUS
					// 注册设备编号
					mstore.dispatch('registerPushDevice')
					// #endif
					// 协议状态提交服务器
					const protocols = [$cons.PROTOCOL_USER, $cons.PROTOCOL_PRIVACY]
					mstore.dispatch('agreementProtocol', encodeURIComponent(protocols.join(',')))
					// 查询用户信息
					mstore.dispatch('fetchUser').then(userRes => {
						uni.hideLoading()
						// 登录成功回调
						callback && callback();
						// 手动关闭授权页
						aLiSDKModule.quitLoginPage();
						appLoginLock = false
					}).catch(userErr => {
						uni.hideLoading()
						// 手动关闭授权页
						aLiSDKModule.quitLoginPage();
						appLoginLock = false
					})
				}).catch(err => {
					uni.hideLoading()
					//手动关闭授权页
					aLiSDKModule.quitLoginPage();
					appLoginLock = false
				})
			} else {
				loginPopup(callback);
				appLoginLock = false
			}
		},
		clickResult => {
			console.log(JSON.stringify(clickResult));
			switch (clickResult.resultCode) {
				case "700000":
					/* 用户点击返回按钮 */
					appLoginLock = false
					break
				case "700001":
					/* 用户切换其他登录方式 */
					loginPopup(callback);
					appLoginLock = false
					break
				case "700002":
					/* 用户点击登录按钮 */
					break
				case "700003":
					/* 用户点击checkBox */
					break
				case "700004":
					/* 用户点击协议 */
					break
				case "700006":
					/* 点击一键登录拉起授权页二次弹窗 */
					break
				case "700007":
					/* 隐私协议二次弹窗关闭 */
					break
				case "700008":
					/* 隐私协议二次弹窗点击确认并继续 */
					aLiSDKModule.quitLoginPage();
					appLoginLock = false
					break
				case "700009":
					/* 点击隐私协议二次弹窗上的协议富文本文字 */
					break
				case "700010":
					/* 用户点击返回按钮，Android专用 */
					/* 调用userControlAuthPageCancel后方可使用 */
					aLiSDKModule.quitLoginPage();
					appLoginLock = false
					break
				case "700011":
					/* 用户使用物理返回键，Android专用 */
					/* 调用userControlAuthPageCancel后方可使用 */
					aLiSDKModule.quitLoginPage();
					appLoginLock = false
					break
				default:
					loginPopup(callback);
					appLoginLock = false
					break
			}
		},
		customUiResult => {
			console.log(JSON.stringify(customUiResult));
			if ("close" == customUiResult.widgetId) {
				//点击了自定义的关闭授权页按钮
				aLiSDKModule.quitLoginPage();
				appLoginLock = false
			}
		});
}


//判断是否登录（所有端）
function judgeLogin(callback) {
	let accessToken = $store.state.token?.accessToken;
	// #ifdef APP-NVUE
	accessToken == getCurrentPages()[0].$vm.$store.state.token?.accessToken;
	// #endif
	if (accessToken) {
		callback && callback();
	} else {
		// 注意：直接使用$store.commit('SET_LOGIN_POPUP', {})会导致在NVUE页面中无法坚挺到设置的数据
		// #ifdef APP-NVUE
		getCurrentPages()[0].$vm.$store.commit('SET_CURRENT_ROUTER', getCurrentRouter());
		// #endif
		// #ifndef APP-NVUE
		$store.commit('SET_CURRENT_ROUTER', getCurrentRouter());
		// #endif

		// #ifdef MP
		loginPopup(callback);
		// #endif
		// #ifdef APP-PLUS
		appLogin(callback);
		// #endif
		// #ifdef H5
		// 不支持
		// #endif
	}
}

// 当前页面是否是登录页面
function isCurrentLoginPage() {
	let pages = getCurrentPages();
	let routeName = pages[pages.length - 1].route;
	if (routeName == 'pages/login/login') {
		return true;
	}
	return false;
}

// 获取当前路径
function getCurrentRouter(callback) {
	let routes = getCurrentPages() // 获取当前打开过的页面路由数组
	let curRoute = routes[routes.length - 1].route
	return curRoute
}

export {
	getCurrentRouter,
	setupLogin,
	judgeLogin
}