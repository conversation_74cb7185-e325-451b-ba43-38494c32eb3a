// core/network/web-socket-service-optimized.js
// =============================================================
// 企业级 WebSocket 服务封装（UniApp 版本）
// =============================================================
// 说明：
//   1. 本文件为单例实现，可直接 import { webSocketService } 使用。
//   2. 采用 ES2022 私有字段（#）语法，需确保构建链支持。
//   3. 所有注释均为中文，涵盖文件头、类、字段、方法以及关键逻辑行。
// =============================================================

import defaultConfig from '@/common/config.js'; // 项目中的全局配置（需包含 socketUrl）
import chatCons from '@/pagesChat/utils/constants.js';
import store from '@/store';

/**
 * 日志输出包装器
 * - 生产环境过滤 debug 日志
 * @param {'error'|'warn'|'info'|'debug'} level 日志级别
 * @param {string} msg 日志内容
 * @param {object} [ctx] 额外上下文
 */
function logger(level = 'info', msg = '', ctx = {}) {
	if (process.env.NODE_ENV === 'production' && level === 'debug') return;
	console[level](`[WS] ${msg}`, ctx);
}

/**
 * Full‑Jitter 指数退避算法
 * 参考 AWS Architecture Blog《Exponential Backoff And Jitter》
 * @param {number} attempt 当前重连尝试次数（1起）
 * @param {number} base 基础延迟(ms)
 * @param {number} cap 最大延迟上限(ms)
 * @returns {number} 延迟时间(ms)
 */
function backoff(attempt, base, cap) {
	const exp = Math.min(cap, base * 2 ** (attempt - 1));
	return Math.random() * exp;
}

const noop = () => {}; // 空函数，避免参数未传造成异常

/**
 * WebSocketService
 * - 封装 UniApp WebSocket 连接、重连、心跳、消息路由等功能
 * - 使用私有字段防止外部直接访问
 * - 支持离线消息队列和通配符消息处理
 */
export class WebSocketService {
	// 默认配置，可通过构造函数参数覆盖
	static DEFAULT_OPTIONS = {
		heartbeatInterval: 30000, // 心跳间隔(ms)
		heartbeatTimeoutRate: 0.8, // 心跳超时比例，0.8表示80%
		reconnectBaseDelay: 3000, // 重连基础延迟(ms)
		reconnectMaxDelay: 60000, // 最大重连延迟(ms)
		maxConsecutiveErrors: 5 // 最大连续JSON解析错误数
	};

	// 私有字段
	#opts; // 最终生效配置
	#socketTask = null; // uni.connectSocket 返回的 SocketTask 实例
	#state = 'DISCONNECTED'; // 连接状态（DISCONNECTED, CONNECTING, CONNECTED, DISCONNECTING, RECONNECTING）
	#isManualClose = false; // 是否主动关闭连接
	#reconnectAttempts = 0; // 当前重连次数

	// 各类计时器句柄（需清除且置 null 防止内存泄漏）
	#heartbeatTimer = null; // 定时发送心跳包（ping）
	#heartbeatTimeoutTimer = null; // 心跳超时定时器，收到pong后需清除
	#reconnectTimer = null; // 重连延时定时器

	#messageHandlers = new Map(); // Map<消息类型, Set<处理函数>>
	#pendingQueue = []; // 离线时积压的消息队列，连接恢复后按序发送
	#consecutiveParseErrors = 0; // 连续JSON解析失败计数，超限触发强制重连

	#sessionId = `ws_${Date.now().toString(36).slice(-6)}`; // 唯一会话ID，用于日志关联

	/**
	 * 构造函数
	 * @param {Partial<typeof WebSocketService.DEFAULT_OPTIONS>} options 自定义配置
	 */
	constructor(options = {}) {
		this.#opts = {
			url: defaultConfig.socketUrl,
			...WebSocketService.DEFAULT_OPTIONS,
			...options
		};
		logger('info', 'WebSocketService 实例创建', {
			sessionId: this.#sessionId,
			url: this.#opts.url
		});
		this.#bindNetworkListener();
	}

	/**
	 * 更新 Token 并处理连接状态
	 * - token 为空时强制断开
	 * - token 变化时重新连接
	 */
	updateToken(token) {
		const oldToken = this.#opts.url?.split('?token=').pop();
		const newToken = token || '';

		if (oldToken === newToken) return;

		this.#opts.url = `${defaultConfig.socketUrl}?token=${newToken}`;

		if (!newToken) {
			this.disconnect();
			logger('info', 'Token 清除，主动断开连接');
		} else if (this.#state !== 'CONNECTED') {
			this.connect();
		} else {
			// 已连接但 token 变化，需要重新连接
			this.disconnect();
			this.#scheduleReconnect(0); // 立即重连
		}
	}

	/**
	 * 建立连接
	 * - 若当前非断开状态，直接返回，避免重复连接
	 */
	connect() {
		if (this.#state !== 'DISCONNECTED') return;
		this.#isManualClose = false;
		this.#openConnection();
	}

	/**
	 * 主动断开连接
	 * - 停止心跳、重连
	 * - 不会自动重连，需调用 connect 重新连接
	 */
	disconnect() {
		if (this.#state === 'DISCONNECTED') return;
		this.#isManualClose = true;
		this.#state = 'DISCONNECTING';
		this.#clearTimers();
		this.#socketTask?.close({
			reason: 'client manual close'
		});
	}

	/**
	 * 发送消息
	 * - 支持字符串或可序列化对象
	 * - 连接未就绪时入队，连接后顺序发送
	 * @param {string|object} payload 消息数据
	 */
	send(payload) {
		const data = typeof payload === 'string' ? payload : JSON.stringify(payload);

		if (this.#state !== 'CONNECTED') {
			this.#pendingQueue.push(data);
			logger('warn', '连接未就绪，消息入队', {
				queueSize: this.#pendingQueue.length
			});
			return;
		}

		this.#socketTask.send({
			data,
			fail: err => {
				logger('error', '发送失败，消息回滚入队', {
					errMsg: err.errMsg
				});
				this.#pendingQueue.unshift(data); // 发送失败，放回队头优先重发
			}
		});
	}

	/**
	 * 订阅消息或事件
	 * - 消息类型支持通配符 '*' 订阅所有消息
	 * - 事件类型用 $ 前缀，常见如 $connected、$disconnected、$reconnecting
	 * @param {string} type 消息或事件类型
	 * @param {Function} handler 事件处理函数
	 * @returns {Function} 取消订阅函数
	 */
	on(type, handler = noop) {
		if (!this.#messageHandlers.has(type)) {
			this.#messageHandlers.set(type, new Set());
		}
		this.#messageHandlers.get(type).add(handler);
		return () => this.off(type, handler);
	}

	/**
	 * 取消订阅
	 * @param {string} type 消息或事件类型
	 * @param {Function} handler 事件处理函数
	 */
	off(type, handler) {
		this.#messageHandlers.get(type)?.delete(handler);
	}

	// 便捷方法：生命周期事件快捷订阅
	onConnected(cb) {
		return this.on('$connected', cb);
	}
	onDisconnected(cb) {
		return this.on('$disconnected', cb);
	}
	onReconnecting(cb) {
		return this.on('$reconnecting', cb);
	}

	// ---------------- 私有方法 ----------------

	/**
	 * 监听系统网络状态变化，网络恢复时自动触发重连
	 */
	#bindNetworkListener() {
		uni.onNetworkStatusChange(res => {
			logger('info', '网络状态变化', res);
			if (res.isConnected && this.#state !== 'CONNECTED') {
				this.#scheduleReconnect(0);
			}
		});
	}

	/**
	 * 打开 WebSocket 连接，绑定各类事件
	 */
	#openConnection() {
		const {
			url
		} = this.#opts;
		logger('info', '开始建立连接', {
			url,
			attempt: this.#reconnectAttempts + 1
		});

		this.#state = 'CONNECTING';
		this.#emit('$connecting');

		const startAt = Date.now();
		this.#socketTask = uni.connectSocket({
			url: url,
			complete: () => {},
			success: () => {},
		});

		this.#socketTask.onOpen(() => {
			logger('info', '连接已建立', {
				duration: Date.now() - startAt
			});
			this.#state = 'CONNECTED';
			this.#reconnectAttempts = 0;
			clearTimeout(this.#reconnectTimer);
			this.#reconnectTimer = null;
			this.#flushPendingQueue();
			this.#startHeartbeat();
			this.#emit('$connected');
		});

		this.#socketTask.onMessage(({
			data
		}) => this.#handleMessage(data));

		const onCloseOrError = evt => {
			logger('warn', '连接关闭或错误', evt);
			this.#cleanupAfterClose();
		};

		this.#socketTask.onClose(onCloseOrError);
		this.#socketTask.onError(onCloseOrError);
	}

	/**
	 * 处理收到的消息
	 * - 支持 string 和 ArrayBuffer
	 * - 处理心跳 pong，JSON 解析及路由
	 * @param {string|ArrayBuffer} raw 原始数据
	 */
	#handleMessage(raw) {
		// 1. 处理心跳响应 'pong'
		if (raw === 'pong') {
			if (this.#heartbeatTimeoutTimer) {
				clearTimeout(this.#heartbeatTimeoutTimer);
				this.#heartbeatTimeoutTimer = null;
			}
			logger('debug', '收到 pong');
			this.#emit('$heartbeat_ack');
			return;
		}

		// 2. 如果是 ArrayBuffer，使用 TextDecoder 解码成字符串
		if (raw instanceof ArrayBuffer) {
			try {
				raw = new TextDecoder('utf-8').decode(raw);
			} catch (err) {
				logger('error', 'ArrayBuffer 解码失败', {
					errMsg: err.message
				});
				return;
			}
		}

		// 3. 非字符串直接忽略
		if (typeof raw !== 'string') {
			logger('warn', '未知数据类型已忽略', {
				dataType: typeof raw
			});
			return;
		}
		console.warn("接收到的websocket", raw)
		// 4. JSON 解析
		try {
			const msg = JSON.parse(raw);
			// 重置解析异常计数
			this.#consecutiveParseErrors = 0;

			logger('debug', '收到消息并解析成功', {
				消息类型: msg.type,
				消息内容: JSON.stringify(msg).slice(0, 200) // 截断部分内容，防止日志太长
			});

			if (msg.type == chatCons.WEBSOCKET_MESSAGE_TYPE_ENUM.IM_MESSAGE_RECEIVE) {
				const msgData = JSON.parse(msg.content)
				console.warn("接收到的websocket【msgData】", msgData)
				// 暂不处理自己发送的消息
				if (msgData.senderId === store.state.userInfo?.id) {
					return
				}
				if (msgData.contentType === chatCons.CONTENT_TYPE.TEXT ||
					msgData.contentType === chatCons.CONTENT_TYPE.FAQ_QUESTION) { 
					const localTextMessage = {
						clientMessageId: msgData.clientMessageId,
						avatar: msgData.senderAvatar,
						nickname: msgData.senderNickname,
						createTime: new Date().getTime(), // TODO: 是否合理
						isRead: false,
						content: msgData.content,
						contentType: msgData.contentType,
						sendStatus: chatCons.SEND_STATUS.SUCCESS,
						sendTime: msgData.sendTime,
						conversationId: '',
						senderId: msgData.senderId,
						receiverId: msgData.receiverId,
						conversationType: msgData.conversationType,
						conversationUserId: '',
						sequence: msgData.sequence
					};
					console.warn("接收到的websocket【localTextMessage】", localTextMessage)
					store.dispatch('receiveMessage', localTextMessage)
				} else if (msgData.contentType === chatCons.CONTENT_TYPE.IMAGE) {

				} else if (msgData.contentType === chatCons.CONTENT_TYPE.AUDIO) {

				}
			} else if (msg.type == chatCons.WEBSOCKET_MESSAGE_TYPE_ENUM.IM_CONVERSATION_ADD) {
				const conversation = JSON.parse(msgData.content)
				store.dispatch('receiveConversation', conversation)
				// 同步到内存
				store.getConversationList()
			} else {
				// TODO
			}


			// 精准匹配与通配符 * 的消息处理器合并
			const handlers = new Set([
				...(this.#messageHandlers.get(msg.type) || []),
				...(this.#messageHandlers.get('*') || [])
			]);

			// 调用所有匹配的消息处理器
			handlers.forEach(handler => {
				try {
					handler(msg);
				} catch (e) {
					logger('error', '消息处理器执行异常', {
						errMsg: e.message
					});
				}
			});
		} catch (err) {
			console.error('JSON 解析异常', err);
			logger('error', 'JSON 解析异常', {
				errMsg: err.message
			});
			if (++this.#consecutiveParseErrors >= this.#opts.maxConsecutiveErrors) {
				logger('warn', '连续解析异常超限，强制重连');
				this.#cleanupAfterClose();
			}
		}
	}

	/**
	 * 启动心跳检测
	 * - 定时发送 ping
	 * - 设定超时检测，若超时则关闭连接，触发重连
	 */
	#startHeartbeat() {
		this.#stopHeartbeat();

		const {
			heartbeatInterval,
			heartbeatTimeoutRate
		} = this.#opts;

		// 定时发送 ping
		this.#heartbeatTimer = setInterval(() => {
			if (this.#state !== 'CONNECTED') return;
			logger('debug', '发送心跳包');
			this.send('ping');

			// 清除上次的超时定时器（若存在）
			if (this.#heartbeatTimeoutTimer) {
				clearTimeout(this.#heartbeatTimeoutTimer);
				this.#heartbeatTimeoutTimer = null;
			}

			// 设置当前心跳超时定时器
			this.#heartbeatTimeoutTimer = setTimeout(() => {
				if (this.#state === 'CONNECTED') {
					logger('warn', '心跳超时，主动关闭连接');
					this.#socketTask.close();
				}
			}, heartbeatInterval * heartbeatTimeoutRate);
		}, heartbeatInterval);
	}

	/**
	 * 停止心跳检测，清理计时器
	 */
	#stopHeartbeat() {
		if (this.#heartbeatTimer) {
			clearInterval(this.#heartbeatTimer);
			this.#heartbeatTimer = null;
		}
		if (this.#heartbeatTimeoutTimer) {
			clearTimeout(this.#heartbeatTimeoutTimer);
			this.#heartbeatTimeoutTimer = null;
		}
	}

	/**
	 * 发送离线消息队列中的消息，FIFO 顺序
	 */
	#flushPendingQueue() {
		if (this.#pendingQueue.length === 0) return;
		logger('info', `开始发送离线队列消息，共 ${this.#pendingQueue.length} 条`);
		while (this.#pendingQueue.length > 0) {
			const data = this.#pendingQueue.shift();
			this.send(data);
		}
	}

	/**
	 * 清理关闭连接后的资源
	 * - 停止心跳
	 * - 清除重连计时器
	 * - 修改连接状态为断开
	 * - 触发断开事件
	 * - 自动重连（非主动关闭时）
	 */
	#cleanupAfterClose() {
		this.#stopHeartbeat();
		if (this.#reconnectTimer) {
			clearTimeout(this.#reconnectTimer);
			this.#reconnectTimer = null;
		}

		if (this.#state !== 'DISCONNECTING') {
			this.#state = 'DISCONNECTED';
			this.#emit('$disconnected');
		}

		if (!this.#isManualClose) {
			this.#scheduleReconnect();
		}
	}

	/**
	 * 计划重连
	 * - 支持延迟参数
	 * - 指数退避算法带随机抖动
	 * - 超过最大次数后放弃重连
	 * @param {number} [delay] 延迟(ms)，默认计算
	 */
	#scheduleReconnect(delay) {
		if (this.#isManualClose) {
			logger('debug', '手动关闭，跳过重连');
			return;
		}

		if (this.#reconnectAttempts >= this.#opts.maxConsecutiveErrors) {
			logger('warn', '超过最大重连次数，放弃重连');
			return;
		}

		if (delay === undefined) {
			delay = backoff(this.#reconnectAttempts + 1, this.#opts.reconnectBaseDelay, this.#opts
				.reconnectMaxDelay);
		}

		logger('info', '计划重连', {
			attempt: this.#reconnectAttempts + 1,
			delay
		});

		this.#state = 'RECONNECTING';
		this.#emit('$reconnecting', {
			attempt: this.#reconnectAttempts + 1,
			delay
		});

		clearTimeout(this.#reconnectTimer);
		this.#reconnectTimer = setTimeout(() => {
			this.#reconnectAttempts++;
			this.#openConnection();
		}, delay);
	}

	/**
	 * 触发事件通知
	 * - 只触发以 $ 开头的事件
	 * @param {string} eventType 事件类型
	 * @param {any} [payload] 事件数据
	 */
	#emit(eventType, payload) {
		const handlers = this.#messageHandlers.get(eventType) || new Set();
		handlers.forEach(handler => {
			try {
				handler(payload);
			} catch (e) {
				logger('error', '事件处理器异常', {
					eventType,
					errMsg: e.message
				});
			}
		});
	}

	/**
	 * 清理所有计时器（断开时调用）
	 */
	#clearTimers() {
		this.#stopHeartbeat();

		if (this.#reconnectTimer) {
			clearTimeout(this.#reconnectTimer);
			this.#reconnectTimer = null;
		}
	}
}

const setupTokenWatch = (wsInstance) => {
	store.watch(
		state => state.token?.accessToken,
		(token) => {
			console.log("Websocket-RefershToken发成了改变")
			wsInstance.updateToken(token?.trim())
		}, {
			immediate: true
		}
	);
}

// 单例导出，
// export const webSocketService = new WebSocketService();
export const webSocketService = (() => {
	const instance = new WebSocketService();
	setupTokenWatch(instance); // 单独初始化监听
	return instance;
})();