// 签名校验
// #ifdef APP-PLUS
export const signVerify = function(tip) {
	// 签名证书检验
	var sign = plus.navigator.getSignature();
	if (plus.os.name == 'Android') { //Android平台
		//自己应用签名证书SHA-1值，是全小写并且中间不包含“:”符号
		var sha1 = 'c7c6464cf8c782b5e85674e8d1323373ed70c9d9';
		if (sha1 != sign) {
			//证书不对时退出应用
			plus.runtime.quit();
			return false;
		}
	} else { //iOS平台
		//自己应用Apple Bunld ID(AppID)的md5值
		var md5 = 'bc10e3ef1239ed1958523f4067176bb4';
		if (md5 != sign) {
			//不进入应用或循环弹出提示框
			plus.runtime.launchApplication({
				action: 'QUIT'
			});
			return false;
		}
	}
	return true;
}

// 模拟器运行校验
export const simulatorVerify = function(tip) {
	// if (plus.navigator.isSimulator()) {
	// 	// 应用被不能运行到模拟器
	// 	plus.runtime.quit();
	// 	return false
	// }
	return true;
}
// #endif