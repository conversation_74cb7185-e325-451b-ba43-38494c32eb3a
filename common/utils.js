import store from '@/store';
import SysPermission from '@/common/sysPermission.js'
import cons from '@/common/constant.js'
import conf from '@/common/config.js'

/**
 * 获取服务端图片地址
 */
export const iImage = (name = '', packagex = '') => {
	// #ifdef APP-PLUS
	return `${!uni.$u.test.isEmpty(packagex) ? `/${packagex}` : ''}/static/image/${name}`
	// #endif

	// #ifdef MP
	return `${conf.imageUrl}/appassets/${conf.appIdent}/${name}`
	// #endif
}

/**
 * 组装协议地址
 */
export const iProtocolUrl = (type = '') => { 
	console.error("得到地址", `${conf.protocolUrl}${conf.basePath}/client/app-content/get/${conf.appIdent}/${cons.TERMINAL_TYPE()}/${type}`)
	return `${conf.protocolUrl}${conf.basePath}/client/app-content/get/${conf.appIdent}/${cons.TERMINAL_TYPE()}/${type}`;
}

/**
 * 客服微信小程序参数构建
 * type: 客服类型 kf- 接单咨询客服 jmkf-加盟客服 jskf-技术客服  oa-公众号
 * miniId: 小程序 id/原始 id
 * ext: 扩展参数
 */
export const iWxMiniProgram = (type = 'kf', miniId = '', ext = {}) => {
	const userInfo = store.state.userInfo;
	const address = store.state.selectedAddress;
	const pathParams = {
		type: conf.appIdent,
		module: type,
		uid: userInfo.id || '',
		city: address.cityId || '',
		...ext
	}
	const data = {
		miniId: miniId,
		path: `pages/index/index${uni.$u.queryParams(pathParams)}`
	}
	return data;
}


/**
 * 分享包含 APP 和 小程序
 */
export const iShare = (data) => {
	console.log("分享【传入参数】", data)
	uni.showLoading({
		title: '分享中..'
	})
	return new Promise((resolve, reject) => {
		uni.$u.http.post('/share/getShareUrl', {
			item_id: data?.itemId, // 分享的项目编号
			item_scene: data?.itemScene, // 分享场景
			path: encodeURIComponent(data?.path) //页面路由地址
		}).then(res => {
			uni.hideLoading()
			// APP 分享
			// #ifdef APP-PLUS
			uni.share({
				provider: 'weixin',
				scene: 'WXSceneSession',
				type: res?.type ?? 5, // 默认小程序
				href: res?.url || '',
				summary: res?.content || '',
				imageUrl: res?.image_url,
				title: res?.title,
				miniProgram: {
					id: res?.gh_program_id,
					path: res?.path,
					type: 0, // 0-正式版； 1-测试版； 2-体验版。 默认值为0。
					webUrl: conf.baseUrl
				},
				success: (shareRes) => {
					console.log("APP分享成功", JSON.stringify(shareRes));
					resolve(shareRes);
				},
				fail: (err) => {
					console.error("APP分享失败:" + JSON.stringify(err));
					uni.showToast({
						title: '分享失败',
						icon: 'none'
					})
					reject(err);
				}
			});
			// #endif

			// 小程序分享
			// #ifdef MP
			const shareInfo = {
				title: res?.title,
				image: res?.image_url,
				path: res?.path,
			}
			resolve(shareInfo);
			// #endif
		}).catch(err => {
			uni.hideLoading()
			reject(err);
		})
	});
}

/**
 * 选择图片
 */
export const selectImage = (callback, count = 1) => {
	// #ifdef APP-PLUS
	plus.nativeUI.actionSheet({
		cancel: '取消',
		buttons: [{
			title: '从相册选择'
		}, {
			title: '拍摄'
		}]
	}, function(e) {
		var options = {}
		if (e.index == 1) {
			options = {
				form: 'album',
				permission: 'album',
				permissionTitle: cons.kPermission_Tip_Camera_Title,
				permissionSubtitle: cons.kPermission_Tip_Camera_Text,
			}
		} else if (e.index == 2) {
			options = {
				form: 'camera',
				permission: 'camera',
				permissionTitle: cons.kPermission_Tip_Camera_Title,
				permissionSubtitle: cons.kPermission_Tip_Camera_Text,
			}
		}
		if (uni.$u.test.isEmpty(options)) {
			return
		}
		SysPermission.judgePermission(options.permission, options.permissionTitle,
			options.permissionSubtitle, (result) => {
				if (result == 1) {
					// 已授权
					uni.chooseImage({
						count: count,
						sizeType: ['compressed'],
						sourceType: [options.form],
						success: function(res) {
							callback && callback({
								filePaths: res.tempFilePaths
							})
						},
						fail(err) {
							console.warn("选择图片", err)
							if (err.errMsg == 'chooseImage:fail User cancelled') {} else {
								uni.showToast({
									title: "选择图片失败",
									icon: 'none'
								})
							}
						}
					});
				}
			});
	});
	// #endif

	// #ifdef MP
	uni.chooseImage({
		count: 1,
		sizeType: ['compressed'],
		sourceType: ['camera', 'album'],
		success: function(res) {
			let filePath = res.tempFilePaths[0];
			callback && callback({
				path: filePath
			})
		},
		fail(err) {
			console.warn("选择图片", err)
			if (err.errMsg == 'chooseImage:fail User cancelled') {} else {
				uni.showToast({
					title: "选择图片失败",
					icon: 'none'
				})
			}
		}
	});
	// #endif
}

// 拨打电话
export const callTel = (data, callback) => {
	if (uni.$u.test.isEmpty(data?.tel)) {
		uni.showToast({
			title: "拨打失败",
			icon: "none"
		})
		callback && callback(false);
		return;
	}
	// #ifdef APP-PLUS
	let platform = uni.getSystemInfoSync().platform;
	if (platform == 'android') {
		SysPermission.judgePermission('call', cons.SYS_PERMISSION_TIP_CALL_TEL_TITLE,
			cons.SYS_PERMISSION_TIP_CALL_TEL_TEXT, (result) => {
				if (result == 1) {
					// 已授权
					let call_mobile = data.tel.replace(/[\s-]/g, "");
					uni.makePhoneCall({
						phoneNumber: call_mobile,
						fail: (e) => {
							uni.showToast({
								title: "拨打失败",
								icon: "none"
							})
						},
						complete: (res) => {
							const isSuccess = res.errMsg == 'makePhoneCall:ok';
							callback && callback(state);
						}
					})
				} else {
					callback && callback(false);
				}
			});
	} else if (platform == 'ios') {
		let call_mobile = data.tel.replace(/[\s-]/g, "");
		uni.makePhoneCall({
			phoneNumber: call_mobile,
			fail: (e) => {
				uni.showToast({
					title: "拨打失败",
					icon: "none"
				})
			},
			complete: () => {
				const isSuccess = res.errMsg == 'makePhoneCall:ok';
				callback && callback(state);
			}
		})
	}
	// #endif

	// #ifdef MP
	let call_mobile = data.tel.replace(/-/g, "");
	uni.makePhoneCall({
		phoneNumber: call_mobile,
		fail: (e) => {
			uni.showToast({
				title: "拨打失败",
				icon: "none"
			})
		},
		complete: () => {
			const isSuccess = res.errMsg == 'makePhoneCall:ok';
			callback && callback(state);
		}
	})
	// #endif
}

/**
 * 复制微信并打开
 * @param {Object} data 微信号
 */
export const copyWechat = (data) => {
	if (uni.$u.test.isEmpty(data?.wx)) {
		uni.showToast({
			title: "复制失败",
			icon: "none"
		})
		return;
	}

	uni.setClipboardData({
		data: data?.wx,
		success: function() {
			uni.showToast({
				title: '微信号已复制成功',
				icon: 'none'
			});
			// #ifdef APP-PLUS
			plus.runtime.openURL('weixin://');
			// plus.runtime.openURL('weixin://dl/friend?weixinid=123456');
			// #endif
		}
	});
}

/**
 * 打开企业微信在线客服
 * @param {Object} data
 * @param {Object} callback
 */
export const openOnlinkKF = (data, callback) => {
	if (uni.$u.test.isEmpty(data)) {
		uni.showToast({
			title: "打开失败",
			icon: "none"
		})
		callback && callback(false);
		return;
	}
	// #ifdef APP-PLUS
	let sweixin = null
	plus.share.getServices(res => {
		sweixin = res.find(i => i.id === 'weixin')
		if (sweixin) {
			sweixin.openCustomerServiceChat({
				corpid: data.wxId,
				url: data.wxUrl,
			}, suc => {
				console.log("success", JSON.stringify(res))
			}, err => {
				console.log("error", JSON.stringify(err))
			})
			callback && callback(true);
		} else {
			plus.nativeUI.alert('当前环境不支持微信操作!')
			callback && callback(false);
		}
	}, function() {
		uni.showToast({
			title: "获取服务失败，不支持该操作。" + JSON.stringify(e),
			icon: 'error'
		})
		callback && callback(false);
	})
	// #endif

	// #ifdef MP
	wx.openCustomerServiceChat({
		extInfo: {
			url: data.wxUrl
		},
		corpId: data.wxId,
		success(res) {},
		fail(res) {
			uni.showToast({
				title: "打开失败",
				icon: 'error'
			})
		}
	})
	// #endif
}

/**
 * 打开微信小程序
 * @param {Object} data  {miniId：'',path:''}
 * @param {Object} callback
 */
export const openMiniWx = (data, callback) => {
	if (uni.$u.test.isEmpty(data)) {
		uni.showToast({
			title: "打开失败",
			icon: "none"
		})
		callback && callback(false);
		return;
	}
	// #ifdef APP-PLUS
	if (!uni.$u.test.isEmpty(data.url)) {
		// 通过url方式打开小程序
		uni.navigateTo({
			url: "/pages/other/webView?url=" + encodeURI(data.url)
		})
	} else {
		// 通过原生方式打开小程序
		plus.share.getServices(function(s) {
			let sweixin = null;
			for (let i = 0; i < s.length; i++) {
				if (s[i].id == 'weixin') {
					sweixin = s[i];
					break;
				}
			}
			//判断是否有微信
			if (sweixin) {
				let path = data.path;
				sweixin.launchMiniProgram({
					id: data.miniId,
					// 0-正式版； 1-测试版； 2-体验版。
					type: 0,
					path: path
				}, function() {}, function(e) {
					uni.showToast({
						title: '微信唤起失败，请检查是否有微信应用',
						icon: 'none'
					})
				})
				callback && callback(true);
			} else {
				uni.showToast({
					title: '微信唤起失败，请检查是否有微信应用',
					icon: 'none',
					duration: 3000
				})
				callback && callback(false);
			}
		}, function(e) {
			uni.showToast({
				title: '微信唤起失败，请重试',
				icon: 'none',
				duration: 3000
			})
			callback && callback(false);
		});
	}
	// #endif

	// #ifdef MP
	uni.navigateToMiniProgram({
		appId: data.miniId,
		path: data.path,
		fail(res) {
			uni.showToast({
				title: '打开失败',
				icon: 'none',
				duration: 3000
			})
		},
		success(res) {
			console.log("跳转成功", res);
		}
	})
	// #endif
}

/**
 * 打开外部app
 * @param {Object} data  {package：'',appUrl:''}
 * @param {Object} callback
 */
export const openOtherApp = (data, callback) => {
	if (uni.$u.test.isEmpty(data)) {
		uni.showToast({
			title: "打开失败",
			icon: "none"
		})
		callback && callback(false);
		return;
	}
	// #ifdef APP-PLUS
	//判断app是否存在
	if (plus.runtime.isApplicationExist({
			pname: data.package
		})) {
		//调用第三方app
		plus.runtime.launchApplication({
				pname: data.package
			},
			function(e) {
				uni.showToast({
					title: "打开失败",
					icon: "none"
				})
			}
		)
		callback && callback(true);
	} else {
		plus.runtime.openURL(encodeURI(data.appUrl), function(res) {});
		callback && callback(true);
	}
	// #endif

	// #ifdef MP
	// 打开APP下载的网页
	uni.navigateTo({
		url: "/pages/other/webView?url=" + encodeURI(data.appUrl)
	})
	// #endif
}

/**
 * 打开网页
 * @param {Object} data {url：''}
 * @param {Object} callback
 */
export const openWebView = (data, callback) => {
	if (uni.$u.test.isEmpty(data)) {
		uni.showToast({
			title: "打开失败",
			icon: "none"
		})
		callback && callback(false);
		return;
	}
	// #ifdef APP-PLUS
	//判断app是否存在
	plus.runtime.openURL(encodeURI(data.url), function(res) {});
	callback && callback(true);
	// #endif

	// #ifdef MP
	// 打开APP下载的网页
	uni.navigateTo({
		url: "/pages/other/webView?url=" + encodeURI(data.url)
	})
	// #endif
}

/**
 * @description 解析 URL 中的查询参数，并返回包含原始 URL 和参数对象
 * @param {string} url - 要解析的 URL 字符串
 * @returns {Object} - 返回不含查询参数的原始 URL 和解析后的参数对象，若无查询参数则返回空对象
 */
export const parseUrlAndParams = (url) => {
	// 容错处理：如果传入的 URL 不是字符串，直接返回空 URL 和空参数对象
	if (typeof url !== 'string') {
		return {
			url: '',
			params: {}
		};
	}

	try {
		// 使用正则表达式匹配 URL 的查询参数部分
		const [baseUrl, queryString] = url.split('?');

		// 如果没有查询字符串，返回原始 URL 和空对象
		if (!queryString) {
			return {
				url: baseUrl, // 返回不含查询参数的 URL
				params: {}
			};
		}

		// 使用 reduce 方法将查询字符串解析为对象
		const params = queryString.split('&').reduce((result, param) => {
			const [key, value] = param.split('='); // 分割参数名和值
			if (key) {
				// 处理没有值的情况，默认为空字符串
				result[key] = value ? decodeURIComponent(value) : '';
			}
			return result;
		}, {});

		// 返回不含查询参数的 URL 和解析后的参数对象
		return {
			url: baseUrl, // 去除查询参数后的 URL
			params
		};
	} catch (error) {
		// 错误处理：捕获并记录解析错误，返回空对象保证程序健壮性
		return {
			url: '',
			params: {}
		};
	}
};