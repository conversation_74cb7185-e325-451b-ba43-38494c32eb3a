import store from '@/store';
import base from '@/common/config.js';
import cons from '@/common/constant.js';
import {
	iProtocolUrl
} from "@/common/utils.js"

function buildSheet() {
	let platform = uni.getSystemInfoSync().platform;

	let config = {};

	const bodyHeight = plus.display.resolutionHeight;
	const bodyWidth = plus.display.resolutionWidth;
	const dialogWidth = bodyWidth;
	const dialogHeight = 400;
	const unit = platform == 'android' ? parseInt(dialogHeight / 10) : parseInt(dialogHeight / 8);
	const sloganY = String(Math.ceil(unit * 0.8));
	const numberY = String(Math.ceil(unit * 1.6));
	const loginBtnY = String(Math.ceil(unit * 2.8));
	const loginBtnX = String(Math.ceil((dialogWidth - 230) / 2));
	const switchY = platform == 'android' ? String(Math.ceil(unit * 4.5)) : String(Math.ceil(unit * 4.2));
	/* 二次隐私弹窗 */
	const privacyAlertWidth = bodyWidth;
	const privacyAlertHeight = platform == 'android' ? 250 : 220;
	const privacyAlertY = bodyHeight - privacyAlertHeight;
	const privacyAlertContentWidth = String(Math.ceil(privacyAlertWidth - 2 * 20));

	if (platform == 'android') {
		config = {
			uiConfig: {
				setDialogTheme: {
					width: String(dialogWidth),
					height: String(dialogHeight),
					isBottom: "true"
				},
				setPrivacyAlertIsNeedShow: true,
				setNavUi: {
					text: "快捷登录",
					textColor: "#000000",
					returnImgPath: "static/image/ic_close.png",
					returnImgX: String(dialogWidth - 44 - 15),
					returnImgWidth: "40",
					returnImgHeight: "40",
				},
				setSloganUi: {
					textColor: "#999999",
					textSize: "12",
					offsetY: sloganY,
				},
				setNumberUi: {
					offsetY: numberY,
					textSize: "23",
				},
				setLoginBtnUi: {
					width: "300", //按钮宽度，注意后面不要加单位，默认单位为dp，注：使用整形字符串，暂不支持浮点型字符串
					height: "45", //按钮高度，注意后面不要加单位，默认单位为dp，注：使用整形字符串，暂不支持浮点型字符串
					offsetY: loginBtnY,
					imgPath: "static/image/img_login_bg.png", //按钮背景图片路径
					text: "本机号码一键登录", //登录按钮标题，不设置则使用默认
					textColor: "#FFFFFF", //按钮文字颜色
					textSize: "16", //按钮文字大小，注意后面不要加单位，默认单位为sp
				},
				setSwitchUi: {
					text: "密码登录",
					textColor: "#666666",
					textSize: "15", //按钮文字大小，注意后面不要加单位，默认单位为sp
					offsetY: switchY,
				},
				//设置协议相关
				setPrivacyUi: {
					baseColor: "#333333", //协议文案非协议部分颜色
					protocolColor: "#444BF1", //协议文案协议富文本颜色
					textSize: 13,
					bottom: 20
				},
				//check box相关设置
				setCheckBoxUi: {
					width: "20", //按钮宽度，注意后面不要加单位，默认单位为dp，注：使用整形字符串，暂不支持浮点型字符串
					height: "20",
					defaultChecked: "false", //check box默认是否勾选，不设置则默认不勾选，需要用户手动勾选（true：默认勾选，false：默认不勾选）
					unCheckedImgPath: "static/image/ic_checkbox_nor.png", //未勾选状态时勾选框图标路径（仅支持static根目录的图片）
					checkedImgPath: "static/image/ic_checkbox_sel.png", //已勾选状态时勾选框的图标路径
				},
				setAppPrivacyOne: {
					title: '用户协议',
					url: iProtocolUrl(cons.PROTOCOL_USER)
				},
				setAppPrivacyTwo: {
					title: '隐私政策',
					url: cons.PROTOCOL_PRIVACY,
				}, //设置协议详情页导航栏相关
				setWebNavUi: {
					bgColor: "#ffffff", //协议详情页的导航栏背景色
					textColor: "#333333", //协议详情页的导航栏标题颜色
					textSize: "20", //协议详情页的导航栏标题大小，注意后面不要加单位，默认单位为sp
					// returnImgPath: "/static/close_black.png" //协议页面标题栏返回按钮图标路径
				},
				//二次弹窗属性
				setPrivacyAlertUi: {
					needAutoLogin: true, //点击确认按钮后是否自动登录
					left: "0", //弹窗水平偏移量，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					top: privacyAlertY, //弹窗竖直偏移量，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					radius: ["15", "15", "0", "0"], //四个圆角值,顺序为左上，左下，右下，右上，需要填充4个值，不足4个值则无效，如果值<=0则为直角 ，默认0
					width: String(privacyAlertWidth), //弹窗宽度，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					height: String(privacyAlertHeight), //弹窗高度，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					alpha: 1.0, //二次弹窗透明度范围0.3~1.0
					backgroundColor: "#FFFFFF", //二次弹窗背景色
				},
				//二次弹窗蒙层属性
				setPrivacyAlertMaskUi: {
					needShow: true, //背景蒙层是否显示
					tapMaskClosed: true, //点击背景蒙层是否关闭弹窗
					color: "#000000", //蒙层颜色
					alpha: 0.8, // 蒙层透明度范围0.3~1.0
				},
				//二次弹窗顶部标题栏
				setPrivacyAlertTitleUi: {
					backgroundColor: "#FFFFFF", //顶部标题背景色
					left: "0", //顶部标题水平偏移量，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					top: "10", //顶部标题竖直偏移量，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					width: String(privacyAlertWidth), //ios专用，标题宽度，单位pt
					alignment: "1", //andriod取值：0居左  1居中，iOS取值0,1,2,3,4，分别对应居左，居中，居右，左右对齐，默认对齐
					text: "请阅读并同意以下条款", //顶部标题文本
					textSize: "18", //顶部标题文本字体大小,注意后面不要加单位（Android默认为dp，iOS默认为pt）
					textColor: "#000000", //顶部标题文本字体颜色
				},
				//二次弹窗协议文本    
				setPrivacyAlertContentUi: {
					backgroundColor: "#FFFFFF", //协议内容区域背景颜色
					textSize: "16", //协议文本字体大小,注意后面不要加单位（Android默认为dp，iOS默认为pt）
					alignment: "0", //协议文本字体对齐方式,andriod取值：0居左  1居中，iOS取值0,1,2,3,4，分别对应居左，居中，居右，左右对齐，默认对齐
					left: "20", //左边距，android侧为横向两侧边距，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					top: "15", //上边距，android侧为纵向两侧边距，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					width: privacyAlertContentWidth, //ios专用，单位pt
					height: "", //ios专用，单位pt 
					baseColor: "#000000", //协议文案非协议部分颜色
					protocolColor: "#444BF1", //协议文案协议富文本颜色
					privacyAlertOperatorColor: "#444BF1", //单独设置运营商协议富文本颜色，该字段优先级最高，设置了该字段之后protocolColor字段设置的将失效，若该字段不设置，则采用protocolColor的值
					privacyAlertOneColor: "#444BF1", //单独设置第一个协议富文本颜色，该字段优先级最高，设置了该字段之后protocolColor字段设置的将失效，若该字段不设置，则采用protocolColor的值
					privacyAlertTwoColor: "#444BF1", //单独设置第二个协议富文本颜色，该字段优先级最高，设置了该字段之后protocolColor字段设置的将失效，若该字段不设置，则采用protocolColor的值
					privacyAlertThreeColor: "#444BF1", //单独设置第三个协议富文本颜色，该字段优先级最高，设置了该字段之后protocolColor字段设置的将失效，若该字段不设置，则采用protocolColor的值
					privacyAlertBeforeText: "我已阅读并同意", //协议整体文案前缀
					privacyAlertEndText: "服务协议" //协议整体文案后缀
				},
				//二次弹窗确认按钮  
				setPrivacyAlertConfirmUi: {
					left: "", //左边距，注意后面不要加单位（Android默认为dp，iOS默认为pt） 
					top: "", //上边距，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					width: privacyAlertContentWidth, //确认按钮宽度，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					height: "50", //确认按钮高度，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					text: "同意并继续", //确认按钮文本
					// imgPath: "static/image/img_login_bg.png", //确认按钮背景图地址,如果需要区分正常效果和点击效果，则使用activeImgPath和hightedImgPath
					color: "#444BF1", //如果没有设置imgPath或activeImgPath，hightedImgPath，则该属性作为按钮的背景颜色
					textColor: "#FFFFFF", //确认按钮字体颜色
					hightedTextColor: "#FFFFFF", //确认按钮点击字体颜色
					textSize: "18", //确认按钮字体大小默认18dp，注意后面不要加单位（Android默认为dp，iOS默认为pt）       
				}
			}
		};
	} else if (platform == 'ios') {
		config = {
			uiConfig: {
				setDialogTheme: {
					radius: ["8", "0", "0", "8"],
					x: String((bodyWidth - dialogWidth) * 0.5),
					y: String(bodyHeight - dialogHeight),
					width: String(dialogWidth),
					height: String(dialogHeight),
				},
				setPrivacyAlertIsNeedShow: true,
				setNavUi: {
					text: "快捷登录 ",
					textColor: "#000000",
					textSize: "20",
					bgColor: "#ffffff",
					returnImgPath: "/static/image/ic_close.png",
					returnImgX: String(dialogWidth - 50),
					returnImgY: "5",
					returnImgWidth: "50",
					returnImgHeight: "50",
				},
				setSloganUi: {
					y: sloganY,
				},
				setNumberUi: {
					y: numberY,
					textSize: "24",
				},
				setLoginBtnUi: {
					width: "300",
					height: "45",
					y: loginBtnY,
					// x: loginBtnX,
					activeImgPath: "static/image/img_login_bg.png", //按钮背景图片路径
					invalidImgPath: "static/image/img_login_bg.png", //按钮背景图片路径
					hightedImgPath: "static/image/img_login_bg.png", //按钮背景图片路径
					text: "本机号码一键登录", //登录按钮标题，不设置则使用默认
					textColor: "#FFFFFF", //按钮文字颜色
					textSize: "16", //按钮文字大小，注意后面不要加单位，默认单位为sp
				},
				setSwitchUi: {
					text: "密码登录",
					textColor: "#666666",
					textSize: "15",
					y: switchY,
				},
				//设置协议相关
				setPrivacyUi: {
					baseColor: "#333333", //协议文案非协议部分颜色
					protocolColor: "#444BF1", //协议文案协议富文本颜色
					textSize: 15,
					bottom: 20
				},
				//check box相关设置
				setCheckBoxUi: {
					width: "20",
					defaultChecked: "false", //check box默认是否勾选，不设置则默认不勾选，需要用户手动勾选（true：默认勾选，false：默认不勾选）
					unCheckedImgPath: "static/image/ic_checkbox_nor.png", //未勾选状态时勾选框图标路径（仅支持static根目录的图片）
					checkedImgPath: "static/image/ic_checkbox_sel.png", //已勾选状态时勾选框的图标路径
				},
				setAppPrivacyOne: {
					title: '用户协议',
					url: iProtocolUrl(cons.PROTOCOL_USER)
				},
				setAppPrivacyTwo: {
					title: '隐私政策',
					url: cons.PROTOCOL_PRIVACY,
				},
				setWebNavUi: {
					bgColor: "#ffffff", //协议详情页的导航栏背景色
					textColor: "#333333", //协议详情页的导航栏标题颜色
					textSize: "20", //协议详情页的导航栏标题大小，注意后面不要加单位，默认单位为sp
					// returnImgPath: "/static/close_black.png" //协议页面标题栏返回按钮图标路径
				},
				//二次弹窗属性
				setPrivacyAlertUi: {
					needAutoLogin: true, //点击确认按钮后是否自动登录
					left: "0", //弹窗水平偏移量，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					top: privacyAlertY, //弹窗竖直偏移量，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					radius: ["8", "0", "0", "8"], //四个圆角值,顺序为左上，左下，右下，右上，需要填充4个值，不足4个值则无效，如果值<=0则为直角 ，默认0
					width: String(privacyAlertWidth), //弹窗宽度，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					height: String(privacyAlertHeight), //弹窗高度，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					alpha: 1.0, //二次弹窗透明度范围0.3~1.0
					backgroundColor: "#FFFFFF", //二次弹窗背景色
				},
				//二次弹窗蒙层属性
				setPrivacyAlertMaskUi: {
					needShow: true, //背景蒙层是否显示
					tapMaskClosed: true, //点击背景蒙层是否关闭弹窗
					color: "#000000", //蒙层颜色
					alpha: 0.8, // 蒙层透明度范围0.3~1.0
				},
				//二次弹窗顶部标题栏      
				setPrivacyAlertTitleUi: {
					backgroundColor: "#FFFFFF", //顶部标题背景色
					left: "0", //顶部标题水平偏移量，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					top: "10", //顶部标题竖直偏移量，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					width: String(privacyAlertWidth), //ios专用，标题宽度，单位pt
					height: "40", //ios专用，标题高度，单位pt
					alignment: "1", //andriod取值：0居左  1居中，iOS取值0,1,2,3,4，分别对应居左，居中，居右，左右对齐，默认对齐
					text: "请阅读并同意以下条款", //顶部标题文本
					textSize: "18", //顶部标题文本字体大小,注意后面不要加单位（Android默认为dp，iOS默认为pt）
					textColor: "#000000", //顶部标题文本字体颜色
				},
				//二次弹窗协议文本    
				setPrivacyAlertContentUi: {
					backgroundColor: "#FFFFFF", //协议内容区域背景颜色
					textSize: "16", //协议文本字体大小,注意后面不要加单位（Android默认为dp，iOS默认为pt）
					alignment: "0", //协议文本字体对齐方式,andriod取值：0居左  1居中，iOS取值0,1,2,3,4，分别对应居左，居中，居右，左右对齐，默认对齐
					left: "20", //左边距，android侧为横向两侧边距，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					top: "60", //上边距，android侧为纵向两侧边距，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					width: privacyAlertContentWidth, //ios专用，单位pt
					height: "", //ios专用，单位pt 
					baseColor: "#000000", //协议文案非协议部分颜色
					protocolColor: "#444BF1", //协议文案协议富文本颜色
					privacyAlertOperatorColor: "#444BF1", //单独设置运营商协议富文本颜色，该字段优先级最高，设置了该字段之后protocolColor字段设置的将失效，若该字段不设置，则采用protocolColor的值
					privacyAlertOneColor: "#444BF1", //单独设置第一个协议富文本颜色，该字段优先级最高，设置了该字段之后protocolColor字段设置的将失效，若该字段不设置，则采用protocolColor的值
					privacyAlertTwoColor: "#444BF1", //单独设置第二个协议富文本颜色，该字段优先级最高，设置了该字段之后protocolColor字段设置的将失效，若该字段不设置，则采用protocolColor的值
					privacyAlertThreeColor: "#444BF1", //单独设置第三个协议富文本颜色，该字段优先级最高，设置了该字段之后protocolColor字段设置的将失效，若该字段不设置，则采用protocolColor的值
					privacyAlertBeforeText: "我已阅读并同意", //协议整体文案前缀
					privacyAlertEndText: "服务协议" //协议整体文案后缀
				},
				//二次弹窗确认按钮   
				setPrivacyAlertConfirmUi: {
					left: "", //左边距，注意后面不要加单位（Android默认为dp，iOS默认为pt） 
					top: "130", //上边距，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					width: privacyAlertContentWidth, //确认按钮宽度，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					height: "50", //确认按钮高度，注意后面不要加单位（Android默认为dp，iOS默认为pt）
					text: "同意并继续", //确认按钮文本
					// imgPath: "static/image/img_login_bg.png", //确认按钮背景图地址,如果需要区分正常效果和点击效果，则使用activeImgPath和hightedImgPath
					color: "#444BF1", //如果没有设置imgPath或activeImgPath，hightedImgPath，则该属性作为按钮的背景颜色
					textColor: "#FFFFFF", //确认按钮字体颜色
					hightedTextColor: "#FFFFFF", //确认按钮点击字体颜色
					textSize: "18", //确认按钮字体大小默认18dp，注意后面不要加单位（Android默认为dp，iOS默认为pt）       
				}
			}
		};
	}
	return config;
}


module.exports = {
	buildSheet: buildSheet,
}