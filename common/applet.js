import store from '@/store';
// 小程序更新
function mpUpdate() {
	// #ifdef MP-WEIXIN
	if (uni.canIUse("getUpdateManager")) {
		const updateManager = uni.getUpdateManager();
		updateManager.onCheckForUpdate(function(res) {
			// 请求完新版本信息的回调
			if (res.hasUpdate) {
				updateManager.onUpdateReady(function() {
					uni.showModal({
						title: "更新提示",
						content: "新版本已经准备好，是否重启应用？",
						success: function(res) {
							if (res.confirm) {
								// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
								updateManager.applyUpdate();
							}
						}
					});
				});
				updateManager.onUpdateFailed(function() {
					// 新的版本下载失败
					uni.showModal({
						title: "已经有新版本了哟~",
						content: "新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~"
					});
				});
			}
		});
	}
	// #endif
}

// 小程序进入场景参数
// flag uni.scanCode方法点击扫码的参数没有q，为true转换参数
function mpScene(e, callback, flag = false) {
	store.commit("SET_CHAT_SCENES_INFO", {}); //先请空
	console.log(e, 'scene')
	var qrCodeValue = ''
	// #ifndef MP-ALIPAY
	if (flag) {
		e.query = {
			q: e.result
		}
		console.log(e, 'scene-转换后')
	}
	if (e.query.q) {
		let scene = decodeURIComponent(e.query.q).split("?")[1];
		scene = scene.split("&");
		let data = {
			//场景值
			scene: e.scene,
		};
		scene.forEach(item => {
			let arr = item.split("=");
			if (arr.length == 2) {
				data[arr[0]] = arr[1];
			}
		});
		store.commit("SET_CHAT_SCENES_INFO", Object.assign(e.query, data));
		console.log(store.state.chatScenesInfo, 'scene--解码参数')
	} else {
		store.commit("SET_CHAT_SCENES_INFO", Object.assign(e.query, {
			path: e.path
		}));
	}
	// #endif
	// #ifdef MP-ALIPAY
	var AliqrCode = {}
	if (flag) {
		e.query = {
			qrCode: e.result
		}
		console.log(e, 'scene-转换后')
	}
	if (e.query && e.query.qrCode) {
		AliqrCode.q = e.query.qrCode;
	} else if (e.query && !e.query.qrCode) {
		AliqrCode = e.query;
	}
	if (AliqrCode.q) {
		var queryParam = AliqrCode.q.split("?")[1] //二维码清除域名
		var scene = queryParam.split("&");
		let data = {};
		scene.forEach(item => {
			let arr = item.split("=");
			if (arr.length == 2) {
				data[arr[0]] = arr[1];
			}
		});
		store.commit("SET_CHAT_SCENES_INFO", Object.assign(AliqrCode, data));
		console.log(store.state.chatScenesInfo, 'scene--支付宝小程序解码参数')
	} else {
		store.commit("SET_CHAT_SCENES_INFO", Object.assign(AliqrCode, {
			path: e.path
		}));
	}
	// #endif
	callback && callback()
}

export {
	mpUpdate, // 小程序更新
	mpScene // 小程序进入场景参数
}