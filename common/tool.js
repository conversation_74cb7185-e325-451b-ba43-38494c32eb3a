/* 涉及到项目中的函数 */

/**
 * 构造树型结构数据
 *
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 * @param {*} rootId 根Id 默认 0
 */
export function handleTree(
	data,
	id = 'id',
	parentId = 'parentId',
	children = 'children', // 自定义子节点字段名
	rootId = 0,
) {
	const cloneData = JSON.parse(JSON.stringify(data));
	const treeData = cloneData.filter((father) => {
		// 1. 筛选当前父节点的子节点
		const branchArr = cloneData.filter((child) => {
			return father[id] === child[parentId];
		});
		// 2. 用参数 children 作为子节点字段名（核心修复）
		if (branchArr.length > 0) {
			father[children] = branchArr; // 替换硬编码的 father.children
		} else {
			// 可选：移除空数组或保留，根据需求决定
			// father[children] = []; 
		}
		// 3. 返回根节点
		return father[parentId] === rootId;
	});
	return treeData.length > 0 ? treeData : data;
}