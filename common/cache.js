import config from '@/common/config.js';

// 定义常量
const EXPIRY_SUFFIX = "_expiry_time";
const EXPIRY_TIME_MS = 1000 * 60; // 1分钟的毫秒数

class Cache {
	constructor(name) {
		this.cacheName = name;
		this.expirySuffix = EXPIRY_SUFFIX;
		this.expiryTimeMs = EXPIRY_TIME_MS;
	}

	// 计算过期时间戳
	calculateExpiryTime(minutes) {
		return minutes * this.expiryTimeMs + Date.now();
	}

	// 检查是否过期
	isExpired(expiryTime) {
		return Date.now() >= expiryTime;
	}

	// 设置缓存数据和过期时间
	setCacheItem(cache, key, data, expiryMinutes) {
		if (expiryMinutes) {
			cache[key] = {
				data,
				[`${key}${this.expirySuffix}`]: this.calculateExpiryTime(expiryMinutes),
			};
		} else {
			cache[key] = data;
		}
	}

	// 设置缓存
	set(key, data, expiryMinutes = 0) {
		try {
			let cache = this.get(this.cacheName) || {};
			if (key === this.cacheName) {
				cache = data;
			} else {
				this.setCacheItem(cache, key, data, expiryMinutes);
			}
			uni.setStorageSync(this.cacheName, cache);
			console.log(`缓存设置成功，键：${key}`);
		} catch (error) {
			console.error("设置缓存失败：", error);
		}
	}

	// 获取缓存
	get(key) {
		try {
			const cacheData = uni.getStorageSync(this.cacheName);
			if (!cacheData) return null;

			if (key === this.cacheName) return cacheData;

			const cacheItem = cacheData[key];
			if (cacheItem && typeof cacheItem === 'object') {
				const expiryKey = `${key}${this.expirySuffix}`;
				if (expiryKey in cacheItem && this.isExpired(cacheItem[expiryKey])) {
					this.remove(key);
					return null;
				}
				return cacheItem.data;
			}
			return cacheItem;
		} catch (error) {
			console.error("获取缓存失败：", error);
			return null;
		}
	}

	// 更新缓存
	update(key, data, expiryMinutes = 0) {
		try {
			let cache = this.get(this.cacheName) || {};
			if (key in cache) {
				this.setCacheItem(cache, key, data, expiryMinutes);
				uni.setStorageSync(this.cacheName, cache);
				console.log(`缓存更新成功，键：${key}`);
			} else {
				console.warn(`缓存键 ${key} 不存在。请使用 set() 方法创建新缓存。`);
			}
		} catch (error) {
			console.error("更新缓存失败：", error);
		}
	}

	// 删除缓存
	remove(key) {
		try {
			if (key === this.cacheName) {
				uni.removeStorageSync(this.cacheName);
			} else {
				let cache = this.get(this.cacheName);
				if (cache && key in cache) {
					delete cache[key];
					uni.setStorageSync(this.cacheName, cache);
				}
			}
			console.log(`缓存删除成功，键：${key}`);
		} catch (error) {
			console.error("删除缓存失败：", error);
		}
	}

	// 清空所有缓存
	clear() {
		try {
			uni.removeStorageSync(this.cacheName);
			console.log("所有缓存已清空");
		} catch (error) {
			console.error("清空所有缓存失败：", error);
		}
	}

	// 检查缓存是否存在且有效
	exists(key) {
		try {
			const cache = this.get(this.cacheName);
			if (cache && key in cache) {
				const cacheItem = cache[key];
				const expiryKey = `${key}${this.expirySuffix}`;
				if (expiryKey in cacheItem && this.isExpired(cacheItem[expiryKey])) {
					this.remove(key);
					return false;
				}
				return true;
			}
			return false;
		} catch (error) {
			console.error("检查缓存存在性失败：", error);
			return false;
		}
	}
}

export default new Cache(config.appName);