import store from '@/store';

// 支付(app、小程序)
function setPay(payInfo, callback) {
	// let userInfo = store.state.userInfo
	const reqParams = {
		id: payInfo.payId || '',
		channelCode: payInfo.channel || '',
		channelExtras: {}, //支付渠道的额外参数，例如说，微信公众号需要传递 openid 参数
	}
	var EnvUtils = plus.android.importClass("com.alipay.sdk.app.EnvUtils");
	EnvUtils.setEnv(EnvUtils.EnvEnum.SANDBOX);
	uni.$u.http.post('/pay/order/submit', reqParams).then((data) => {
		const reqPay = {
			success: (res) => {
				callback && callback({
					success: true,
					msg: '支付成功'
				});
				console.log('success:' + JSON.stringify(res));
			},
			fail: (err) => {
				var msg = err.errMsg;
				if (err.errMsg === 'requestPayment:fail [paymentAlipay:62001]user cancel' ||
					err.errMsg === 'requestPayment:fail cancel') {
					msg = '支付已取消'
				}
				callback && callback({
					success: false,
					msg: msg
				});
				console.log('fail:' + JSON.stringify(err));
			}
		};
		if (payInfo.channel == 'wx_lite') { // 微信小程序
			const payConfig = JSON.parse(data.displayContent);
			reqPay.provider = 'wxpay';
			reqPay.timeStamp = payConfig.timeStamp;
			reqPay.nonceStr = payConfig.nonceStr;
			reqPay.package = payConfig.packageValue;
			reqPay.signType = payConfig.signType;
			reqPay.paySign = payConfig.paySign
		} else if (payInfo.channel == 'wx_app') { // app微信
			reqPay.provider = 'wxpay';
			reqPay.orderInfo = data.displayContent;
		} else if (payInfo.channel == 'alipay_app') { //app支付宝
			reqPay.provider = 'alipay';
			reqPay.orderInfo = data.displayContent;
		}
		console.log("支付参数", reqPay);
		uni.requestPayment(reqPay);
	}, err => {
		callback && callback({
			success: false,
			msg: err.msg
		});
	});
}

export {
	setPay //支付(app、小程序)
}