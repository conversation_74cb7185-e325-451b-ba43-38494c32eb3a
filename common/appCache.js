// function startClearCache(callback) {
// 	// uni.showModal({
// 	// 	title: '清除缓存',
// 	// 	content: '您确定要清除缓存吗？',
// 	// 	success: function(res) {
// 	// 		if (res.confirm) {
// 	// 			console.log('用户点击确定');
// 	// 			clearCache(callback);
// 	// 		} else if (res.cancel) {
// 	// 			console.log('用户点击取消');
// 	// 		}
// 	// 	}
// 	// });
// 	uni.dialog.confirm("清除缓存", "您确定要清除缓存吗?", () => {
// 		clearCache(callback);
// 	}, () => {
// 		// 取消
// 	});
// }
// #ifdef APP-PLUS
// 计算缓存大小
function cacheFormatSize(callback) {
	let fileSizeString = '0B';
	plus.cache.calculate(function(size) {
		let sizeCache = parseInt(size);
		if (sizeCache == 0) {
			fileSizeString = "0B";
		} else if (sizeCache < 1024) {
			fileSizeString = sizeCache + "B";
		} else if (sizeCache < 1048576) {
			fileSizeString = (sizeCache / 1024).toFixed(2) + "KB";
		} else if (sizeCache < 1073741824) {
			fileSizeString = (sizeCache / 1048576).toFixed(2) + "MB";
		} else {
			fileSizeString = (sizeCache / 1073741824).toFixed(2) + "GB";
		}
		callback(fileSizeString);
	});

}

// 清理缓存
function clearCache(callback) {
	let os = plus.os.name;
	if (os == 'Android') {
		let main = plus.android.runtimeMainActivity();
		let sdRoot = main.getCacheDir();
		let files = plus.android.invoke(sdRoot, "listFiles");
		let len = files.length;
		for (let i = 0; i < len; i++) {
			let filePath = '' + files[i]; // 没有找到合适的方法获取路径，这样写可以转成文件路径  
			plus.io.resolveLocalFileSystemURL(filePath, function(entry) {
					if (entry.isDirectory) {
						entry.removeRecursively(function(entry) { //递归删除其下的所有文件及子目录  
							uni.showToast({
								title: '缓存清理完成',
								duration: 2000
							});
							callback && callback();
						}, function(e) {
							console.log(e.message)
						});
					} else {
						entry.remove();
					}
				},
				function(e) {
					console.log('文件路径读取失败')
				})
		}
	} else { // ios  
		plus.cache.clear(function() {
			uni.showToast({
				title: '缓存清理完成',
				duration: 2000
			})
			callback && callback();
		})
	}
}

export {
	clearCache,
	cacheFormatSize,
}
// #endif