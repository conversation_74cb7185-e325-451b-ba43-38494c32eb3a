import conf from '@/common/config.js';
import $store from '@/store';
export default {

	// 终端类型
	TERMINAL_TYPE: () => {
		const sys = uni.getSystemInfoSync()
		switch (sys.uniPlatform) {
			case 'app':
				if (sys.osName == 'ios') {
					return 15;
				} else if (sys.osName == 'android') {
					return 10;
				}
				return 0;
			case 'mp-weixin':
				return 20;
			case 'mp-baidu':
				return 25;
			case 'mp-toutiao':
				return 30;
			case 'mp-kuaishou':
				return 35
			default:
				return 0;
		}
	},

	/********************************* 协议相关 ********************************/
	// 用户协议
	PROTOCOL_USER: 'protocol.user',
	// 隐私政策
	PROTOCOL_PRIVACY: 'protocol.privacy_policy',
	// 信息保护协议
	PROTOCOL_PROTECT: 'protocol.information_protection',
	// 账号注销协议
	PROTOCOL_CANCEL: 'protocol.account_cancellation',
	// 支付协议
	PROTOCOL_PAYMENT: 'protocol.payment',
	// 入驻协议
	PROTOCOL_ENTER: 'protocol.settlement',
	// 实名认证
	PROTOCOL_CERT: 'protocol.certification',
	// 个人信息清单
	PROTOCOL_PERSONAL_INFO_LIST: 'protocol.personal_info_collection',
	// 第三方信息共享清单
	PROTOCOL_THIRD_INFO_LIST: 'protocol.third_party_info_sharing',
	// 隐私政策URL
	PROTOCOL_PRIVACY_URL: 'xxxx',
	// ICP备案地址
	PROTOCOL_ICP_URL: 'https://beian.miit.gov.cn/#/Integrated/index',
	// 协议和文本的的映射
	PROTOCOL_MAPS: {
		'protocol.user': '用户协议',
		'protocol.privacy_policy': '隐私政策',
		'protocol.information_protection': '信息保护声明',
		'protocol.account_cancellation': '账号注销须知',
		'protocol.payment': '支付协议',
		'protocol.settlement': '入驻协议',
		'protocol.certification': '实名认证',
		'protocol.personal_info_collection': '个人信息清单',
		'protocol.third_party_info_sharing': '第三方信息共享清单',
	},

	/********************************* 性别 ********************************/
	// 男
	SEX_MALE: 1,
	// 女
	SEX_FEMALE: 2,

	/********************************* 分类类型 ********************************/
	// 服务类型
	CATEGORY_TYPE_SERVICE: 10,
	// 行业类型
	CATEGORY_TYPE_INDUSTRY: 20,
	// 资质类型
	CATEGORY_TYPE_QUALIFICATION: 30,
	// 申报类型
	CATEGORY_TYPE_DECLARATION: 40,
	// 主题类型
	CATEGORY_TYPE_SUBJECT: 50,

	/********************************* 分类展示场景 ********************************/
	// 首页分类展示场景
	CATEGORY_DISPLAY_SCENE_HOME: 10,
	// 首页推荐分类展示场景
	CATEGORY_DISPLAY_SCENE_HOME_RECOMMEND: 15,
	// 线索大厅展示场景
	CATEGORY_DISPLAY_SCENE_HALL: 20,
	// 分类中心展示场景
	CATEGORY_DISPLAY_SCENE_CENTER: 30,
	
	/********************************* 服务商类型 ********************************/
	// 个人
	PROVIDER_TYPE_PERSONAL: 1,
	// 公司
	PROVIDER_TYPE_COMPANY: 2,
	
	/********************************* 服务商类型审核状态类型 ********************************/
	// 未提交
	PROVIDER_PROFILE_AUDIT_STATUS_UNSUBMITTED: 0,
	// 待审核
	PROVIDER_PROFILE_AUDIT_STATUS_PENDING: 1,
	// 审核通过
	PROVIDER_PROFILE_AUDIT_STATUS_APPROVED: 2,
	// 审核拒绝
	PROVIDER_PROFILE_AUDIT_STATUS_REJECTED: 3,


	/********************************* 认证类型 ********************************/
	// 个人
	CERTIFICATION_TYPE_PERSONAL: 1,
	// 公司
	CERTIFICATION_TYPE_COMPANY: 2,


	/********************************* 认证状态类型 ********************************/
	// 未提交
	CERTIFICATION_STATUS_UNSUBMITTED: 0,
	// 待审核
	CERTIFICATION_STATUS_PENDING: 1,
	// 审核通过
	CERTIFICATION_STATUS_APPROVED: 2,
	// 审核拒绝
	CERTIFICATION_STATUS_REJECTED: 3,


	/********************************* 产品审核状态 ********************************/
	// 待审核
	PRODUCT_AUDIT_STATUS_PENDING: 1,
	// 审核通过
	PRODUCT_AUDIT_STATUS_APPROVED: 2,
	// 审核拒绝
	PRODUCT_AUDIT_STATUS_REJECTED: 3,

	/********************************* 置顶业务类型 ********************************/
	// 产品
	PIN_BIZ_TYPE_PRODUCT: 1,
	// 服务商
	PIN_BIZ_TYPE_PROVIDER: 2,


	/********************************* 需求类型 ********************************/
	/* 线索单 */
	DEMAND_TYPE_CLUE: 1,
	/* 预约单 */
	DEMAND_TYPE_APPOINT: 2,

	/********************************* 需求来源 ********************************/
	// 预约产品
	DEMAND_ORIGIN_TYPE_APPOINT_PRODUCT: 2,
	// 预约服务商
	DEMAND_ORIGIN_TYPE_APPOINT_MERCHANT: 3,
	// 咨询产品
	DEMAND_ORIGIN_TYPE_CONSULT_PRODUCT: 5,
	// 咨询服务商
	DEMAND_ORIGIN_TYPE_CONSULT_PROVIDER: 6,
	// 主动发布需求
	DEMAND_ORIGIN_TYPE_PUBLISH: 1,
	
	
	/********************************* 咨询方式 ********************************/
	// 电话咨询
	DEMAND_CONSULT_CHANNEL_PHONE: 1,
	// 微信咨询
	DEMAND_CONSULT_CHANNEL_WECHAT: 2,
	// 在线咨询
	DEMAND_CONSULT_CHANNEL_ONLINE: 3,


	/********************************* 主动发布需求子类型【对应（DEMAND_ORIGIN_TYPE_PUBLISH）的子类型】 ********************************/
	// 发布需求
	DEMAND_ORIGIN_SUBTYPE_NORMAL: 1,
	// 发布资质服务需求
	DEMAND_ORIGIN_SUBTYPE_QUALIFICATION: 2,
	// 发布申报服务需求
	DEMAND_ORIGIN_SUBTYPE_DECLARATION: 3,


	/********************************* 资质服务交易类型，对应（DEMAND_ORIGIN_SUBTYPE_QUALIFICATION） ********************************/
	DEMAND_QUALIFICATION_TRADE_TYPE: {
		BUY: {
			type: 1,
			name: '资质求购'
		},
		SELL: {
			type: 2,
			name: '资质转让'
		}
	},

	/********************************* 分享场景 ********************************/
	// 从商品详情分享
	SHARE_SCENE_PRODUCT_DETAIL: '100',


	/********************************* 检查操作权限的KEY ********************************/
	kPermission_Key_xx: 'xx',



	/********************************* 文本常量|弹窗提示 ********************************/
	// 使用APP时弹出的系统权限告知说明
	kPermission_Tip_Used_Text: '您在使用会计代账宝时，最小必要权限为：使用手机存储权限、相机权限，如不提供以上权限，将无法使用相关功能。您可自行手动设置权限管理关闭，您在登录后点击我的-右上角设置-APP系统权限设置中开启和关闭权限。',
	// 隐私政策告知提示【包含用户协议和隐私政策】
	kPrivacy_Policy_Update_Text: "尊敬的用户，您好，为了加强对您个人信息的保护，根据最新法律法规要求，我们更新了隐私政策，请您仔细阅读并确认<a style='color:#2734B4' href='https://xieyi.5jia.vip/jiazhengtong.html'>《隐私政策》</a>，我们将严格按照政策内容使用和保护您的个人信息，为您提供更好的服务。",
	// 隐私政策更新提示 【只有隐私政策】
	kPrivacy_Policy_Tip_Text: `欢迎使用会计代账宝，我们承诺会采取业界先进的安全措施保护您的信息安全。在您使用本应用时，我们会向您申请或获取手机存储、相机权限，同时需要您的设备信息、日志信息用于信息发布、打击违法违规行为及防止平台个人信息泄露等功能。点击“同意”，视为您已阅读并同意会计代账宝<a style='color:#2734B4' href='${conf.protocolUrl}/agree/agree.html?type=member_agree&software=${conf.appIdent}'>《用户协议》</a>和<a style='color:#2734B4' href='https://xieyi.5jia.vip/jiazhengtong.html'>《隐私政策》</a>及上述内容。`,

	//使用相机权限告知说明
	kPermission_Tip_Camera_Title: '使用手机存储、相机权限说明',
	kPermission_Tip_Camera_Text: '为您提供上传文件功能，我们会请求您开启手机存储、  相机权限，拒绝提供该权限将无法使用上传文件功能。',

	//使用麦克风权限告知说明
	kPermission_Tip_Micro_Title: '使用麦克风权限说明',
	kPermission_Tip_Micro_Text: '为您提供语音识别工能，我们会请求您开启麦克风权限，拒绝提供该权限将无法使用语音识别功能。',

	//使用电话权限
	SYS_PERMISSION_TIP_CALL_TEL_TITLE: '使用拨打电话权限说明',
	SYS_PERMISSION_TIP_CALL_TEL_TEXT: '为您提供电话拨打功能，我们会请求您开启拨打电话权限，拒绝提供该权限将无法使用该功能。',

	//使用位置权限权限
	kPermission_Tip_Location_Title: '使用定位权限说明',
	kPermission_Tip_Location_Text: '为您提供更精准的家政服务，我们会请求您开启定位限，拒绝提供该权限将无法使用该功能。',

}