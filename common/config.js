const conf = {
	appName: "会计代账宝",
	appIdent: 1001,
	// socketUrl:'ws://192.168.1.50:48080/infra/ws',
	// baseUrl: "http://192.168.1.50:48080",
	socketUrl: 'ws://192.168.31.253:48080/infra/ws',
	baseUrl: "http://192.168.31.253:48080",
	basePath: "/app-api",
	protocolUrl: "http://192.168.31.253:48080",
	imageUrl: "https://jzimg.456app.cn",
	signSecretKey: "zKi4QqVtfAXWO0dycj34RjLCjDwPrH6m",
	aesKey: "UaZc5PFME66lAf2k",
	aesIv: "J849goVPy5sZGhg2",
	defaultShare: {
		path: '/pages/home/<USER>',
		image: ''
	},
};

if (process.env.NODE_ENV === 'development') {
	// 开发环境

} else if (process.env.NODE_ENV === 'production') { 
	// 生产环境
}

// 平台标识
function getPlatform() {
	// #ifdef APP-PLUS
	return 'app';
	// #endif
	// #ifdef MP-WEIXIN
	return 'mpweixin';
	// #endif
	// 默认平台
	return 'unknown';
}
conf.platform = getPlatform()

// 公众号小程序 ID
function getMiniProgramId() {
	// #ifdef APP-PLUS
	// 原始id
	return 'gh_74954a311ff3'
	// #endif
	// #ifdef MP-WEIXIN
	// appid
	return 'wxa338d37f9eec1148'
	// #endif
}
conf.miniProgramId = getMiniProgramId()

// 默认分享的内容
conf.defaultShare.title = conf.appName;
conf.defaultShare.content = conf.appName;
conf.defaultShare.image = `${conf.imageUrl}/${conf.appIdent}/img_min_share.jpg`;

export default conf;