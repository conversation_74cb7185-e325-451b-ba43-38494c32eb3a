import store from '@/store';
import {
	judgeLogin
} from '@/common/login.js';

// 白名单
const whiteList = ["/pages/home/<USER>", "/pages/login/login", "/pages/login/edit-password",
	"/pages/welcome/guide-page", "/pages/welcome/ios-privacy", "/pagesMine/privacy/agreement",
	"/components/dialog/dialog"
];
// NavigateBack白名单
const backWhiteList = ["/pagesMine/privacy/agreement"];
// 路由类型
const routeList = ["navigateTo", "redirectTo", "reLaunch", "switchTab"];


export default function() {

	// 点击跳转时拦截
	routeList.forEach(item => {
		uni.addInterceptor(item, {
			invoke(e) {
				// 调用拦截前
				//1、需要登录操作拦截
				/* 页面路径 */
				let routeUrl = e.url.split('?')[0];
				/* 不需要拦截 */
				if (whiteList.includes(routeUrl)) {
					return e;
				}
				/* 未登录 并且后台设置强制登录 则需要跳转登录 */
				let forceLogin = store.state.forceLogin || false;
				if (!store.getters.getterIsLogin && forceLogin) {
					judgeLogin(() => {
						// 登录成功后的回调函数，继续跳转
						onJump({
							type: item,
							url: e.url
						})
					});
					return false;
				}
				return e;
			},
			fail(e) {
				// 失败回调
			}
		})
	})

	// 返回页面拦截器
	// uni.addInterceptor('navigateBack', {
	// 	invoke(e) {
	// 		// 调用拦截前
	// 		// 页面路径
	// 		let routeUrl = uni.$u.page();
	// 		// 如果有加载弹窗则关闭
	// 		$store.commit('setLoadingShow', false)
	// 		uni.hideLoading()

	// 		// 不需要拦截
	// 		if (backWhiteList.includes(routeUrl)) {
	// 			return e;
	// 		}
	// 		const popupStates = [{
	// 				action: 'setLoginPopupShow',
	// 				value: $store.state.setLoginPopupShow
	// 			}, {
	// 				action: 'setPublishPopupShow',
	// 				value: $store.state.publishPopupShow
	// 			}
	// 			// , {
	// 			// 	action: 'setDecorBookingPopup',
	// 			// 	value: $store.state.decorBookingPopup.show,
	// 			// 	valueObj: true
	// 			// }
	// 		];
	// 		const item = popupStates.find(state => state.value === true);
	// 		if (item) {
	// 			const {
	// 				action,
	// 				valueObj
	// 			} = item;
	// 			$store.commit(item.action, valueObj ? {
	// 				show: false
	// 			} : false)
	// 			return false;
	// 		}
	// 		return e;
	// 	},
	// 	fail(e) {
	// 		// 失败回调
	// 		console.warn(e)
	// 		const pages = getCurrentPages();
	// 		if (pages.length <= 1) {
	// 			uni.reLaunch({
	// 				url: '/pages/home/<USER>'
	// 			});
	// 		}
	// 	}
	// })

	// // 选择资源拦截器
	// uni.addInterceptor('chooseImage', {
	// 	invoke(e) {
	// 		// 调用拦截前
	// 		// 页面路径
	// 		console.warn("拦截选择图片", e)  
	// 		return false;
	// 	},
	// 	fail(e) {
	// 		// 失败回调
	// 	}
	// })
}