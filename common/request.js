import conf from '@/common/config.js';
import cons from '@/common/constant.js';
import store from '@/store';
import md5 from '@/plugins/md5.js'
import {
	judgeLogin
} from '@/common/login.js';
const sys = uni.$u.sys();
const os = uni.$u.os();

// 帐号注销申请中 再次登录此值需设置为true
let continueLogin = false
const loginApis = ['/Alilogin/AlibabaLogin', '/user/mobilelogin', '/user/login', '/wechat/miniLogin', '/user/miniLogin']
// 节流登录标识
var throttleLogin = true

// Loading实例
let LoadingInstance = {
	target: null,
	count: 0,
};

/**
 * 关闭loading
 */
function closeLoading() {
	if (LoadingInstance.count > 0) LoadingInstance.count--;
	if (LoadingInstance.count === 0) uni.hideLoading();
}

// 初始化请求配置，默认全局配置
uni.$u.http.setConfig((config) => {
	config.baseURL = conf.baseUrl + conf.basePath;
	config.header = {
		'Content-Type': 'application/json;charset=UTF-8'
	};
	config.custom = {
		// 显示请求时loading模态框 默认显示
		showLoading: false,
		loadingMsg: '加载中..',
		// 显示操作成功消息 默认不显示
		showSuccess: false,
		successMsg: '',
		// 失败提醒 默认使用后端返回信息
		showError: true,
		errorMsg: '',
		// 是否签名
		sign: true,
		// true:返回的数据成功只返回data  false:返回response
		isFactory: true,
	};
	return config
})

// 请求拦截
uni.$u.http.interceptors.request.use((config) => {
	// 可使用async await 做异步操作
	config.data = config.data || {}

	// 显示 loading
	if (config?.custom?.showLoading) {
		LoadingInstance.count++;
		LoadingInstance.count === 1 &&
			uni.showLoading({
				title: config.custom.loadingMsg,
				mask: true,
				fail: () => {
					uni.hideLoading();
				},
			});
	}
	config.header = {
		...config.header,
		'x-device-type': sys.deviceType, //设备类型 比如
		'x-device-os': sys.osName, // 操作系统名称
		'x-device-os-name': `${sys.osName}/${sys.osVersion || ''}`, //操作系统以及版本
		'x-device-rom-name': `${sys.romName}/${sys.romVersion || ''}`, //ROM系统以及版本
		'x-device-model': `${sys.deviceBrand}/${sys.deviceModel}`, //设备型号
		'x-device-id': sys.deviceId, //设备唯一标识
		'x-useid': store.state.useIdent, //使用APP的唯一标识【每次打开APP的标识】
		'x-app-version': sys.appVersion, //应用版本
		'x-app-build': sys.appVersionCode, //应用构建号
		'x-timestamp': parseInt(Date.now() / 1000),
		'x-nonce': uni.$u.guid(10),
		'x-tenant-id': 1,
		'x-app-code': conf.appIdent,
		'x-terminal': cons.TERMINAL_TYPE(),
	};

	if (!uni.$u.test.isEmpty(store.state.token?.accessToken)) {
		config.header['Authorization'] = store.state.token?.accessToken;
	}

	// 取出所有参数
	// let originalParams = (config.method === 'GET' || config.method === 'UPLOAD') ? config.params : config.data;

	// //*** 注销用户在申请中情况下若继续登录需要传递的参数 ***
	// if (continueLogin && loginApis.includes(config.url)) {
	// 	originalParams.restoreUser = 1
	// 	continueLogin = false
	// }
	// 根据custom参数中配置的是否加签(使用原始参数进行加签)
	// if (config?.custom.sign) {
	// 	// 签名【加签方式如下】(所有参数按照key升序后以盐 + [keyvaluekeyvalue...]方式拼接) + 盐	
	// 	const signParams = {
	// 		...originalParams,
	// 		timestamp: config.header.Timestamp,
	// 		nonce: config.header.Nonce
	// 	};
	// 	const keyValueStr = sortByString(signParams);
	// 	const signStr = `${conf.signSecretKey}${keyValueStr}${conf.signSecretKey.toUpperCase()}`;
	// 	config.header['Sign'] = md5(uni.$u.trim(signStr, 'all')).toLowerCase();

	// console.warn(`[签名信息开始] 【${config.url} 】---》`);
	// console.warn(`[字符串拼接--]  【${signStr}】`)
	// console.warn(`[签名结果----]   【${config.header['Sign']}】`)
	// console.warn(`[签名信息结束] 【${config.url} 】---》`, config);
	// }

	// if (config.method === 'GET') {
	// 	config.params = originalParams;
	// } else {
	// 	config.data = originalParams;
	// }

	// #ifdef APP-PLUS
	// if (plus.networkinfo.isSetProxy()) {
	// 	uni.$u.toast('应用不能运行在网络代理环境');
	// 	return Promise.reject(config);
	// }
	// if (plus.navigator.isRoot()) {
	// 	uni.$u.toast('应用不能运行在越狱或ROOT环境');
	// 	return Promise.reject(config);
	// }
	// #endif

	console.warn(`[请求开始] 【${config.url} 】---》`, config);

	return config
}, config => {
	return Promise.reject(config)
})

// 响应拦截
uni.$u.http.interceptors.response.use((response) => {
	// 关闭加载动画
	// store.commit("SET_LOADING_SHOW", false);
	response.config.custom.showLoading && closeLoading();

	const {
		data,
		config
	} = response;
	const custom = config?.custom;

	console.warn(`[请求结果] 【${config.url} 】---》`, response);

	if (data.code !== 0) {
		if (data.code == 401) {
			// 被迫下线/token失效
			store.dispatch("logoutUser");
			// 1.5秒节流登录
			if (throttleLogin) {
				throttleLogin = false
				setTimeout(() => {
					throttleLogin = true
				}, 1500)
				judgeLogin()
			}
			return Promise.reject(data)
		}
		if (data.code == 202) {
			// 帐号申请注销中
			store.dispatch('logoutUser');
			uni.dialog.confirm("账号注销中", "当前账号注销申请已提交，继续登录则会撤销账号注销申请！", () => {
				continueLogin = true
				judgeLogin()
			}, () => {
				// 取消
			}, '继续登录', '取消');
			return Promise.reject(data)
		}

		if (custom.showError) {
			// 错误提示
			uni.showToast({
				title: data.msg || '服务器开小差啦,请稍后再试~',
				icon: 'none',
				mask: true,
			});
		}
		return Promise.reject(data)
	}

	// 自定义处理【showSuccess 成功提示】：如果需要显示成功提示，则显示成功提示
	if (custom.showSuccess && custom.successMsg !== '' && data.code === 0) {
		uni.showToast({
			title: custom.successMsg,
			icon: 'none',
		});
		console.error("-----", '提示成功')
	}

	return custom.isFactory ? (data.data || {}) : data;

}, (error) => {
	console.warn(`[请求错误] 【${error.config.fullPath}】---》`, error);
	let errorMessage = '请检查网络或服务器'
	if (error !== undefined) {
		switch (error.statusCode) {
			case 400:
				errorMessage = '请求错误';
				break;
			case 401:
				errorMessage = '您的登陆已过期';
				// 正常情况下，后端不会返回 401 错误，所以这里不处理 handleAuthorized
				break;
			case 403:
				errorMessage = '拒绝访问';
				break;
			case 404:
				errorMessage = '请求出错';
				break;
			case 408:
				errorMessage = '请求超时';
				break;
			case 429:
				errorMessage = '请求频繁, 请稍后再访问';
				break;
			case 500:
				errorMessage = '服务器开小差啦,请稍后再试~';
				break;
			case 501:
				errorMessage = '服务未实现';
				break;
			case 502:
				errorMessage = '网络错误';
				break;
			case 503:
				errorMessage = '服务不可用';
				break;
			case 504:
				errorMessage = '网络超时';
				break;
			case 505:
				errorMessage = 'HTTP 版本不受支持';
				break;
		}
		if (error?.errMsg?.includes('timeout')) errorMessage = '请求超时';
	}

	if (error && error?.config) {
		if (error?.config?.custom?.showError) {
			uni.showToast({
				title: error?.data?.msg || errorMessage,
				icon: 'none',
				mask: true,
			});
		}
		error?.config?.custom?.showLoading && closeLoading();
	}
	return Promise.reject(error)
})


/**
 * 按照对象的key进行排序【升序】
 * @param {Object} obj 
 */
function sortByString(obj) {
	const _result = []
	Object.keys(obj).sort().forEach(function(key) {
		const value = obj[key];
		if (uni.$u.test.isEmpty(value)) {
			// 为空的Value设置为空字符
			_result.push(`${key}`)
		} else if (value.constructor === Array || value.constructor === Object) {
			// 如果Value为数组或者对象将值序列化
			_result.push(`${key}${JSON.stringify(value)}`)
		} else {
			// 
			_result.push(`${key}${value}`)
		}
	});
	let str = _result.length ? _result.join('') : '';
	return str;
}

/**
 * 移出对象属性为空的元素
 * @param {Object} obj
 */
function removeProperty(obj) {
	Object.keys(obj).forEach(item => {
		if (obj[item] === '' || obj[item] === undefined || obj[item] === null || obj[item] === 'null')
			delete obj[item]
	})
	return obj
}