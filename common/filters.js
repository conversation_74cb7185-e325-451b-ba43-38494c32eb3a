import Vue from 'vue';

/**
 * 将分转为元
 *
 * @param price 分，例如说 100 分
 * @returns {string} 元，例如说 1.00 元
 */

Vue.filter('fen2yuan', function(price) {
	if (isNaN(price)) return '0.00';
	return (price / 100.0).toFixed(2);
});

/**
 * 将分转为元
 *
 * 如果没有小数点，则不展示小数点
 *
 * @param price 分，例如说 100 分
 * @returns {string} 元，例如说 1 元
 */
Vue.filter('fen2yuanSimple', function(price) {
	if (isNaN(price)) return '0';
	return (price / 100.0).toFixed(2).replace(/\.?0+$/, '');
});

/**
 * 将百分比转化为“打x者” 的x部分
 *
 * @param discountPercent
 */
Vue.filter('formatDiscountPercent', function(discountPercent) {
	if (isNaN(discountPercent)) return '0';
	return (discountPercent / 10.0).toFixed(1).replace(/\.?0+$/, '');
});


/**
 * 价格格式化（返回rich-text结构）
 * @param {Object} options
 * @param {number|string} options.price - 价格
 * @param {string} [options.fontSize='12px'] - 基础字号
 * @param {string} [options.fontWeight='500'] - 基础字重
 * @param {string} [options.highlightSize='24px'] - 高亮字号
 * @param {string} [options.highlightWeight='bold'] - 高亮字重
 * @returns {Array} rich-text节点数组
 */
Vue.filter('formattedPrice', function({
	price,
	fontSize = '12px',
	fontWeight = '500',
	highlightSize = '24px',
	highlightWeight = 'bold'
}) {
	if (isNaN(price)) return '0';
	// 将 price 从字符串转换为数字，如果转换失败，设置为默认值 0.00
	const numericPrice = !isNaN(Number(price)) ? Number(price) : 0.00;
	// 将价格格式化为两位小数
	const [integerPart, decimalPart] = numericPrice.toFixed(2).split('.');
	// 判断是否需要显示小数部分（这里可以保持为 true 以强制显示小数部分）
	// const showDecimal = decimalPart !== '00';
	const showDecimal = true;
	// 返回 JSON 结构
	return [{
			name: 'span',
			attrs: {
				style: `font-size: ${fontSize}; font-weight: ${fontWeight};` // ¥ 的样式
			},
			children: [{
				type: 'text',
				text: '¥' // 货币符号
			}]
		},
		{
			name: 'span',
			attrs: {
				style: `font-size: ${highlightSize}; font-weight: ${highlightWeight};` // 整数部分的样式
			},
			children: [{
				type: 'text',
				text: integerPart // 显示整数部分
			}]
		},
		{
			name: 'span',
			attrs: {
				style: `font-size: ${fontSize}; font-weight: ${fontWeight};` // 小数部分和单位的样式
			},
			children: [{
				type: 'text',
				text: showDecimal ? `.${decimalPart}起` : `起` // 根据条件显示小数部分
			}]
		}
	];
});

/**
 * 局部天数格式化过滤器
 * @param {number} days - 要格式化的天数
 * @returns {string} 格式化后的字符串
 */
Vue.filter('formatDays', function(days) {
	if (!days) return '0天';

	if (days < 30) {
		return `${days}天`;
	} else if (days < 90) {
		const months = Math.floor(days / 30);
		return `${months}月`;
	} else if (days < 365) {
		const quarters = Math.floor(days / 90);
		return `${quarters}季度`;
	} else {
		const years = Math.floor(days / 365);
		const remainingDays = days % 365;
		if (remainingDays > 0) {
			return `${years}年${remainingDays}天`;
		} else {
			return `${years}年`;
		}
	}
});