import {
	judgeLogin
} from '@/common/login.js';

/**
 * 页面跳转
 * @param {Object} options - 配置参数
 * @param {string} options.type - 跳转方式
 * @param {string} options.url - 跳转地址
 * @param {Object} options.params - 传递参数
 * @param {Object} options.events - Event
  * @param {Object} options.animationType - 动画类型
 * @param {boolean} options.login - 是否需要登录
 */
function onJump({
	type = 'navigateTo',
	url,
	params = {},
	events = {},
	animationType = 'pop-in',
	login = false
}) {
	if (login) {
		judgeLogin(() => {
			executePage({
				type: type,
				url: url,
				params: params,
				events: events,
				animationType:animationType
			})
		})
	} else {
		executePage({
			type: type,
			url: url,
			params: params,
			events: events,
			animationType:animationType
		})
	}
}

/**
 * 执行页面跳转
 * @param {Object} options - 配置参数
 * @param {string} options.type - 跳转方式
 * @param {string} options.url - 跳转地址
 * @param {Object} options.params - 传递参数
 * @param {Object} options.events - Event
 * @param {Object} options.animationType - 动画类型
 */
function executePage({
	type = 'navigateTo',
	url,
	params = {},
	events = {},
	animationType = 'pop-in'
}) {
	const path = url + uni.$u.queryParams(params);
	switch (type) {
		case 'switchTab':
			uni.switchTab({
				url: path
			});
			break;
		case 'redirectTo':
			uni.redirectTo({
				url: path
			});
			break;
		case 'reLaunch':
			uni.reLaunch({
				url: path
			});
			break;
		default:
			uni.navigateTo({
				url: path,
				animationType:animationType,
				events: events
			});
			break;
	}
}


/**
 * 返回页面
 * 返回上一页，只有一页的时候返回主页
 * @param {Object} options - 配置参数
 * @param {number} options.delta - 返回的页面层级
 */
function onBack({
	delta = 1
} = {}) {
	const pages = getCurrentPages();
	if (pages.length > 1) {
		uni.navigateBack({
			delta
		});
	} else {
		uni.reLaunch({
			url: '/pages/home/<USER>'
		});
	}
}

export {
	onJump,
	onBack
}