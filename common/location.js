import store from '@/store';
import cons from '@/common/constant.js'
import SysPermission, {
	checkOpenGPS
} from '@/common/sysPermission.js'
// 地图密钥管理器类
class ApiKeyManager {
	constructor(apiProviders) {
		this.apiProviders = apiProviders;
		this.currentProviderType = Object.keys(apiProviders)[0]; // 默认使用第一个提供商
		this.currentApiKeyIndex = 0; // 默认使用第一个密钥索引
		this.currentApiKey = apiProviders[this.currentProviderType][0]; // 默认使用第一个密钥
	}
	// 获取下一个密钥
	getNextApiKey(providerType = this.currentProviderType, apiKeyIndex = this.currentApiKeyIndex) {
		const keys = this.apiProviders[providerType];
		const apiKey = keys[apiKeyIndex];
		apiKeyIndex = (apiKeyIndex + 1) % keys.length;
		if (apiKeyIndex === 0) {
			const providerTypes = Object.keys(this.apiProviders);
			providerType = providerTypes[(providerTypes.indexOf(providerType) + 1) % providerTypes.length];
		}
		this.currentProviderType = providerType;
		this.currentApiKeyIndex = apiKeyIndex;
		this.currentApiKey = apiKey;
		return {
			providerType: providerType,
			apiKeyIndex: apiKeyIndex,
			apiKey: apiKey
		};
	}
	// 获取当前的 API 密钥信息
	getCurrentApiKey() {
		return {
			providerType: this.currentProviderType,
			apiKeyIndex: this.currentApiKeyIndex,
			apiKey: this.currentApiKey
		};
	}
}

// 创建密钥管理器实例
const apiKeyManager = new ApiKeyManager({
	amap: ['da2832bd38777e008a6d4c11aa122dda'],
	tencent: [],
	baidu: []
});

// 通用的 API 请求函数
const makeApiRequest = (url, data) => {
	console.warn('\n\n');
	console.warn('地址服务->', `[请求密钥]：${data.key}--[请求地址]：${url}`);
	console.warn('地址服务->请求参数:', data);
	return new Promise((resolve, reject) => {
		uni.request({
			url: url,
			data: data,
			success: (res) => {
				console.warn('地址服务->返回结果:', res.data);
				if (res.statusCode === 200 && (res.data.info === 'OK' || res.data.info ===
						'ok')) {
					resolve(res.data);
				} else {
					reject(new Error(res.data.info || 'Request failed'));
				}
			},
			fail: (error) => {
				console.error('地址服务->请求失败:', error);

				reject(new Error(error.errMsg || '请求失败'));
			}
		});
	});
};

// 处理请求并动态切换 API 密钥和提供商
const handleRequest = async (url, data) => {
	const providerTypes = Object.keys(apiKeyManager.apiProviders);
	let retries = providerTypes.length * Math.max(...providerTypes.map(type => apiKeyManager.apiProviders[type]
		.length)); // 总重试次数为提供商数量乘以每个提供商的最大密钥数量

	while (retries > 0) {
		try {
			const {
				providerType,
				apiKeyIndex,
				apiKey
			} = apiKeyManager.getCurrentApiKey();
			const res = await makeApiRequest(url, {
				...data,
				key: apiKey
			});
			return res;
		} catch (error) {
			console.warn(`请求失败: ${error.message}. 正在尝试下一个密钥或提供商...`);
			retries--;
			apiKeyManager.getNextApiKey(apiKeyManager.currentProviderType, apiKeyManager.currentApiKeyIndex);
		}
	}
	throw new Error('错误：所有提供程序中的所有API密钥都已用完.');
};


/**
 * 坐标转换
 * @param {Object} params 包含经度和纬度的对象 { lng: 经度, lat: 纬度 }
 */
export const coordinateConvert = async (params) => {
	if (!params || !params.lng || !params.lat) {
		throw new Error('参数错误：经度和纬度坐标不能为空');
	}
	const url = 'https://restapi.amap.com/v3/assistant/coordinate/convert';
	const data = {
		locations: `${params.lng},${params.lat}`,
		coordsys: 'gps',
		output: 'json'
	};
	const result = await handleRequest(url, data);
	const {
		locations
	} = result;
	return {
		lat: locations.split(',')[1],
		lng: locations.split(',')[0]
	};
};
// 逆地理编码
export const regeocode = async (params) => {
	if (!params || !params.lng || !params.lat) {
		throw new Error('参数错误：经度和纬度坐标不能为空');
	}
	const url = 'https://restapi.amap.com/v3/geocode/regeo';
	const data = {
		location: `${params.lng},${params.lat}`,
		radius: '500',
		extensions: 'all'
	};
	console.warn("逆地理编码参数", data)
	const result = await handleRequest(url, data);
	const {
		addressComponent,
		formatted_address,
		pois
	} = result.regeocode;
	const poi = pois[0]; // 取第一个兴趣点
	return {
		formatAddress: formatted_address,
		provinceId: '',
		provinceName: addressComponent.province,
		cityId: '',
		cityName: addressComponent.city,
		districtId: addressComponent.adcode,
		districtName: addressComponent.district,
		townshipName: addressComponent.township,
		townshipCode: addressComponent.towncode,
		name: poi.name,
		address: poi.address,
		distance: poi.distance,
		lat: poi.location.split(',')[1],
		lng: poi.location.split(',')[0],
		pois: pois.map((e) => ({
			provinceId: '',
			provinceName: addressComponent.province,
			cityId: '',
			cityName: addressComponent.city,
			districtId: addressComponent.adcode,
			districtName: addressComponent.district,
			townshipName: addressComponent.township,
			townshipCode: addressComponent.towncode,
			name: e.name,
			address: e.address,
			distance: e.distance,
			lat: e.location.split(',')[1],
			lng: e.location.split(',')[0]
		}))
	};
};

/**
 * 周边检索
 * @param {Object} params 包含检索条件的对象 { lng: 经度, lat: 纬度, pageSize: 每页数量, pageNum: 页码 }
 */
export const placeAround = async (params) => {
	if (!params || !params.lng || !params.lat) {
		throw new Error('参数错误：经度和纬度坐标不能为空');
	}
	const url = 'https://restapi.amap.com/v5/place/around';
	const data = {
		location: `${params.lng},${params.lat}`,
		radius: '10000',
		page_size: params.pageSize,
		page_num: params.pageNum,
	};
	const result = await handleRequest(url, data);
	const {
		pois
	} = result;
	const newPois = pois.map((e) => ({
		provinceId: e.pcode,
		provinceName: e.pname,
		cityId: '',
		cityName: e.cityname,
		districtId: e.adcode,
		districtName: e.adname,
		name: e.name,
		address: e.address,
		distance: e.distance,
		lat: e.location.split(',')[1],
		lng: e.location.split(',')[0]
	}));
	return newPois;
};

/**
 * 关键词检索v1 【使用本地 JS 实现】
 * @param {Object} params 包含检索条件的对象 { keywords: 关键词, city: 城市名称, pageSize: 每页数量, pageNum: 页码 }
 */
export const placeSearch = async (params) => {
	if (!params || !params.lng || !params.lat) {
		throw new Error('参数错误：经度和纬度坐标不能为空');
	}
	const url = 'https://restapi.amap.com/v5/place/text';
	const data = {
		keywords: params.keywords,
		city_limit: true,
		region: params.city,
		page_size: params.pageSize,
		page_num: params.pageNum,
	};
	const result = await handleRequest(url, data);
	const {
		pois
	} = result;
	const newPois = pois.map((e) => ({
		provinceId: e.pcode,
		provinceName: e.pname,
		cityId: '',
		cityName: e.cityname,
		districtId: e.adcode,
		districtName: e.adname,
		name: e.name,
		address: e.address,
		distance: e.distance,
		lat: e.location.split(',')[1],
		lng: e.location.split(',')[0]
	}));
	return newPois;
};
/**
 * 关键词检索v2 【使用服务端 API 实现】
 * @param {Object} params 包含检索条件的对象 { keywords: 关键词, region: 城市名称/城市code/区域code, pageSize: 每页数量, pageNum: 页码 }
 */
export const placeSearch2 = (params) => {
	return new Promise((resolve, reject) => {
		const data = {
			keyword: params.keywords || '',
			city_limit: params.cityLimit || '0',
			city_id: params.region || '',
			// page_size: params.pageSize,
			// page_num: params.pageNum,
		};
		uni.$u.http.get('/map/search', {
			params: data
		}).then(res => {
			resolve(res);
		}).catch(err => {
			reject(err);
		});
	});
};


// 获取小程序定位地址
// #ifdef MP
const getMpAddress = (successCallback, errCallback) => {
	uni.authorize({
		scope: 'scope.userLocation',
		success: () => {
			uni.getSetting({
				success: res => {
					if (res.authSetting['scope.userLocation']) {
						uni.getLocation({
							type: 'wgs84',
							isHighAccuracy: true,
							success: res => {
								// 第一种方案：使用本地 JS 实现
								// regeocode(res).then((regeoRes) => {
								// 	console.log('最终定位结果：', regeoRes);
								// 	successCallback && successCallback(
								// 		regeoRes);
								// }).catch((regeoErr) => {
								// 	console.error('逆地理编码失败：', regeoErr);
								// 	errCallback && errCallback(
								// 		regeoErr);
								// });
								// 第二种方案：使用服务端 API 实现
								_getAddress({
									lat: res.latitude || '',
									lng: res.longitude || ''
								}, successCallback, errCallback)
							},
							fail: (err) => {
								console.error('[小程序]getLocation定位失败:', err);
								// 错误码说明:
								// 'unauthorized' - 未授权位置权限，请在设置中授权后重试。
								// 'service_disabled' - 位置服务未开启，请在设备设置中开启定位服务。
								// 'location_unavailable' - 无法获取位置，可能是由于信号问题或设备环境。
								// 'timeout' - 请求超时，请重试获取位置。
								// 'unknown' - 未知错误，具体信息在 err 对象中。
								let errCode = 'fail'
								switch (err.errCode) {
									case 1:
										errCode = 'unauthorized';
										break;
									case 2:
										errCode = 'service_disabled';
										break;
									case 3:
										errCode = 'location_unavailable';
										break;
									case 4:
										errCode = 'timeout';
										break;
									default:
										errCode = 'unknown';
								}
								errCallback({
									code: `location_${errCode}`,
									msg: err.errMsg
								});
							}
						});
					} else {
						errCallback({
							code: 'getsetting_not_declared',
							msg: '小程序]位置信息未声明'
						});
						uni.showModal({
							title: '提示',
							content: '请先在设置页面打开“位置信息”使用权限',
							confirmText: '去设置',
							cancelText: '再逛会',
							success: res => {
								if (res.confirm) {
									uni.openSetting();
								}
							}
						});
					}
				}
			});
		},
		fail: (error) => {
			console.error('【[小程序】authorize授权定位失败:', error);
			// 错误码说明:
			// denied - 用户拒绝了权限，请在设置中手动授权。
			// unauthorized - 未授权位置信息，请授权后重试。
			// unknown - 其他错误。
			let errCode = 'fail'
			if (error.errMsg.includes('auth deny')) {
				errCode = 'denied';
			} else if (error.errMsg.includes('scope.userLocation')) {
				errCode = 'unauthorized';
			} else {
				errCode = 'unknown';
			}
			errCallback({
				code: `authorize_${errCode}`,
				msg: error.errMsg
			});
		}
	});
}
// #endif

// 获取APP定位地址信息
// #ifdef APP-PLUS
const getAppAddress = (successCallback, errorCallback) => {
	uni.getLocation({
		type: 'wgs84',
		// type: 'gcj02',
		isHighAccuracy: true,
		success: (res) => {
			console.error('定位信息[CurrentPosition]:', res);
			// 第一种方案：使用本地 JS 实现
			// 获取到位置后进行坐标转换
			// coordinateConvert({
			// lat: res.latitude,
			// lng: res.longitude
			// }).then((convertedCoords) => {
			// 	// 坐标转换成功后进行逆地理编码
			// 	// regeocode(convertedCoords).then(successCallback).catch(errorCallback); 
			// }).catch((convertErr) => {
			// 	console.error('坐标转换失败：', convertErr);
			// 	errorCallback && errorCallback(null);
			// });

			// 第二种方案：使用服务端 API 实现 
			_getAddress({
				lat: res.latitude || '',
				lng: res.longitude || ''
			}, successCallback, errorCallback)
		},
		fail: (error) => {
			console.error('------定位失败:', error);
			errorCallback && errorCallback(error?.message || '定位失败');
		}
	});
}
// #endif

// 调用服务端api将经纬度解析为地址
const _getAddress = (params, successCallback, errorCallback) => {
	uni.$u.http.get('/map/regeo', {
		params: {
			lat: params.lat,
			lng: params.lng
		}
	}).then(res => {
		successCallback(res);
	}).catch(err => {
		errorCallback(err);
	});
}

// 获取地址信息 使用IP定位
export const getLocationByIp = ({
	force = false
} = {}) => {
	return new Promise((resolve, reject) => {
		const selectAddress = store.state.selectedAddress;
		const curAddress = store.state.currentAddress;

		// 地址验证函数
		const isAddressValid = (address) =>
			!uni.$u.test.isEmpty(address?.cityName);

		if (!force) {
			// 优先返回已选择的地址
			if (isAddressValid(selectAddress)) {
				resolve(selectAddress);
				return; // 如果 selectAddress 有效，直接返回结果
			}

			// 如果 selectAddress 无效，则检查当前地址
			if (isAddressValid(curAddress)) {
				store.commit("SET_SELECTED_ADDRESS", curAddress);
				resolve(curAddress);
				return; // 如果 selectAddress 为空但 curAddress 有值，则返回 curAddress
			}
		}

		uni.$u.http.get('/system/area/get-by-ip').then(res => {    
			const addressInfo = {
				provinceId: res.provinceId,
				provinceName: res.provinceName, 
				cityId: res.cityId,
				cityName: res.cityName,
				districtId: res.areaId,
				districtName: res.areaName,
			};
			store.commit("SET_CURRENT_ADDRESS", addressInfo);
			store.commit("SET_SELECTED_ADDRESS", addressInfo);
			resolve(addressInfo);
		}).catch(err => {
			reject(err);
		});
	});
}

// 获取地址信息 使用GPS定位
export const getLocationByGps = ({
	tip = true,
	force = false
} = {}) => {
	return new Promise((resolve, reject) => {
		const GPSOpen = checkOpenGPS(false);
		const selectAddress = store.state.selectedAddress;
		const curAddress = store.state.currentAddress;

		const isAddressValid = (address) =>
			GPSOpen &&
			!uni.$u.test.isEmpty(address?.cityName) &&
			!uni.$u.test.isEmpty(address?.lat) &&
			!uni.$u.test.isEmpty(address?.lng);
		const isAddressValid1 = (address) =>
			!uni.$u.test.isEmpty(address?.cityName);

		if (!force) {
			// 如果不强制定位，优先返回已保存的地址（选择的城市是没有经纬度的）
			if (isAddressValid1(selectAddress)) {
				resolve(selectAddress);
				return; // 优先取selectAddress，直接返回结果
			}
			if (isAddressValid(curAddress)) {
				store.commit("SET_SELECTED_ADDRESS", curAddress);
				resolve(curAddress);
				return; // 如果selectAddress为空，但currentAddress有值，则直接返回currentAddress
			}
		}
		GPSLocation({
			tip
		}).then(res => {
			store.commit("SET_CURRENT_ADDRESS", res);
			store.commit("SET_SELECTED_ADDRESS", res);
			resolve(res);
		}).catch(error => {
			reject(error);
			if (tip) {
				uni.showToast({
					title: error,
					icon: "none"
				});
			}
		})
	});
}

// 根据GPS定位
export const GPSLocation = ({
	tip = true,
} = {}) => {
	return new Promise((resolve, reject) => {
		const successCallback = (res) => {
			console.error("GPS定位得到结果", res)
			resolve(res);
		};
		const errCallback = (err) => {
			reject(err);
			if (tip) {
				uni.showToast({
					title: err,
					icon: "none"
				});
			}
		};
		// #ifdef MP
		getMpAddress(successCallback, errCallback);
		// #endif
		// #ifdef APP-PLUS
		SysPermission.judgePermission('location', cons.SYS_PERMISSION_TIP_LOCATION_TITLE,
			cons.SYS_PERMISSION_TIP_LOCATION_TEXT, (result) => {
				if (result == 1) {
					// 已授权
					getAppAddress(successCallback, errCallback);
				} else {
					reject(null);
				}
			});

		// #endif
	});
}