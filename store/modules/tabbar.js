export const state = {
	// --------TabBar数据----------
	// pagePath： 页面路径
	// iconPath： 默认图标
	// selectedIconPath： 激活图标
	// text： 提示文字
	// count：红色角标显示的数字，如果需要移除角标，配置此参数为0即可
	// isDot： 如果配置此值为true，那么角标将会以红点的形式显示
	tabbarList: [{
			ident: 'home',
			pagePath: "pages/home/<USER>",
			iconPath: "/static/tabbar/ic_tabbar_home_nor.png",
			selectedIconPath: "/static/tabbar/ic_tabbar_home_high.png",
			count: 0,
			isDot: true,
			text: "首页"
		},
		{
			ident: 'service',
			pagePath: "pages/category/index",
			iconPath: "/static/tabbar/ic_tabbar_caregory_nor.png",
			selectedIconPath: "/static/tabbar/ic_tabbar_caregory_high.png",
			count: 0,
			isDot: true,
			text: "分类"
		},
		{
			ident: 'publish',
			pagePath: "",
			iconPath: "/static/tabbar/ic_tabbar_publish.png",
			selectedIconPath: "/static/tabbar/ic_tabbar_publish.png",
			mid: true,
			count: 0,
			isDot: false,
			text: ""
		},
		{
			ident: 'customer',
			pagePath: "pages/customer/index",
			iconPath: "/static/tabbar/ic_tabbar_customer_nor.png",
			selectedIconPath: "/static/tabbar/ic_tabbar_customer_high.png",
			count: 0,
			isDot: true,
			text: "客源"
		},
		{
			ident: 'mine',
			pagePath: "pages/mine/index",
			iconPath: "/static/tabbar/ic_tabbar_mine_nor.png",
			selectedIconPath: "/static/tabbar/ic_tabbar_mine_high.png",
			count: 0,
			isDot: true,
			text: "我的"
		}
	]
};
export const getters = {

};
export const mutations = {
	// 设置TabBar角标为点
	SET_TABBAR_BADGE(state, data) {
		let {
			index = 0, count = 0, isDot = false
		} = data;
		if (index >= 0 && index < state.tabbarList.length) {
			state.tabbarList[index].count = count
		}
	},

	// 清除tabbar角标
	REMOVE_TABBAR_BADGE(state, data) {
		let {
			index = 0
		} = data;
		if (index >= 0 && index < state.tabbarList.length) {
			state.tabbarList[index].count = 0
		}
	},
};
export const actions = {

};