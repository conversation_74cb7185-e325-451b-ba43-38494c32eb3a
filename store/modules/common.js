// 定义状态和缓存数据名称
export const state = {
	loginPopup: { // 控制是否打开登录弹窗
		show: false,
		callback: null
	},
	loadingShow: false, // 加载动画
	chatScenesInfo: {}, // 扫码参数
	currentRouter: '', // 当前路径
	noticeSettingPopupShow: false, // 通知权限设置
	notifyPopup: { // 应用内通知
		show: false
	},
	useIdent: '', // 每次使用APP的唯一标识
	isDark: false, // 是否夜晚模式
	isPaymentDisabled: false, // 是否关闭支付功能
	isAppLaunchDisabled: false, // 是否关闭应用相互拉起功能
	publishPopupShow: false, // 发布弹窗
	publishFormPopup: { // 发布表单弹窗
		show: false
	},
	currentAddress: {}, // 当前位置 格式：{provinceName: "",provinceId: "",cityName: "",cityId: "",districtName: "",districtId: ""}
	selectedAddress: {} // 用户主动当选择的位置,默认和当前位置（既定位）一致
};

// 缓存的数据KEY
const cacheNameList = ["isDark", "token", "userInfo", "currentAddress", "selectedAddress"];

export const mutations = {

	// 从缓存中获取数据（打开APP时调用）
	SET_CACHE_DATA(state) {
		for (let name of cacheNameList) {
			let data = uni.getStorageSync(name);
			if (data !== null) {
				state[name] = data;
			}
		}
	},

	// 设置登录弹窗显示状态
	SET_LOGIN_POPUP(state, data) {
		state.loginPopup = data;
	},

	// 设置加载动画显示状态
	SET_LOADING_SHOW(state, data) {
		state.loadingShow = data;
	},

	// 设置扫码参数
	SET_CHAT_SCENES_INFO(state, data) {
		state.chatScenesInfo = data;
	},

	// 设置当前路径
	SET_CURRENT_ROUTER(state, data) {
		state.currentRouter = data;
	},

	// 通知权限弹窗
	SET_NOTICE_SETTING_POPUP_SHOW(state, data) {
		state.noticeSettingPopupShow = data
	},

	// 应用内通知弹窗
	SET_NOTIFY_POPUP(state, data) {
		state.notifyPopup = data
	},

	// 发布弹窗
	SET_PUBLISH_POPUP_SHOW(state, data) {
		state.publishPopupShow = data
	},

	// 发布表单弹窗
	SET_PUBLISH_FORM_POPUP(state, data) {
		state.publishFormPopup = data
	},

	// 每次使用APP的唯一标识
	SET_USE_IDENT(state, data) {
		state.useIdent = data
	},

	// 设置夜晚模式
	SET_IS_DARK(state, data) {
		state.isDark = data;
		uni.setStorageSync('isDark', state.isDark); // 更新夜晚模式到本地缓存
	},

	// 设置关闭支付功能
	SET_IS_PAYMENT_DISABLED(state, data) {
		state.isPaymentDisabled = data;
	},

	// 设置关闭应用相互拉起功能
	SET_IS_APP_LAUNCH_DISABLED(state, data) {
		state.isAppLaunchDisabled = data;
	},

	// 系统自动定位的用户位置
	SET_CURRENT_ADDRESS(state, data) {
		if (data) {
			state.currentAddress = data
			// #ifdef H5
			window.sessionStorage.setItem('currentAddress', JSON.stringify(data));
			// #endif
			// #ifndef H5
			uni.setStorageSync('currentAddress', data);
			// #endif
		}
	},

	// 用户主动选择的位置
	SET_SELECTED_ADDRESS(state, data) {
		if (data) {
			state.selectedAddress = data
			// #ifdef H5
			window.sessionStorage.setItem('selectedAddress', JSON.stringify(data));
			// #endif
			// #ifndef H5
			uni.setStorageSync('selectedAddress', data);
			// #endif
		}
	}
};

export const actions = {

};

export const getters = {

};