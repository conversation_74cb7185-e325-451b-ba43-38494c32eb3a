// store/modules/chat.js

import chatConstants from '@/pagesChat/utils/constants.js';
import {
	initLocalChatDB,
	saveLocalConversation,
	getAllLocalConversations,
	getLocalConversationByNo,
	saveLocalMessage,
	getLocalMessagesByConversationNo
} from '@/pagesChat/utils/chatDB.js';
import {
	generateConversationNo
} from '@/pagesChat/utils/tool.js';

export const state = {
	/** 所有会话列表（本地或服务端加载） */
	conversationList: [],

	/** 输入框中的文字内容 */
	inputText: '',

	/**
	 * 当前正在聊天的会话编号（conversationNo）
	 * 用于判断当前用户是否处于某个会话中，从而决定是否：
	 * - 清除未读数
	 * - 实时追加消息到聊天页面
	 * - 聊天页是否显示消息发送状态
	 * - 避免收到消息后误计入未读数
	 *
	 * 应在进入聊天页面时设置，在退出时清空。
	 */
	currentConversationNo: null
};

export const getters = {
	getConversationList: state => state.conversationList,
	getInputText: state => state.inputText,
	getCurrentConversationNo: state => state.currentConversationNo
};

export const mutations = {
	/** 设置会话列表 */
	SET_CONVERSATION_LIST(state, list) {
		state.conversationList = list;
	},

	/** 添加会话 */
	ADD_CONVERSATION(state, conversation) {
		state.conversationList.push(conversation);
	},

	/** 替换指定索引的会话 */
	REPLACE_CONVERSATION(state, {
		index,
		conversation
	}) {
		state.conversationList.splice(index, 1, conversation);
	},

	/** 设置输入框内容 */
	SET_INPUT_TEXT(state, text) {
		state.inputText = text;
	},

	/** 设置当前会话编号 */
	SET_CURRENT_CONVERSATION_NO(state, no) {
		state.currentConversationNo = no;
	},

	/** 清空当前会话编号 */
	CLEAR_CURRENT_CONVERSATION_NO(state) {
		state.currentConversationNo = null;
	},

	/** 清空某个会话的未读消息数 */
	CLEAR_UNREAD_COUNT(state, conversationNo) {
		const index = state.conversationList.findIndex(c => c.no === conversationNo);
		if (index !== -1) {
			state.conversationList[index].unreadMessagesCount = 0;
		}
	}
};

export const actions = {
	/** 初始化本地数据库（App 端 SQLite） */
	async initLocalChatDB() {
		await initLocalChatDB();
	},

	/**
	 * 进入聊天会话（进入页面时调用）
	 * 1. 通知后端进入会话（查/建会话并推送FAQ）
	 * 2. 拉取历史消息
	 * 3. 设置当前会话编号
	 */
	async enterConversation({
		commit,
		dispatch,
		state
	}, {
		targetId,
		type
	}) {
		// 1. 通知后端进入会话
		await uni.$u.http.post('/im/conversation/enter', {
			targetId,
			type
		});

		// // 2. 生成会话编号
		// const conversationNo = generateConversationNo(state.userInfo.id, targetId, type);

		// // 3. 拉取历史消息
		// await dispatch('getLocalMessagesByConversationNo', conversationNo);

		// // 4. 设置当前会话
		// commit('SET_CURRENT_CONVERSATION_NO', conversationNo);
	},

	/**
	 * 发送消息（乐观 UI）
	 * 1. 本地写入消息
	 * 2. HTTP 请求发送消息
	 * 3. 更新会话列表中最后一条消息与状态
	 */
	async sendMessage({
		state,
		commit,
		dispatch
	}, message) {
		// 本地写入消息（状态为 sending）
		await saveLocalMessage(message);

		const conversation = await getLocalConversationByNo(message.conversationNo)
		if (!conversation) {
			// 发送消息前创建会话
			await dispatch('createConversation', {
				targetId: message.receiverId,
				type: message.conversationType,
				message: message
			});
		}

		try {
			const res = await uni.$u.http.post('/im/message/send', message);
			const successMsg = {
				...message,
				id: res.id,
				sendTime: res.sendTime,
				sendStatus: chatConstants.SEND_STATUS.SUCCESS
			};
			await saveLocalMessage(successMsg); // 更新本地状态为成功

			// 更新会话列表里的最后消息
			const index = state.conversationList.findIndex(item => item.no === successMsg.conversationNo);
			if (index >= 0) {
				const updatedConv = {
					...state.conversationList[index],
					lastMsg: successMsg
				};
				commit('REPLACE_CONVERSATION', {
					index: index,
					conversation: updatedConv
				});
				await saveLocalConversation(updatedConv); // 更新本地会话信息
			}
			return successMsg;
		} catch (error) {
			const failedMsg = {
				...message,
				sendStatus: chatConstants.SEND_STATUS.FAILURE
			};
			await saveLocalMessage(failedMsg);

			return failedMsg;
		} finally {
			commit('SET_INPUT_TEXT', '');
		}
	},

	/**
	 * 接收服务器推送的消息
	 * 1. 判断会话是否存在
	 * 2. 存入本地消息
	 * 3. 更新未读数 / 会话列表
	 */
	async receiveMessage({
		state,
		commit,
		dispatch
	}, message) {
		console.error("[receiveMessage]-message", message)
		const conversationNo = generateConversationNo(
			message.senderId,
			message.receiverId,
			message.conversationType
		);

		const idx = state.conversationList.findIndex(item => item.no === conversationNo);
		const isCurrent = state.currentConversationNo === conversationNo;

		// 写入本地消息表
		await saveLocalMessage({
			...message,
			conversationNo
		});

		if (idx === -1) {
			// await dispatch('createConversation', {
			//   targetId: message.senderId,
			//   conversationType: message.conversationType,
			//   avatar: message.avatar,
			//   nickname: message.nickname
			// });
			// 新建会话
			const newConv = {
				no: conversationNo,
				// userId: message.senderId,// 会话所属用户 
				targetId: message.senderId, // 会话不是自己创建的，是自己接受到的消息（所以对自己而言聊天对象就是对方）
				type: message.conversationType,
				avatar: message.avatar,
				nickname: message.nickname,
				lastMsg: message,
				unreadMessagesCount: isCurrent ? 0 : 1,
				updateTime: message.sendTime
			};
			console.error("[receiveMessage]-创建会话", newConv)
			commit('ADD_CONVERSATION', newConv);
			await saveLocalConversation(newConv); // 本地持久化
		} else {
			// 更新已有会话
			const oldConv = state.conversationList[idx];
			const updated = {
				...oldConv,
				avatar: message.avatar, // 头像单独更新一下
				nickname: message.nickname, // 昵称单独更新一下
				lastMsg: message,
				unreadMessagesCount: isCurrent ?
					0 : (oldConv.unreadMessagesCount ?? 0) + 1
			};
			console.error("已有的绘画", updated)
			commit('REPLACE_CONVERSATION', {
				index: idx,
				conversation: updated
			});
			await saveLocalConversation(updated);
		}
	},

	/** 接收服务器推送的新会话 */
	async receiveConversation({
		commit
	}, conversation) {
		commit('ADD_CONVERSATION', conversation);
	},

	/**
	 * @desc 确保进入聊天页面前已有对应会话
	 *
	 * 从用户详情页、陌生人页面等非会话入口进入聊天时调用。
	 * 不依赖网络是否可用，始终生成并返回 conversationNo。
	 * 若本地不存在该会话，则尝试远程创建（失败也不影响跳转）。
	 *
	 * 使用场景：页面跳转前调用，确保聊天页能正常展示历史消息和输入框等。
	 */
	async ensureConversationBeforeChat({
		state,
		dispatch
	}, {
		targetId,
		type,
		avatar,
		nickname
	}) {
		const conversationNo = generateConversationNo(state.userInfo.id, targetId, conversationType);

		// 1. 本地是否已存在
		const exists = state.conversationList.find(c => c.no === conversationNo);
		if (exists) return conversationNo;

		try {
			// 2. 尝试服务端创建会话（如已存在会直接返回）
			await dispatch('createConversation', {
				targetId,
				type,
				avatar,
				nickname
			});
		} catch (error) {
			console.warn('[ensureConversationBeforeChat] 会话创建失败:', error);
			// 3. 即使失败也返回编号，让聊天页面能继续使用
		}

		return conversationNo;
	},

	/**
	 * 根据消息创建会话（WebSocket 首次消息创建）
	 */
	async createConversation({
		dispatch
	}, {
		targetId,
		type,
		message
	}) {
		try {
			const res = await uni.$u.http.post('/im/conversation/create', {
				targetId,
				type
			});

			const localConversation = {
				...res,
				avatar: message.avatar,
				nickname: message.nickname,
				updateTime: res.updateTime,
				targetId: res.targetId,
				senderId: res.userId,
				no: res.no,
				type: res.conversationType,
				unreadMessagesCount: res.unreadMessagesCount,
				description: res.lastMessageDescription,
				lastMsg: message
			};

			await saveLocalConversation(localConversation);
			await dispatch('getAllLocalConversations');
		} catch (error) {
			console.error('[createConversation]', error);
		}
	},

	/** 获取本地缓存会话列表 */
	async getConversationList({
		commit,
		dispatch,
		state
	}) {
		try {
			const localList = await getAllLocalConversations();
			commit('SET_CONVERSATION_LIST', localList);
		} catch (e) {
			console.error('[getConversationList]', e);
		} finally {
			if (state.conversationList.length === 0) {
				await dispatch('getConversationListFromServer');
			}
		}
	},

	/** 从服务器拉取会话 */
	async getConversationListFromServer({
		commit
	}) {
		try {
			const res = await uni.$u.http.get('/im/conversation/list');
			const list = res.map(item => ({
				...item,
				updateTime: item.lastReadTime,
				name: item.targetId,
				targetId: item.targetId,
				senderId: item.userId,
				conversationNo: item.no,
				type: item.conversationType,
				unreadMessagesCount: item.unreadMessagesCount,
				description: item.lastMessageDescription,
				lastMsg: {}
			}));
			commit('SET_CONVERSATION_LIST', list);
			list.forEach(saveLocalConversation); // 缓存到本地
		} catch (e) {
			console.error('[getConversationListFromServer]', e);
		}
	},

	/** 获取某个会话的全部消息记录 */
	async getLocalMessagesByConversationNo(_, conversationNo) {
		try {
			return await getLocalMessagesByConversationNo(conversationNo);
		} catch (err) {
			console.error('获取消息失败', err);
			return [];
		}
	}
};