// #ifdef APP-PLUS
import aliyunPushHelper from '@/common/aliPush.js';
// #endif

// 定义状态和缓存数据名称
export const state = {
	token: {}, // 用户 token，假设初始值为 null
	userInfo: {}, // 用户信息，假设初始值为 null
	userVip: {}, // VIP信息，假设初始值为 null<|endoftext|>

	sysConf: {}, // App配置数据
};

export const mutations = {

	// 设置用户 token
	SET_TOKEN(state, token) {
		state.token = token;

		uni.setStorageSync('token', token);
	},

	// 设置用户信息
	SET_USER_INFO(state, data) {
		state.userInfo = data;
		uni.setStorageSync('userInfo', data);
	},

	// 设置VIP信息
	SET_USER_VIP(state, data) {
		state.userVip = data;
	},

	// 设置APP配置信息信息
	SET_SYS_CONF(state, data) {
		state.sysConf = Object.assign({}, state.sysConf, data);
		// #ifdef H5
		window.sessionStorage.setItem('sysConf', JSON.stringify(state.sysConf));
		// #endif
		// #ifndef H5
		uni.setStorageSync('sysConf', state.sysConf);
		// #endif
	}
};

export const actions = {
	// 拉取程序参数配置
	fetchSysConf({
		commit,
		state
	}) {
		return Promise.all([
				uni.$u.http.get('/client/app-content/map'),
				uni.$u.http.get('/client/app-config/map')
			])
			.then(([paramsRes, commonWordRes]) => {
				const combinedRes = {
					...paramsRes,
					...commonWordRes
				};
				// 是否禁用应用相互拉起
				commit('SET_IS_APP_LAUNCH_DISABLED', 0)
				// 是否禁用支付
				commit('SET_IS_PAYMENT_DISABLED', 0)
				commit('SET_SYS_CONF', combinedRes);
				return combinedRes;
			})
			.catch(err => {
				return null;
			});
	},

	// 拉取用户信息
	fetchUser({
		commit,
		state
	}) {
		return new Promise(function(resolve, reject) {
			uni.$u.http.get('/member/user/get', {
				custom: {
					showLoading: false
				}
			}).then(res => {
				commit('SET_USER_INFO', res);
				resolve(res)
			}).catch(err => {
				reject(null)
			})
		});
	},

	// 获取用户VIP信息
	fetchUserVip({
		commit
	}) {
		return new Promise(function(resolve, reject) {
			uni.$u.http.get('/member/vip-membership/get', {
				custom: {
					showLoading: false
				}
			}).then(res => {
				commit('SET_USER_VIP', res);
				resolve(res)
			}).catch(err => {
				reject(null)
			})
		});
	},

	// 更新用户配置
	updateUserProfile({
		commit,
		state,
		getters
	}, params = {}) {
		return new Promise(function(resolve, reject) {
			if (!getters.getterIsLogin) {
				reject(null)
				return
			}
			var reqData = {
				city_id: state.selectedAddress?.cityId || '',
				area_id: state.selectedAddress?.districtId || '',
				...params
			}
			uni.$u.http.post('/user/updateCityInfo', reqData, {
				custom: {
					showLoading: false
				}
			}).then(res => {
				resolve(res)
			}).catch(err => {
				reject(null)
			})
		});
	},

	// 注册推送设备
	registerPushDevice({
		commit,
		state
	}) {
		return new Promise(function(resolve, reject) {
			// #ifdef APP-PLUS
			let deviceId = aliyunPushHelper.getDeviceId()
			uni.$u.http.post('/user/setUserDeviceNumber', {
				device_number: deviceId || ''
			}, {
				custom: {
					showLoading: false
				}
			}).then(res => {
				resolve(res)
			}).catch(err => {
				reject(null)
			})
			// #endif
			// #ifndef APP-PLUS
			reject('仅仅支持APP')
			// #endif
		});
	},

	// 同意协议
	agreementProtocol(type) {
		return
		return new Promise(function(resolve, reject) {
			if (type == null || type == '') {
				reject(null)
				return
			}
			uni.$u.http.post('user/setUserSignAgree', {
				type: type
			}, {
				custom: {
					showLoading: false,
					showError: false
				}
			}).then(res => {
				resolve(res)
			}).catch(err => {
				reject(null)
			})
		});
	},

	// 退出登录
	logoutUser({
		commit,
		state
	}) {
		commit('SET_TOKEN', {});
		commit('SET_USER_INFO', {});
	},
};

export const getters = {
	// 获取登录状态
	getterIsLogin(state) {
		return state.token?.accessToken !== null && state.token?.accessToken !== '';
	},
	// VIP状态
	vipStatus(state) {
		const vip = state.userVip;
		if (!vip || vip.status !== 0) {
			return {
				isValid: false,
				remainingDays: -1
			};
		}

		// 确保时间戳有效
		const now = new Date().getTime(); // 使用毫秒级时间戳
		const start = vip.startTime || 0;
		const end = vip.endTime || 0;

		// 直接比较毫秒值，无需创建 Date 对象
		const isValid = now >= start && now <= end;
		if (!isValid) return {
			isValid: false,
			remainingDays: -1
		};

		// 计算剩余天数（毫秒转天）
		const remainingMs = end - now;
		const remainingDays = Math.ceil(remainingMs / (1000 * 60 * 60 * 24));

		return {
			isValid,
			remainingDays
		};
	}
};