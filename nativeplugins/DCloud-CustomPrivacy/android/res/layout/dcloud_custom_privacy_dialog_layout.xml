<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_content_layout"
    android:layout_width="330dp"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/dcloud_dialog_shape"
    android:gravity="center"
    android:orientation="vertical">


    <TextView
        android:id="@+id/tv_custom_privacy_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingTop="20dp"
        android:text=""
        android:textColor="#333333"
        android:textSize="18sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_privacy_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:letterSpacing="0.02"
        android:paddingHorizontal="20dp"
        android:paddingVertical="10dp"
        android:tag="{'linkColor':'#6B67EF','linkLine':false}"
        android:text=""
        android:textColor="#333333"
        android:textSize="15sp" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"></LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="20dp"
        android:paddingVertical="20dp">

        <Button
            android:id="@+id/btn_custom_privacy_cancel"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="15dp"
            android:layout_weight="1"
            android:background="@drawable/dcloud_custom_cancel_btn_shape"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:paddingLeft="12dp"
            android:paddingTop="10dp"
            android:paddingRight="12dp"
            android:paddingBottom="10dp"
            android:text="拒绝"
            android:textColor="#666666"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btn_custom_privacy_sure"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:background="@drawable/dcloud_custom_confirm_btn_shape"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:paddingLeft="12dp"
            android:paddingTop="10dp"
            android:paddingRight="12dp"
            android:paddingBottom="10dp"
            android:text="同意"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />

    </LinearLayout>
</LinearLayout>