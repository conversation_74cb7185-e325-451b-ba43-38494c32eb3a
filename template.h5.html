<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
		<title>
			<%= htmlWebpackPlugin.options.title %>
		</title>
		<script>
			var UA = window.navigator.userAgent.toLowerCase();
			var isAndroid = UA.indexOf('android') > 0;
			var isIOS = /iphone|ipad|ipod|ios/.test(UA);
			if (!(isAndroid || isIOS)) {
				// 正式发布的时候使用，开发期间不启用。
				// window.location.href = '/demo/picture/website/pcguide.html';
			}
		</script>
		<script>
			document.addEventListener('DOMContentLoaded', function() {
				document.documentElement.style.fontSize = document.documentElement.clientWidth / 20 + 'px'
			})
		</script>
		<link rel="stylesheet" href="<%= BASE_URL %>static/index.css" />
	</head>
	<body>
		<!-- 该文件为 H5 平台的模板 HTML，并非应用入口，请勿直接运行此文件。 -->
		<!-- 详见文档：https://uniapp.dcloud.io/collocation/manifest?id=h5-template -->
		<noscript>
			<strong>Please enable JavaScript to continue.</strong>
		</noscript>
		<div id="app"></div>
		<!-- 微信SDK文件 -->
		<script src="<%= BASE_URL %>static/mp-h5/jweixin-1.4.0.js"></script>
		<!-- 高德地图文件 -->
		<script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=859eeef6f37229fba3afb895542a1e04&plugin=AMap.ToolBar"></script>
		<script src="//webapi.amap.com/ui/1.0/main.js?v=1.0.11"></script>
		<!-- built files will be auto injected -->
		<script>
			var _hmt = _hmt || [];
			(function() {
				var hm = document.createElement("script");
				hm.src = "https://hm.baidu.com/hm.js?fe3b7a223fc08c795f0f4b6350703e6f";
				var s = document.getElementsByTagName("script")[0];
				s.parentNode.insertBefore(hm, s);
			})();
		</script>
	</body>
</html>
