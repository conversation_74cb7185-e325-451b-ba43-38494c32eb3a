{
    "name" : "会计代账宝",
    "appid" : "__UNI__8C431E5",
    "description" : "会计代账宝",
    "transformPx" : false,
    "icons" : [
        {
            "sizes" : "分辨率，192x192",
            "src" : "图片路径"
        }
    ],
    "versionName" : "1.1.0",
    "versionCode" : 10,
    "app-plus" : {
        "compatible" : {
            "ignoreVersion" : true
        },
        "modules" : {
            "Payment" : {},
            "Camera" : {},
            "Share" : {},
            "VideoPlayer" : {},
            "Push" : {},
            "Geolocation" : {},
            "SQLite" : {}
        },
        "useragent" : {
            "value" : "dp.Html5Plus",
            "concatenate" : true
        },
        "useragent_android" : {
            "value" : "dp.Html5Plus",
            "concatenate" : true
        },
        "useragent_ios" : {
            "value" : "dp.Html5Plus",
            "concatenate" : true
        },
        "screenOrientation" : [
            "portrait-primary",
            "portrait-secondary",
            "landscape-primary",
            "landscape-secondary"
        ],
        "distribute" : {
            "apple" : {
                "devices" : "universal"
            },
            "android" : {
                "permissionPhoneState" : {
                    "request" : "none",
                    "prompt" : "为保证您正常、安全地使用，需要获取设备识别码（部分手机提示为获取手机号码）使用权限，请允许。"
                },
                "permissionExternalStorage" : {
                    "request" : "none",
                    "prompt" : "应用保存运行状态等信息，需要获取读写手机存储（系统提示为访问设备上的照片、媒体内容和文件）权限，请允许。"
                },
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.SYSTEM_ALERT_WINDOW\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"com.huawei.android.launcher.permission.CHANGE_BADGE\"/>",
                    "<uses-permission android:name=\"com.vivo.notification.permission.BADGE_ICON\"/>"
                ],
                "abiFilters" : [ "arm64-v8a" ],
                "schemes" : "bljzt",
                "minSdkVersion" : 21,
                "targetSdkVersion" : 30
            },
            "sdkConfigs" : {
                "ad" : {},
                "maps" : {},
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wxd87c468a3c8a2baa",
                        "UniversalLinks" : "https://3c7435f72044ac8e80482566f0e8e39b.share2dlink.com/"
                    },
                    "alipay" : {
                        "__platform__" : [ "android" ]
                    }
                },
                "oauth" : {},
                "speech" : {},
                "share" : {
                    "weixin" : {
                        "appid" : "wxd87c468a3c8a2baa",
                        "UniversalLinks" : "https://3c7435f72044ac8e80482566f0e8e39b.share2dlink.com/"
                    }
                },
                "push" : {},
                "statics" : {},
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                }
            },
            "splashscreen" : {
                "iosStyle" : "common",
                "androidStyle" : "common",
                "ios" : {
                    "storyboard" : "/Users/<USER>/Desktop/APP相关资源/会计代账宝/ios/启动图/LaunchScreen.zip"
                },
                "android" : {
                    "hdpi" : "/Users/<USER>/Desktop/APP相关资源/会计代账宝/android/启动图/0.3启动页480x762 -1.9.png",
                    "xhdpi" : "/Users/<USER>/Desktop/APP相关资源/会计代账宝/android/启动图/0.3启动页720x1242 -1.9.png",
                    "xxhdpi" : "/Users/<USER>/Desktop/APP相关资源/会计代账宝/android/启动图/0.3启动页1080x1882 -1.9.png"
                },
                "useOriginalMsgbox" : true
            },
            "safearea" : {
                //安全区域配置，仅iOS平台生效  
                "bottom" : {
                    // 底部安全区域偏移，"none"表示不空出安全区域，"auto"自动计算空出安全区域，默认值为"none"  
                    "offset" : "none"
                }
            },
            "ios" : {
                "dSYMs" : false,
                "idfa" : false,
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "允许程序访问相册来上传用户头像",
                    "NSPhotoLibraryAddUsageDescription" : "允许程序访问相册写入图片",
                    "NSCameraUsageDescription" : "允许程序访问相机上传用户头像",
                    "NSMicrophoneUsageDescription" : "允许程序访问麦克分以便使用语音识别功能"
                },
                "urltypes" : "bljzt",
                "urlschemewhitelist" : "baiduboxapp"
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        },
        "compilerVersion" : 3,
        "nvueLaunchMode" : "fast",
        "nativePlugins" : {
            "AliCloud-NirvanaPns" : {
                "__plugin_info__" : {
                    "name" : "阿里云号码认证SDK",
                    "description" : "阿里云号码认证SDK，包含一键登录和本机号码校验两个功能。",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=4297",
                    "android_package_name" : "vip.acc.easy",
                    "ios_bundle_id" : "vip.acc.easy",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "4297",
                    "parameters" : {}
                }
            },
            "Aliyun-Push" : {
                "阿里云移动推送Android AppKey" : "",
                "阿里云移动推送Android AppSecret" : "",
                "阿里云移动推送iOS AppKey" : "",
                "阿里云移动推送iOS AppSecret" : "",
                "__plugin_info__" : {
                    "name" : "阿里云移动推送",
                    "description" : "移动推送（Mobile Push）是提供给移动开发者的移动端消息推送服务，通过在App中集成推送功能，进行高效、精准、实时的消息推送，从而使业务及时触达用户，提高用户粘性。",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=7628",
                    "android_package_name" : "vip.acc.easy",
                    "ios_bundle_id" : "vip.acc.easy",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "7628",
                    "parameters" : {
                        "阿里云移动推送Android AppKey" : {
                            "des" : "阿里云EMAS移动应用标识",
                            "key" : "",
                            "value" : ""
                        },
                        "阿里云移动推送Android AppSecret" : {
                            "des" : "阿里云EMAS移动应用密钥",
                            "key" : "",
                            "value" : ""
                        },
                        "阿里云移动推送iOS AppKey" : {
                            "des" : "阿里云EMAS移动应用标识",
                            "key" : "aliyun:push:appKey",
                            "value" : ""
                        },
                        "阿里云移动推送iOS AppSecret" : {
                            "des" : "阿里云EMAS移动应用密钥",
                            "key" : "aliyun:push:appSecret",
                            "value" : ""
                        }
                    }
                }
            },
            "Aliyun-ThirdPush" : {
                "com.gcm.push.apiKey" : "",
                "com.gcm.push.applicationid" : "",
                "com.gcm.push.projectid" : "",
                "com.gcm.push.sendid" : "",
                "com.hihonor.push.app_id" : "",
                "com.huawei.hms.client.appid" : "",
                "com.meizu.push.id" : "",
                "com.meizu.push.key" : "",
                "com.oppo.push.key" : "",
                "com.oppo.push.secret" : "",
                "com.vivo.push.api_key" : "",
                "com.vivo.push.app_id" : "",
                "com.xiaomi.push.id" : "",
                "com.xiaomi.push.key" : "",
                "__plugin_info__" : {
                    "name" : "阿里云移动推送-厂商通道",
                    "description" : "移动推送（Mobile Push）是提供给移动开发者的移动端消息推送服务，通过在App中集成推送功能，进行高效、精准、实时的消息推送，从而使业务及时触达用户，提高用户粘性。厂商通道是使用手机厂商提供的",
                    "platforms" : "Android",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=7629",
                    "android_package_name" : "vip.acc.easy",
                    "ios_bundle_id" : "vip.acc.easy",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "7629",
                    "parameters" : {
                        "com.gcm.push.apiKey" : {
                            "des" : "gcm推送apiKey",
                            "key" : "",
                            "value" : ""
                        },
                        "com.gcm.push.applicationid" : {
                            "des" : "gcm推送applicationId",
                            "key" : "",
                            "value" : ""
                        },
                        "com.gcm.push.projectid" : {
                            "des" : "gcm推送projectid",
                            "key" : "",
                            "value" : ""
                        },
                        "com.gcm.push.sendid" : {
                            "des" : "gcm推送sendId",
                            "key" : "",
                            "value" : ""
                        },
                        "com.hihonor.push.app_id" : {
                            "des" : "荣耀推送AppId",
                            "key" : "",
                            "value" : ""
                        },
                        "com.huawei.hms.client.appid" : {
                            "des" : "华为推送AppId",
                            "key" : "",
                            "value" : ""
                        },
                        "com.meizu.push.id" : {
                            "des" : "魅族推送ID",
                            "key" : "",
                            "value" : ""
                        },
                        "com.meizu.push.key" : {
                            "des" : "魅族推送Key",
                            "key" : "",
                            "value" : ""
                        },
                        "com.oppo.push.key" : {
                            "des" : "Oppo推送Key",
                            "key" : "",
                            "value" : ""
                        },
                        "com.oppo.push.secret" : {
                            "des" : "Oppo推送密钥",
                            "key" : "",
                            "value" : ""
                        },
                        "com.vivo.push.api_key" : {
                            "des" : "vivo推送Api Key",
                            "key" : "",
                            "value" : ""
                        },
                        "com.vivo.push.app_id" : {
                            "des" : "vivo推送App Id",
                            "key" : "",
                            "value" : ""
                        },
                        "com.xiaomi.push.id" : {
                            "des" : "小米推送ID",
                            "key" : "",
                            "value" : ""
                        },
                        "com.xiaomi.push.key" : {
                            "des" : "小米推送Key",
                            "key" : "",
                            "value" : ""
                        }
                    }
                }
            }
        },
        "splashscreen" : {
            "waiting" : false,
            "alwaysShowBeforeRender" : false,
            "autoclose" : false,
            "delay" : 0
        }
    },
    "quickapp" : {},
    "mp-weixin" : {
        "setting" : {
            "urlCheck" : false,
            "es6" : true,
            "postcss" : true,
            "minified" : true
        },
        "usingComponents" : true,
        "appid" : "wx79c82deca2ae5512",
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于小程序位置接口的效果展示"
            }
        }
    },
    "h5" : {
        "template" : "template.h5.html",
        "router" : {
            "mode" : "hash",
            "base" : "./"
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : true
            }
        }
    }
}
