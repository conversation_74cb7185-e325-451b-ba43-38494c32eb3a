<template>
	<view class="test-container">
		<button @click="showCascader = true">打开级联选择器</button>
		
		<el-cascader-popup
			:show="showCascader"
			:title="'测试级联选择'"
			:level="3"
			:showAllLevels="true"
			:options="cascaderOptions"
			:props="cascaderProps"
			@close="showCascader = false"
			@confirm="handleConfirm"
		/>
	</view>
</template>

<script>
import ElCascaderPopup from './components/popup-cascader/popup-cascader.vue'

export default {
	components: {
		ElCascaderPopup
	},
	data() {
		return {
			showCascader: false,
			cascaderOptions: [
				{
					id: '1',
					name: '一级选项1',
					children: [
						{
							id: '1-1',
							name: '二级选项1-1',
							children: [
								{ id: '1-1-1', name: '三级选项1-1-1' },
								{ id: '1-1-2', name: '三级选项1-1-2' }
							]
						},
						{
							id: '1-2',
							name: '二级选项1-2',
							children: [
								{ id: '1-2-1', name: '三级选项1-2-1' },
								{ id: '1-2-2', name: '三级选项1-2-2' }
							]
						}
					]
				},
				{
					id: '2',
					name: '一级选项2',
					children: [
						{
							id: '2-1',
							name: '二级选项2-1',
							children: [
								{ id: '2-1-1', name: '三级选项2-1-1' },
								{ id: '2-1-2', name: '三级选项2-1-2' }
							]
						},
						{
							id: '2-2',
							name: '二级选项2-2',
							children: [
								{ id: '2-2-1', name: '三级选项2-2-1' },
								{ id: '2-2-2', name: '三级选项2-2-2' }
							]
						}
					]
				}
			],
			cascaderProps: {
				value: 'id',
				label: 'name',
				children: 'children'
			}
		}
	},
	methods: {
		handleConfirm(result) {
			console.log('选择结果:', result)
		}
	}
}
</script>

<style>
.test-container {
	padding: 20px;
}

button {
	padding: 10px 20px;
	background: #409eff;
	color: white;
	border: none;
	border-radius: 4px;
	cursor: pointer;
}
</style>
