<template>
	<view class="test-container">
		<view class="test-info">
			<text>单选选中值: {{ JSON.stringify(selectedValue) }}</text>
			<br />
			<text>多选选中值: {{ JSON.stringify(multipleSelectedValue) }}</text>
		</view>
		<button @click="showCascader = true">打开级联选择器（单选）</button>
		<button @click="showMultipleCascader = true" style="margin-left: 10px;">打开级联选择器（多选）</button>
		<button @click="clearSelection" style="margin-left: 10px;">清空选择</button>
		
		<!-- 单选模式 -->
		<el-cascader-popup
			:show="showCascader"
			:title="'测试级联选择（单选）'"
			:level="3"
			:showAllLevels="true"
			:multiple="false"
			:options="cascaderOptions"
			:props="cascaderProps"
			:modelValue="selectedValue"
			@close="showCascader = false"
			@confirm="handleConfirm"
		/>

		<!-- 多选模式 -->
		<el-cascader-popup
			:show="showMultipleCascader"
			:title="'测试级联选择（多选）'"
			:level="3"
			:showAllLevels="true"
			:multiple="true"
			:max="3"
			:options="cascaderOptions"
			:props="cascaderProps"
			:modelValue="multipleSelectedValue"
			@close="showMultipleCascader = false"
			@confirm="handleMultipleConfirm"
		/>
	</view>
</template>

<script>
import ElCascaderPopup from './components/popup-cascader/popup-cascader.vue'

export default {
	components: {
		ElCascaderPopup
	},
	data() {
		return {
			showCascader: false,
			showMultipleCascader: false,
			selectedValue: ['1-2-1'], // 预选值，用于测试回显高亮
			multipleSelectedValue: ['1-1-1', '2-2-2'], // 多选预选值
			cascaderOptions: [
				{
					id: '1',
					name: '一级选项1',
					children: [
						{
							id: '1-1',
							name: '二级选项1-1',
							children: [
								{ id: '1-1-1', name: '三级选项1-1-1' },
								{ id: '1-1-2', name: '三级选项1-1-2' }
							]
						},
						{
							id: '1-2',
							name: '二级选项1-2',
							children: [
								{ id: '1-2-1', name: '三级选项1-2-1' },
								{ id: '1-2-2', name: '三级选项1-2-2' }
							]
						}
					]
				},
				{
					id: '2',
					name: '一级选项2',
					children: [
						{
							id: '2-1',
							name: '二级选项2-1',
							children: [
								{ id: '2-1-1', name: '三级选项2-1-1' },
								{ id: '2-1-2', name: '三级选项2-1-2' }
							]
						},
						{
							id: '2-2',
							name: '二级选项2-2',
							children: [
								{ id: '2-2-1', name: '三级选项2-2-1' },
								{ id: '2-2-2', name: '三级选项2-2-2' }
							]
						}
					]
				}
			],
			cascaderProps: {
				value: 'id',
				label: 'name',
				children: 'children'
			}
		}
	},
	methods: {
		handleConfirm(result) {
			console.log('单选结果:', result)
			// 更新选中值
			if (result.value && result.value.length > 0) {
				this.selectedValue = [result.value[0][this.cascaderProps.value]]
			}
		},
		handleMultipleConfirm(result) {
			console.log('多选结果:', result)
			// 更新多选值
			if (result.value && result.value.length > 0) {
				this.multipleSelectedValue = result.value.map(item => item[this.cascaderProps.value])
			}
		},
		clearSelection() {
			this.selectedValue = []
			this.multipleSelectedValue = []
		}
	}
}
</script>

<style>
.test-container {
	padding: 20px;
}

.test-info {
	margin-bottom: 20px;
	padding: 10px;
	background: #f5f5f5;
	border-radius: 4px;
}

button {
	padding: 10px 20px;
	background: #409eff;
	color: white;
	border: none;
	border-radius: 4px;
	cursor: pointer;
}
</style>
