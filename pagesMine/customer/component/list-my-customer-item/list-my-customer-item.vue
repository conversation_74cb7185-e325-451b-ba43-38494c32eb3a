<template>
	<view class="flex-col wraper" @click.stop="click" v-if="!$u.test.isEmpty(item)">
		<image class="wraper__new"></image>
		<view class="flex wraper__top">
			<image class="wraper__top__source" :src="iImage('ic_customer_source_im.png', 'pagesMine')"></image>
			<text class="wraper__top__title line1">公司注册·工商服务</text>
		</view>
		<view class="flex wraper__tag-content">
			<view class="flex wraper__tag-content__tag" v-for="(t, i) in tagFields" :key="i">
				<image :src="iImage(t.ic)" mode="aspectFit"></image>
				<text>{{ t.value }}</text>
			</view>
		</view>
		<text class="wraper__tooltip">您未开通会员，无法接听，开通会员后立即回拨 拷贝</text>
		<view class="flex wraper__bottom">
			<text class="wraper__bottom__date">2025-05 -16 16:26</text>
			<view class="flex wraper__bottom__actions">
				<view class="flex wraper__bottom__actions__im">在线聊</view>
				<view class="flex wraper__bottom__actions__tel">立即回拨</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'list-hall-demand-item',
	props: {
		// 列表条目数据
		item: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {};
	},
	computed: {
		tagFields() {
			return [
				{ ic: 'ic_hall_demand_city.png', value: '北京市·朝阳区' },
				{ ic: 'ic_hall_demand_tel.png', value: '189********' }
			];
		}
	},
	methods: {
		click() {
			this.$emit('click', { detail: this.item });
		}
	}
};
</script>

<style lang="scss" scoped>
.wraper {
	margin: 0 30rpx;
	padding: 25rpx 20rpx;
	background-color: #ffffff;
	box-shadow: 0rpx 0rpx 18rpx 0rpx rgba(126, 129, 137, 0.16);
	border-radius: 20rpx;
	position: relative;
	overflow: hidden;

	&__new {
		width: 15rpx;
		height: 15rpx;
		background: #f43a55;
		border-radius: 50%;
		position: absolute;
		top: 20rpx;
		right: 20rpx;
	}

	&__top {
		align-items: center;

		&__source {
			width: 46rpx;
			height: 46rpx;
		}

		&__title {
			margin-left: 15rpx;
			font-size: 36rpx;
			font-weight: bold;
			color: #1e2134;
			line-height: 36rpx;
		}
	}

	&__tag-content {
		padding: 10rpx 20rpx;
		margin-top: 20rpx;
		background-color: #f5f7fb;
		border-radius: 10rpx;
		flex-wrap: wrap;

		&__tag {
			height: 55rpx;
			margin-right: 30rpx;

			image {
				width: 30rpx;
				height: 30rpx;
			}
			text {
				margin-left: 5rpx;
				font-weight: 400;
				font-size: 28rpx;
				color: #1e2134;
			}
		}
	}

	&__tooltip {
		font-size: 24rpx;
		color: #f43a55;
		line-height: 60rpx;
	}

	&__bottom {
		margin-top: 20rpx;
		justify-content: space-between;

		&__date {
			font-size: 26rpx;
			color: #909099;
		}

		&__actions {
			&__im,
			&__tel {
				width: 160rpx;
				height: 60rpx;
				background: #444bf1;
				color: #ffffff;
				font-size: 28rpx;
				font-weight: 500;
				border-radius: 30px;
				justify-content: center;
			}

			&__im {
				width: 160rpx;
				height: 60rpx;
				background: #f43a55;
				color: #ffffff;
				font-size: 28rpx;
				border-radius: 30px;
				justify-content: center;
			}
		}

		&__actions > view:not(:last-child) {
			margin-right: 10rpx;
		}
	}
}
</style>
