<template>
	<view>
		<public-module></public-module>
		<z-paging ref="paging"  :refresher-out-rate="0.9" v-model="dataList" @query="getList">
			<template #top>
				<!-- 顶部导航 -->
				<view class="flex nav-wraper">
					<view class="flex nav-wraper__content" :style="{ height: `${navBarHeight}px`, marginTop: `${statusBarHeight}px` }">
						<view class="flex nav-wraper__content__left">
							<image :src="iImage('img_my_customer_title.png', 'pagesMine')"></image>
						</view>
						<view class="flex nav-wraper__content__right" @click.stop="onSelectAddress">
							<text>客源大厅</text>
						</view>
					</view>
				</view>
			</template>

			<!-- 列表状态 -->
			<custom-empty slot="empty"></custom-empty>
			<custom-loading :status="loadingStatus"></custom-loading>
			<!-- 列表 -->
			<view class="flex wrap-list">
				<list-my-customer-item class="wrap-list__item" :item="item" v-for="(item, index) in dataList" :key="index" @click="onListItemClick"></list-my-customer-item>
			</view>
			<!-- 【底部TabBar】 -->
			<view slot="bottom">
				<x-tabbar ref="xTabbar"></x-tabbar>
			</view>
		</z-paging>
	</view>
</template>

<script>
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';
import ListMyCustomerItem from './component/list-my-customer-item/list-my-customer-item.vue';
export default {
	components: {
		ListMyCustomerItem
	},
	data() {
		return {
			// 小程序胶囊
			mpMenuRect: {},
			// 状态栏高度
			statusBarHeight: 0,
			// 导航栏高度
			navBarHeight: 44,

			// 分类
			cateList: [],
			cateIndex: [],

			// 列表加载状态
			loadingStatus: 'LOADING',
			// 数据列表
			dataList: []
		};
	},
	computed: {
		...mapState(['selectedAddress', 'sysConf']),
		...mapGetters(['getterIsLogin'])
	},
	onLoad(options) {
		uni.hideTabBar();
		const sys = uni.getSystemInfoSync();
		this.statusBarHeight = sys.statusBarHeight;
		// #ifdef MP
		this.mpMenuRect = uni.getMenuButtonBoundingClientRect();
		// #endif
	},
	onShow() {

	},
	methods: {
		// 列表条目点击
		onListItemClick(item) {
			this.onJump({
				url: '/pagesCustomer/customer/detail',
				params: { id: item.detail.id }
			});
		},
		// 查询数据列表
		getList(pageNo, pageSize) {
			uni.$u.http
				.get('/product/spu/page', {
					page: pageNo,
					limit: pageSize,
					serviceCategoryId: ''
				})
				.then((res) => {
					this.loadingStatus = 'SUCCESS';
					this.$refs.paging.complete(res.list);
				})
				.catch((err) => {
					this.loadingStatus = 'FAIL';
					this.$refs.paging.complete(false);
				});
		}
	}
};
</script>

<style lang="scss" scoped>
/* 导航栏 */
.nav-wraper {
	z-index: 999;
	width: 750rpx;

	&__content {
		flex: 1;
		justify-content: space-between;

		&__left {
			height: 80rpx;
			padding: 0 30rpx;
			image {
				width: 220rpx;
				height: 44rpx;
			}
		}

		&__right {
			height: 80rpx;
			padding: 0 30rpx;
			justify-content: flex-end;
			text {
				font-weight: 500;
				font-size: 30rpx;
				color: #1e2134;
			}
		}
	}
}


/* 列表 */
.wrap-list {
	padding: 0 30rpx;
	flex-direction: column;

	&__item {
		width: 100%;
		margin-top: 20rpx;
	}
}
</style>
