<template>
	<view>
		<public-module></public-module>
		<u-navbar slot="top" bgColor="#FFFFFF" title="教程详情" :fixed="true" :statusBar="true" :placeholder="true" :autoBack="true" />
		<view class="flex-col wrap">
			<text class="wrap__title">{{ detail.title }}</text>
			<view class="wrap__content"><u-parse :content="detail.content"></u-parse></view>
		</view>
		<view class="flex bottom">
			<text>还未解决您的问题？</text>
			<view class="flex bottom__kf" @click="kfPopup.show = true">
				<image class="bottom__kf__ic" :src="iImage('ic_article_detail_kf.png','pagesMine')" mode="aspectFill"></image>
				联系客服
			</view>
		</view>
		<!-- 客服 -->
		<u-popup :show="kfPopup.show" bgColor="transparent" mode="center" :close-on-click-overlay="false" @close="kfPopup.show = false">
			<popup-kf @close="kfPopup.show = false"></popup-kf>
		</u-popup>
	</view>
</template>

<script>
export default {
	data() {
		return {
			pageParams: {},
			// 文章详情
			detail: {},
			// 客服弹窗
			kfPopup: {
				show: false
			}
		};
	},
	onLoad(options) {
		this.pageParams = options;
		this.queryDetial();
	},
	methods: {
		queryDetial() {
			uni.$u.http
				.get('/promotion/article/get', { params: { id: this.pageParams.id } })
				.then((res) => {
					this.detail = res;
				})
				.catch((err) => {});
		}
	}
};
</script>

<style>
page {
	background-color: #ffffff;
}
</style>
<style lang="scss" scoped>
.wrap {
	padding: 30rpx;
	&__title {
		font-weight: bold;
		font-size: 34rpx;
		color: #333333;
	}
	&__content {
		margin-top: 40rpx;
		font-size: 32rpx;
		color: #333333;
		line-height: 45rpx;
		text-align: justify;
	}
}
.bottom {
	width: 100%;
	padding: 30rpx;
	background-color: #f7f7f7;
	justify-content: space-between;

	// backdrop-filter: blur(10px);
	// position: fixed;
	// bottom: 0;
	&__kf {
		width: 220rpx;
		height: 66rpx;
		border-radius: 33rpx;
		border: 1px solid #444BF1;
		font-size: 30rpx;
		color: #444BF1;
		align-items: center;
		justify-content: center;
		&__ic {
			width: 43rpx;
			height: 33rpx;
			margin-right: 10rpx;
		}
	}
}
</style>
