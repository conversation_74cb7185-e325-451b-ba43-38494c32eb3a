<template>
	<view>
		<public-module></public-module>
		<z-paging ref="paging" v-model="dataList" @query="queryList">
			<template #top>
				<u-navbar bgColor="#FFFFFF" title="使用教程" :statusBar="true" :placeholder="true" :autoBack="true" />
			</template>
			<view class="flex wrap-list">
				<view class="flex wrap-list__cell" v-for="(item, index) in dataList" :key="index"
					@click="onListItemClick(item)">
					<view class="flex wrap-list__cell__left">
						<image class="wrap-list__cell__left__cover" :src="item.coverUrl" mode="aspectFill"></image>
						<image class="wrap-list__cell__left__flag" :src="iImage('ic_video.png','pagesMine')"
							v-if="item.type == 2"></image>
					</view>
					<view class="flex-col wrap-list__cell__right">
						<text class="wrap-list__cell__right__title line1">{{ item.title }}</text>
						<text class="wrap-list__cell__right__subtitle line2">{{ item.introduction }}</text>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				dataList: []
			};
		},
		methods: {
			onListItemClick(item) {
				uni.$u.http.put('/promotion/article/add-browse-count', {}, {
					params:{id: item.id},
					custom: {
						showError: false
					}
				})
				this.onJump({
					url: '/pagesMine/helper/detail',
					params: {
						id: item.id
					}
				});
			},
			queryList(pageNo, pageSize) {
				uni.$u.http
					.get('/promotion/article/page', {
						params: {
							pageNo: pageNo,
							pageSize: pageSize
						}
					})
					.then((res) => {
						this.loadingStatus = 'SUCCESS';
						this.$refs.paging.complete(res.list);
					})
					.catch((err) => {
						this.loadingStatus = 'FAIL';
						this.$refs.paging.complete(false);
					});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.wrap-list {
		width: 100%;
		flex-direction: column;
		padding: 0 30rpx;

		&__cell {
			width: 100%;
			margin-top: 20rpx;
			padding: 20rpx;
			background: #ffffff;
			border-radius: 20rpx;
			justify-content: space-between;

			&__left {
				position: relative;
				align-items: center;
				justify-content: center;
				background: #f7f7f7;
				border-radius: 15rpx;

				&__cover {
					width: 200rpx;
					min-width: 200rpx;
					height: 150rpx;
					border-radius: 15rpx;
				}

				&__flag {
					width: 46rpx;
					height: 46rpx;
					position: absolute;
				}
			}

			&__right {
				flex: 1;
				height: 150rpx;
				margin-left: 20rpx;
				align-items: flex-start;
				position: relative;

				&__title {
					font-weight: bold;
					font-size: 30rpx;
					color: #333333;
				}

				&__subtitle {
					margin-top: 15rpx;
					font-size: 26rpx;
					color: #999999;
				}
			}
		}
	}
</style>