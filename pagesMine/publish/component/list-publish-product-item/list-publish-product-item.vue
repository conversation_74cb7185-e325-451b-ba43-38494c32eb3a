<template>
	<view class="flex-col wraper" @click.stop="click">
		<image class="wraper__audit-status" :src="audtiStatusImg" v-if="audtiStatusImg"></image>
		<view class="flex wraper__content">
			<view class="wraper__left">
				<image class="wraper__left__image" :src="item.picUrl" mode="aspectFill"></image>
			</view>
			<view class="flex wraper__right">
				<view class="flex">
					<text class="wraper__right__pin" v-if="item.isPinned">置顶中</text>
					<text class="wraper__right__title line1">{{ item.name }}</text>
				</view>
				<text class="wraper__right__subtitle line1">{{ item.introduction }}</text>
				<rich-text class="wraper__right__price" :nodes="formatPrice"></rich-text>
			</view>
		</view>
		<view class="flex wraper__bar">
			<view v-for="(item, index) in actionBtns" :key="index" class="flex wraper__bar__item" :class="[item.class]" @click.stop="childClick(item.type)" v-if="item.condition">
				<image :src="iImage(item.icon, 'pagesMine')" mode="aspectFit"></image>
				<text>{{ item.label }}</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'list-publish-product-item',
	props: {
		item: {
			type: Object,
			default: () => {}
		}
	},
	computed: {
		// 格式化价格
		formatPrice() {
			const yuan = this.$options.filters.fen2yuanSimple(this.item.price);
			return this.$options.filters.formattedPrice({
				price: yuan,
				unit: '',
				highlightSize: '18px'
			});
		},
		audtiStatusImg() {
			switch (this.item.latestAuditStatus) {
				case this.cons.PRODUCT_AUDIT_STATUS_PENDING: // 待审核
					return this.iImage('img_audit_status_pending.png');
				case this.cons.PRODUCT_AUDIT_STATUS_REJECTED: // 审核驳回
					return this.iImage('img_audit_status_rejected.png');
				default:
					return null;
			}
		},
		// 展示指定按钮
		actionBtns() {
			const auditStatus = this.item?.latestAuditStatus;
			return [
				{
					type: 'delete',
					label: '删除',
					icon: 'ic_list_item_delete.png',
					class: 'wraper__bar__item--delete',
					condition: true
				},
				{
					type: 'edit',
					label: '编辑',
					icon: 'ic_list_item_edit.png',
					class: 'wraper__bar__item--edit',
					condition: !auditStatus || auditStatus === this.cons.PRODUCT_AUDIT_STATUS_REJECTED || auditStatus === this.cons.PRODUCT_AUDIT_STATUS_APPROVED
				},
				{
					type: 'pin',
					label: '置顶',
					icon: 'ic_list_item_pin.png',
					class: 'wraper__bar__item--pin',
					condition: !auditStatus || auditStatus === this.cons.PRODUCT_AUDIT_STATUS_APPROVED
				}
			].filter((e) => e.condition);
		}
	},
	data() {
		return {};
	},
	methods: {
		click() {
			this.$emit('click', { detail: this.item });
		},
		childClick(type) {
			this.$emit('childClick', { type, detail: this.item });
		}
	}
};
</script>

<style lang="scss" scoped>
.wraper {
	padding: 0 20rpx;
	background: #ffffff;
	box-shadow: 0rpx 0rpx 18rpx 0rpx rgba(126, 129, 137, 0.16);
	border-radius: 20rpx;
	position: relative;

	&__audit-status {
		z-index: 10;
		width: 100rpx;
		height: 100rpx;
		position: absolute;
		top: 0;
		left: 0;
	}

	&__content {
		z-index: 5;
		padding: 20rpx 0;
	}

	&__left {
		width: 200rpx;
		height: 150rpx;

		&__image {
			width: 100%;
			height: 100%;
			background: #f5f5f5;
			border-radius: 15rpx;
		}
	}

	&__right {
		flex: 1;
		height: 160rpx;
		margin-left: 20rpx;
		padding: 5rpx 0;
		flex-direction: column;
		align-items: flex-start;
		position: relative;

		&__pin {
			width: 90rpx;
			height: 36rpx;
			margin-right: 10rpx;
			background: #2cbdb9;
			border-radius: 4rpx;
			font-weight: 400;
			font-size: 24rpx;
			color: #ffffff;
			line-height: 36rpx;
			text-align: center;
		}

		&__title {
			font-weight: bold;
			font-size: 32rpx;
			line-height: 36rpx;
			color: #1e2134;
		}

		&__subtitle {
			font-size: 28rpx;
			color: #5f6069;
			line-height: 50rpx;
		}

		&__price {
			margin-top: auto;
			font-weight: 800;
			font-size: 36rpx;
			color: #f43a55;
		}
	}

	&__bar {
		height: 80rpx;
		justify-content: flex-end;
		border-top: 0.8rpx solid #efefef;

		&__item {
			align-items: center;
			justify-content: center;

			image {
				width: 30rpx;
				height: 32rpx;
			}

			text {
				margin-left: 10rpx;
				font-weight: 400;
				font-size: 30rpx;
				color: #1e2134;
			}

			&--pin {
				text {
					color: #444bf1;
				}
			}
		}
	}

	&__bar > view:not(:last-child) {
		margin-right: 50rpx;
	}
}
</style>
