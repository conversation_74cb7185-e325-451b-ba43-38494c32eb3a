<template>
	<view>
		<z-paging>
			<template #top>
				<u-navbar title="发布服务" :statusBar="true" :placeholder="true" :autoBack="true" />
			</template>
			<view class="flex audit-wrap" v-if="!$u.test.isEmpty(auditInfo) && auditInfo.auditStatus == this.cons.PRODUCT_AUDIT_STATUS_REJECTED">
				<image :src="iImage('ic_shuom.png', 'pagesMine')" mode="aspectFit"></image>
				<text>{{ auditInfo.auditReason }}</text>
			</view>

			<view class="flex-col wrap">
				<view class="flex-col wrap__pics-cell" :class="{ verify: formVerify && $u.test.isEmpty(form.imageList) }">
					<text class="wrap__pics-cell__title">{{ `服务宣传图（${form.imageList.length}/${maxPicCount}）` }}</text>
					<view class="wrap__pics-cell__content">
						<!-- 添加按钮（当未达到最大数量时显示） -->
						<view v-if="form.imageList.length < maxPicCount" class="item wrap__pics-cell__content__add" @click="chooseImage">
							<image :src="iImage('ic_pic_add.png')" mode="aspectFit"></image>
							<text>点击上传</text>
						</view>
						<!-- 已上传图片列表 -->
						<view v-for="(item, index) in form.imageList" :key="index" class="item wrap__pics-cell__content__img">
							<image class="wrap__pics-cell__content__img__preview" :src="item" mode="aspectFill" @click="previewImage(index)" />
							<image class="wrap__pics-cell__content__img__delete" :src="iImage('ic_item_delete.png', 'pagesMine')" @click.stop="deleteImage(index)" />
						</view>
					</view>
				</view>
				<view class="flex wrap__normal-cell" :class="{ verify: formVerify && $u.test.isEmpty(form.producName) }">
					<text class="wrap__normal-cell__text">服务名称</text>
					<input class="wrap__normal-cell__input" v-model="form.producName" placeholder="请填写服务名称" maxlength="12" />
				</view>
				<view class="flex wrap__normal-cell" :class="{ verify: formVerify && $u.test.isEmpty(serviceCategoryValue) }" @click="onShowSelectPopup('service')">
					<text class="wrap__normal-cell__text">业务类型</text>
					<input class="wrap__normal-cell__input" :value="serviceCategoryValue" placeholder="请选择业务类型" disabled />
					<image class="wrap__normal-cell__arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
				</view>
				<view class="flex wrap__normal-cell" :class="{ verify: formVerify && $u.test.isEmpty(serviceAreaValue) }" @click="onShowSelectPopup('city')">
					<text class="wrap__normal-cell__text">服务区域</text>
					<input class="wrap__normal-cell__input" :value="serviceAreaValue" placeholder="请选择服务区域" disabled />
					<image class="wrap__normal-cell__arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
				</view>
				<view class="flex wrap__normal-cell" :class="{ verify: formVerify && $u.test.isEmpty(form.price) }">
					<text class="wrap__normal-cell__text">服务价格</text>
					<input class="wrap__normal-cell__input" v-model="form.price" placeholder="请填写服务价格" type="number" maxlength="6" />
					<text class="wrap__normal-cell__prefix">元起</text>
				</view>
				<view class="flex wrap__normal-cell" :class="{ verify: formVerify && $u.test.isEmpty(form.advantageTags) }">
					<text class="wrap__normal-cell__text">服务优势</text>
					<view class="wrap__normal-cell__tag-select" @click.stop="onShowSelectPopup('advantageTag')">
						<text class="wrap__normal-cell__tag-select__placeholder" v-if="$u.test.isEmpty(form.advantageTags)">{{ `请选择服务优势(最多${maxTagCount}个)` }}</text>
						<view class="wrap__normal-cell__tag-select__content" v-else>
							<view class="flex wrap__normal-cell__tag-select__content__tag" v-for="(str, idx) in form.advantageTags" :key="idx">
								<view class="wrap__normal-cell__tag-select__content__tag__name">{{ str }}</view>
								<image class="flex wrap__normal-cell__tag-select__content__tag__delete" :src="iImage('ic_tag_delete.png', 'pagesMine')" @click.stop="deleteAdvantageTag(idx)"></image>
							</view>
						</view>
					</view>
					<image class="wrap__normal-cell__arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
				</view>
				<view class="flex wrap__normal-cell" :class="{ verify: formVerify && $u.test.isEmpty(form.intro) }">
					<text class="wrap__normal-cell__text">服务简介</text>
					<input class="wrap__normal-cell__input" v-model="form.intro" placeholder="请填写服务简介" maxlength="30" />
				</view>
				<view class="flex wrap__normal-cell" :class="{ verify: formVerify && $u.test.isEmpty(form.intro) }">
					<text class="wrap__normal-cell__text">服务内容</text>
					<view class="flex-col wrap__normal-cell__list">
						<view class="flex wrap__normal-cell__list__item" v-for="(item, index) in processList" :key="index" @click="onServiceProcessItemClick(item)">
							<image class="wrap__normal-cell__list__item__ic" :src="form.serviceProcessIds.includes(item.id) ? iImage('ic_check_sel.png') : iImage('ic_check_nor.png')"></image>
							<text class="wrap__normal-cell__list__item__text">{{ `${index + 1}.${item.name}` }}</text>
						</view>
					</view>
				</view>
			</view>

			<template #bottom>
				<view class="flex submit" @click="onPrepareSubmit">立即发布</view>
			</template>
		</z-paging>
		<!-- 城市选择器 -->
		<u-popup :show="cityPopup.show" bgColor="transparent" mode="bottom" :safe-area-inset-bottom="false" closeable @close="cityPopup.show = false">
			<popup-city :length="cityPopup.level" :show="cityPopup.show" :value="cityPopup.value" :complete="cityPopup.complete" @close="cityPopup.show = false"></popup-city>
		</u-popup>
		<!-- 选择器 -->
		<u-popup :show="selectPopup.show" bgColor="transparent" mode="bottom" :close-on-click-overlay="false" :safe-area-inset-bottom="false" closeable @close="selectPopup.show = false">
			<popup-select
				:title="selectPopup.title"
				:list="selectPopup.list"
				:value="selectPopup.value"
				:max-num="selectPopup.maxNum"
				:show-input="selectPopup.showInput"
				:complete="selectPopup.complete"
				@close="selectPopup.show = false"
			></popup-select>
		</u-popup>
		<!-- 级联选择器 -->
		<u-popup :show="linkSelectPopup.show" bgColor="transparent" mode="bottom" :safe-area-inset-bottom="false" closeable @close="linkSelectPopup.show = false">
			<popup-link-select
				:title="linkSelectPopup.title"
				:level="linkSelectPopup.level"
				:list="linkSelectPopup.list"
				:value="linkSelectPopup.value"
				:complete="linkSelectPopup.complete"
				@close="linkSelectPopup.show = false"
			></popup-link-select>
		</u-popup>
		<!-- 通用操作弹窗 -->
		<u-popup :show="promptPopup.show" bgColor="transparent" mode="center" :close-on-click-overlay="false" @close="promptPopup.show = false">
			<popup-prompt
				:ident="promptPopup.ident"
				:icon="promptPopup.icon"
				:iconStyle="promptPopup.iconStyle"
				:title="promptPopup.title"
				:message="promptPopup.message"
				:actions="promptPopup.actions"
				:tipText="promptPopup.tipText"
				:buttonBoxJustifyContent="promptPopup.buttonBoxJustifyContent"
				:customStyle="promptPopup.customStyle"
				:showClose="promptPopup.showClose"
				@cancel="promptPopup.show = false"
			></popup-prompt>
		</u-popup>
	</view>
</template>

<script>
import { selectImage } from '@/common/utils.js';
import { handleTree } from '@/common/tool.js';
export default {
	props: {},
	data() {
		return {
			pageParams: {},
			// 最大服务优势数量
			maxTagCount: 3,
			// 最大图片数量
			maxPicCount: 3,
			// 服务流程
			processList: [],
			// 表单数据
			form: {
				// 已上传图片列表
				imageList: [],
				// 产品名称
				producName: '',
				// 服务类型
				serviceCategory: [],
				// 服务区域
				serviceArea: {},
				// 服务价格
				price: '',
				// 服务优势
				advantageTags: [],
				// 服务简介
				intro: '',
				// 服务流程
				serviceProcessIds: []
			},
			// 审核信息【编辑状态下有值]
			auditInfo: {},

			// 表单验证
			formVerify: false,

			// 城市选择器
			cityPopup: { show: false },
			// 选择器
			selectPopup: { show: false, list: [] },
			// 选择器
			linkSelectPopup: { show: false, list: [] },
			// 通用弹窗
			promptPopup: { show: false }
		};
	},
	computed: {
		// 是否是编辑
		isEdit() {
			return uni.$u.test.isEmpty(this.pageParams?.id) ? false : true;
		},
		// 服务器区域名称
		serviceAreaValue() {
			return [this.form.serviceArea.provinceName, this.form.serviceArea.cityName].filter((e) => !uni.$u.test.isEmpty(e)).join('/');
		},
		// 服务类型名称
		serviceCategoryValue() {
			if (!uni.$u.test.isEmpty(this.form.serviceCategory)) {
				return this.form.serviceCategory.map((e) => e.secondName).join('/');
			}
			return '';
		}
	},
	onLoad(options) {
		this.pageParams = options;

		this.getServiceProcessList();

		if (this.isEdit) {
			this.getProductEditable();
		}
	},
	methods: {
		// 展示选择器
		onShowSelectPopup(type) {
			switch (type) {
				case 'city':
					this.cityPopup = {
						show: true,
						level: 2,
						value: this.form.serviceArea,
						complete: (res) => {
							this.form.serviceArea = res;
						}
					};
					break;
				case 'service':
					uni.$u.http.get('/category/common/list-by-type', { params: { type: this.cons.CATEGORY_TYPE_SERVICE } }).then((res) => {
						this.linkSelectPopup = {
							show: true,
							level: 2,
							list: handleTree(res),
							value: this.form.serviceCategory,
							complete: (res) => {
								console.error('-----', res);
								this.form.serviceCategory = res;
							}
						};
					});
					break;
				case 'advantageTag':
					uni.$u.http.get('/product/advantage/list').then((res) => {
						this.selectPopup = {
							show: true,
							maxNum: this.maxTagCount,
							list: res,
							showInput: true,
							complete: (res) => {
								if (this.form.advantageTags.length >= this.maxTagCount) {
									uni.showToast({
										title: '超出最大限制',
										icon: 'none'
									});
									return;
								}
								if (!uni.$u.test.isEmpty(res.inputText)) {
									this.form.advantageTags.push(res.inputText);
								} else {
									this.form.advantageTags = res.result.map((e) => e.name);
								}
							}
						};
					});
					break;
			}
		},
		// 选择图片
		chooseImage() {
			selectImage((result) => {
				if (result.filePaths.length === 0) return;
				uni.showLoading({
					title: `上传中(0/${result.filePaths.length})`,
					icon: 'none',
					mask: true
				});

				// 存储所有上传任务
				const uploadTasks = result.filePaths.map((filePath, index) => {
					return uni.$u.http
						.upload('/infra/file/upload', {
							params: { directory: 'product' },
							filePath: filePath,
							name: 'file'
						})
						.then((data) => {
							// 更新上传进度
							uni.showLoading({
								title: `上传中(${index + 1}/${result.filePaths.length})`,
								icon: 'none',
								mask: true
							});
							return data;
						});
				});

				// 批量执行上传
				Promise.all(uploadTasks)
					.then((allData) => {
						uni.hideLoading();
						// 全部上传成功后添加到列表
						this.form.imageList.push(...allData);
						uni.showToast({
							title: `成功上传${allData.length}张图片`,
							icon: 'none'
						});
					})
					.catch((err) => {
						uni.hideLoading();
						uni.showToast({
							title: `上传失败: ${err.message || '未知错误'}`,
							icon: 'none'
						});
					});
			}, this.maxPicCount - this.form.imageList.length);
		},

		// 删除图片
		deleteImage(index) {
			this.form.imageList.splice(index, 1);
		},

		// 预览图片
		previewImage(index) {
			uni.previewImage({
				current: index,
				urls: this.form.imageList
			});
		},

		// 删除服务优势标签
		deleteAdvantageTag(index) {
			this.form.advantageTags.splice(index, 1);
		},

		// 服务流程选择
		onServiceProcessItemClick(item) {
			this.form.serviceProcessIds = this.form.serviceProcessIds.includes(item.id)
				? this.form.serviceProcessIds.filter((id) => id !== item.id) // 移除
				: [...this.form.serviceProcessIds, item.id]; // 添加
		},

		// 提交准备工作
		onPrepareSubmit() {
			this.formVerify = true;

			const validations = [
				{ condition: uni.$u.test.isEmpty(this.form.imageList), message: '请上传宣传图片' },
				{ condition: uni.$u.test.isEmpty(this.form.producName), message: '请填写服务名称' },
				{ condition: uni.$u.test.isEmpty(this.form.serviceCategory), message: '请选择服务类型' },
				{ condition: uni.$u.test.isEmpty(this.form.serviceArea), message: '请选择服务区域' },
				{ condition: uni.$u.test.isEmpty(this.form.price), message: '请填写服务价格' },
				{ condition: uni.$u.test.isEmpty(this.form.advantageTags), message: '请选择服务优势' },
				{ condition: uni.$u.test.isEmpty(this.form.intro), message: '请填写服务简介' },
				{ condition: uni.$u.test.isEmpty(this.form.serviceProcessIds), message: '请选择服务流程' }
			];
			for (const e of validations) {
				if (e.condition) {
					uni.showToast({
						title: e.message,
						icon: 'none'
					});
					return;
				}
			}

			this.judgeLogin(() => {
				this.onSubmit();
			});
		},

		// 提交
		onSubmit() {
			const reqParams = {
				name: this.form.producName,
				introduction: this.form.intro,
				serviceCategoryId: this.form.serviceCategory.map((e) => e.secondId).join(','),
				serviceAreaCode: this.form.serviceArea.cityId,
				advantageTags: this.form.advantageTags,
				picUrl: this.form.imageList[0],
				sliderPicUrls: this.form.imageList,
				price: Math.round(this.form.price * 100),
				serviceProcessIds: this.form.serviceProcessIds
			};
			if (this.isEdit) {
				// 编辑
				uni.$u.http.put('/product/spu/update', { id: this.pageParams?.id, ...reqParams }, { custom: { showLoading: true, loadingMsg: '提交中..' } }).then((res) => {
					uni.showToast({
						title: '提交成功',
						icon: 'none'
					});
					this.getOpenerEventChannel().emit('reloadProductList', {});
					this.onBack();
				});
			} else {
				// 创建
				uni.$u.http
					.post('/product/spu/create', reqParams, { custom: { showLoading: true, loadingMsg: '提交中..' } })
					.then((res) => {
						// TODO 此处发布成功之后要提示去置顶呢
						this.promptPopup = {
							show: true,
							ident: 'prompt',
							icon: this.iImage('ic_popup_success.png'),
							iconStyle: 'widht:100rpx;height:100rpx;',
							title: '提交成功',
							message: '您的产品已提交成功<br/>请等待审核 ',
							actions: [
								{
									type: 'ok',
									title: '知道了',
									customStyle: 'width:240rpx;height:90rpx;border-radius: 45rpx;background: #444BF1;color:#FFFFFF;',
									action: () => {
										this.getOpenerEventChannel().emit('reloadProductList', {});
										this.onBack();
									}
								}
							],
							showClose: false
						};
					})
					.catch((err) => {
						uni.hideLoading();
						// TODO 根据状态码提示发布上线
						// this.promptPopup = {
						// 	show: true,
						// 	ident: 'prompt',
						// 	title: '发布上限',
						// 	message: '您的发布已达上限<br/>请编辑或者删除服务重新发布 ',
						// 	actions: [
						// 		{
						// 			type: 'ok',
						// 			title: '知道了',
						// 			customStyle: 'width:480rpx;height:90rpx;border-radius: 45rpx;background: #444BF1;color:#FFFFFF;',
						// 			action: () => {
						// 				this.onBack();
						// 			}
						// 		}
						// 	],
						// 	showClose: false
						// };
					});
			}
		},
		// 查询产品编辑信息
		getProductEditable() {
			// TODO此处应该根据服务类型ID获取服务流程列表
			uni.$u.http.get('/product/spu/get-editable', { params: { id: this.pageParams.id } }).then((res) => {
				console.error('得到结果', res);
				// 数据回显
				this.form.imageList = res.sliderPicUrls || [];
				this.form.producName = res.name;
				this.form.serviceCategory = [{ secondId: res.serviceCategoryId, secondName: res.serviceCategoryName }];
				this.form.serviceArea = { provinceId: res.serviceAreaProvinceCode, provinceName: res.serviceAreaProvinceName, cityId: res.serviceAreaCityCode, cityName: res.serviceAreaCityName };
				this.form.price = this.$options.filters.fen2yuanSimple(res.price);
				this.form.advantageTags = res.advantageTags;
				this.form.intro = res.introduction;
				this.form.serviceProcessIds = res.serviceProcessIds || [];
				// 审核信息 
				this.auditInfo = { auditStatus: res.auditStatus, auditReason: res.auditReason };
			});
		},
		// 查询服务流程列表
		getServiceProcessList() {
			// TODO此处应该根据服务类型ID获取服务流程列表
			uni.$u.http.get('/product/service-process/list', { params: {}, custom: { showLoading: true } }).then((res) => {
				this.processList = res;
			});
		}
	}
};
</script>

<style>
page {
	background-color: #ffffff;
}
</style>

<style lang="scss" scoped>
.audit-wrap {
	width: 100%;
	padding: 20rpx 30rpx;
	background-color: #ffeff0;
	border-radius: 15rpx;
	font-weight: 400;
	font-size: 26rpx;
	color: #f43a55;
	line-height: 36rpx;
	image {
		width: 36rpx;
		height: 36rpx;
		margin-right: 10rpx;
		flex-shrink: 0;
	}
}

.wrap {
	padding: 30rpx;

	&__normal-cell {
		padding: 20rpx 0;
		align-items: flex-start;

		&__text {
			font-weight: 500;
			font-size: 30rpx;
			color: #1e2134;
			line-height: 60rpx;
			white-space: nowrap;
		}

		&__input {
			flex: 1;
			height: 60rpx;
			margin-left: 45rpx;
			font-size: 30rpx;
			color: #1e2134;
		}

		&__tag-select {
			flex: 1;
			margin-left: 45rpx;
			font-size: 30rpx;

			&__placeholder {
				line-height: 60rpx;
				font-size: 30rpx;
				color: #666;
			}
			&__content {
				display: flex;
				flex-wrap: wrap;
				margin: 0 -8rpx 0 -8rpx;
				&__tag {
					height: 44rpx;
					margin: 8rpx;
					padding: 0 10rpx;
					background: #f3f3f5;
					border-radius: 4rpx;
					border: 1px solid #d8d8db;
					&__name {
						font-weight: 400;
						font-size: 26rpx;
						color: #1e2134;
					}
					&__delete {
						width: 20rpx;
						height: 20rpx;
						margin-left: 5rpx;
					}
				}
			}
		}

		&__list {
			margin-left: 45rpx;
			&__item {
				line-height: 60rpx;
				justify-content: flex-start;

				&__ic {
					width: 30rpx;
					height: 30rpx;
					margin-right: 10rpx;
					flex-shrink: 0;
				}

				&__text {
					font-weight: 400;
					font-size: 30rpx;
					color: #333333;
				}
			}
		}

		&__arrow {
			width: 10rpx;
			height: 60rpx;
		}

		&__prefix {
			line-height: 60rpx;
			font-weight: 400;
			font-size: 30rpx;
			color: #1e2134;
		}
	}

	&__pics-cell {
		margin-bottom: 30rpx;

		&__title {
			font-weight: 500;
			font-size: 30rpx;
			color: #1e2134;
			white-space: nowrap;
		}

		&__content {
			display: flex;
			flex-wrap: wrap;
			margin: 20rpx -7rpx 0 -7rpx;

			/* 抵消子项margin实现间距 */
			.item {
				width: 220rpx;
				height: 166rpx;
				margin: 7rpx;
				/* 14rpx的一半，因为两边都有 */
				position: relative;
				border-radius: 8rpx;
				overflow: hidden;
			}

			/* 添加按钮样式 */
			&__add {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				background-color: #ffffff;
				border: 1px dashed #ddd;

				image {
					width: 39rpx;
					height: 39rpx;
					margin-bottom: 10rpx;
				}

				text {
					font-size: 24rpx;
					color: #909099;
				}
			}

			&__img {
				width: 220rpx;
				height: 166rpx;
				margin: 7rpx;
				/* 14rpx的一半，因为两边都有 */
				position: relative;
				background-color: #f5f5f5;
				border-radius: 8rpx;

				&__preview {
					width: 100%;
					height: 100%;
				}

				&__delete {
					position: absolute;
					right: -20rpx;
					top: -20rpx;
					width: 40rpx;
					height: 40rpx;
					z-index: 2;
				}
			}
		}
	}
}

/* 校验 */
.verify {
	animation: shake 0.2s ease-in-out 1; /* 抖动1次 */

	.wrap__normal-cell__text,
	.wrap__pics-cell__title {
		font-weight: bold;
		font-size: 30rpx;
		color: red;
	}
}

@keyframes shake {
	0%,
	100% {
		transform: translateX(0);
	}
	25% {
		transform: translateX(-8px);
	}
	50% {
		transform: translateX(8px);
	}
	75% {
		transform: translateX(-8px);
	}
}

.submit {
	flex: 1;
	margin: 30rpx;
	height: 100rpx;
	min-height: 100rpx;
	margin-top: 15rpx;
	background: #444bf1;
	border-radius: 10rpx;
	font-weight: bold;
	font-size: 32rpx;
	color: #ffffff;
	justify-content: center;
}
</style>
