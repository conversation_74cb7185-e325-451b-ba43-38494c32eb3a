<template>
	<view>
		<public-module></public-module>
		<z-paging ref="paging" v-model="dataList" :auto-clean-list-when-reload="false" show-refresher-when-reload @query="queryList">
			<template #top>
				<u-navbar title="我的发布" bgColor="transparent" :statusBar="true" :placeholder="true" :autoBack="true" />
			</template>

			<custom-empty slot="empty" message="暂无发布"></custom-empty>
			<!-- 产品列表 -->
			<view class="flex wrap-list">
				<list-publish-product-item class="wrap-list__item" :item="item" v-for="(item, index) in dataList" :key="index" @click="onListItemClick" @childClick="onListChildItemClick"></list-publish-product-item>
			</view>
		</z-paging>

		<view class="flex fixed-publish" @click="onPublishBtnClick">+</view>
		<!-- 通用操作弹窗 -->
		<u-popup :show="promptPopup.show" bgColor="transparent" mode="center" :close-on-click-overlay="false" @close="promptPopup.show = false">
			<popup-prompt
				:ident="promptPopup.ident"
				:icon="promptPopup.icon"
				:iconStyle="promptPopup.iconStyle"
				:title="promptPopup.title"
				:message="promptPopup.message"
				:actions="promptPopup.actions"
				:tipText="promptPopup.tipText"
				:buttonBoxJustifyContent="promptPopup.buttonBoxJustifyContent"
				:customStyle="promptPopup.customStyle"
				:showClose="promptPopup.showClose"
				@cancel="promptPopup.show = false"
			></popup-prompt>
		</u-popup>
	</view>
</template>

<script>
import ListPublishProductItem from '../component/list-publish-product-item/list-publish-product-item.vue';
export default {
	components: {
		ListPublishProductItem
	},
	data() {
		return {
			// 数据列表
			dataList: [],
			// 通用弹窗
			promptPopup: { show: false }
		};
	},
	onLoad(options) {},
	methods: {
		// 发布产品
		onPublishBtnClick() {
			this.goPublishPage();
		},
		// 列表条目点击
		onListItemClick(e) {
			this.onJump({
				url: '/pagesHome/product/detail',
				params: {
					id: e.detail.id
				}
			});
		},
		// 列表元素点击
		onListChildItemClick(e) {
			switch (e.type) {
				case 'delete':
					this.promptPopup = {
						show: true,
						ident: 'prompt',
						title: '温馨提示',
						message: '您确定要删除该产品？',
						actions: [
							{
								type: 'cancel',
								title: '取消',
								customStyle: 'width:240rpx;background: #FFFFFF;color:#444BF1;border: 0.8px solid #444BF1;',
								action: () => {
									this.promptPopup.show = false;
								}
							},
							{
								type: 'confirm',
								title: '删除',
								customStyle: 'width:240rpx;background: #444BF1;color:#FFFFFF;',
								action: () => {
									this.promptPopup.show = false;
									this.deleteProduct(e.detail.id);
								}
							}
						],
						showClose: false
					};
					break;
				case 'edit':
					this.goPublishPage({ id: e.detail.id });
					break;
				case 'pin':
					this.onJump({
						url: '/pagesMine/pin/index',
						params: { pinBizType: this.cons.PIN_BIZ_TYPE_PRODUCT, id: e.detail.id },
						events: {
							reloadProductList: (res) => {
								this.$refs.paging.reload(false);
							}
						}
					});
					break;
			}
		},
		// 跳转发布产品页面
		goPublishPage(params = {}) {
			this.onJump({
				url: '/pagesMine/publish/product/publish',
				params: params,
				events: {
					reloadProductList: (res) => {
						this.$refs.paging.reload(false);
					}
				}
			});
		},
		// 删除
		deleteProduct(id) {
			uni.showLoading({ title: '正在删除..', mask: true });
			uni.$u.http
				.delete('/product/spu/delete', {}, { params: { id: id } })
				.then((res) => {
					this.$refs.paging.reload(false);
					uni.hideLoading();
				})
				.catch((err) => {
					uni.hideLoading();
				});
		},
		// 查询列表
		queryList(pageNo, pageSize) {
			uni.$u.http
				.get('/product/spu/my-page', {
					params: {
						pageNo: pageNo,
						pageSize: pageSize
					}
				})
				.then((res) => {
					this.$refs.paging.complete(res.list);
				})
				.catch((err) => {
					this.$refs.paging.complete(false);
				});
		}
	}
};
</script>

<style lang="scss" scoped>
/* 列表 */
.wrap-list {
	padding: 0 30rpx;
	background-color: #f7f7f7;
	flex-direction: column;

	&__item {
		width: 100%;
		margin-top: 20rpx;
	}
}

/* 发布按钮 */
.fixed-publish {
	width: 120rpx;
	height: 120rpx;
	background-color: #f43a55;
	border-radius: 50%;
	justify-content: center;
	color: #ffffff;
	font-weight: bold;
	font-size: 90rpx;
	position: fixed;
	right: 30rpx;
	bottom: 80rpx;
}
</style>
