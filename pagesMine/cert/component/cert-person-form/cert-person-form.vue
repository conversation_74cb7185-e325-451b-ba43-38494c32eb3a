<template>
	<view class="flex-col idcard-wrap">
		<view class="flex idcard-wrap__item">
			<text>真实姓名</text>
			<input v-model="form.username" placeholder="请填写真实姓名" />
			<view class="flex idcard-wrap__item__camera" @click="onSelectImage">
				<image :src="iImage('ic_camera.png', 'pagesMine')" mode="aspectFit" />
			</view>
		</view>
		<view class="flex idcard-wrap__item">
			<text>身份证号</text>
			<input v-model="form.idNumber" placeholder="请填写身份证号" />
			<view class="flex idcard-wrap__item__camera" @click="onSelectImage">
				<image :src="iImage('ic_camera.png', 'pagesMine')" mode="aspectFit" />
			</view>
		</view>
		<view class="flex idcard-wrap__item" style="background-color: #f3f3f5">
			<text>本人电话</text>
			<input v-model="form.mobile" placeholder="请填写本人电话" disabled />
		</view>
	</view>
</template>

<script>
export default {
	name: 'person-form',
	props: {
		// 认证信息
		cert: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			form: {
				// 用户姓名
				username: null,
				// 身份证号码
				idNumber: null,
				// 本人手机
				mobile: null
			}
		};
	},
	watch: {
		// 监听弹窗状态
		cert: {
			handler(newVal, oldVal) {
				this.updateForm();
			},
			deep: true,
			immediate: true
		}
	},
	mounted() {
		this.updateForm();
	},
	methods: {
		updateForm() {
			console.error('======*****======', this.cert);
			this.form.username = this.cert?.realName || '';
			this.form.idNumber = this.cert?.idCardNumber || '';
			this.form.mobile = this.cert?.mobile || '';
		},
		onSelectImage() {
			this.$emit('selectImage', 'idcard');
		},
		verifyForm() {
			const validations = [
				{ condition: uni.$u.test.isEmpty(this.form.username), message: '请填写真实姓名' },
				{ condition: uni.$u.test.isEmpty(this.form.idNumber), message: '请填写身份证号' },
				{ condition: uni.$u.test.isEmpty(this.form.mobile), message: '请填写本人电话' }
			];
			for (const e of validations) {
				if (e.condition) {
					uni.showToast({
						title: e.message,
						icon: 'none'
					});
					return null;
				}
			}
			return this.form;
		}
	}
};
</script>

<style lang="scss" scoped>
.idcard-wrap {
	padding: 30rpx;

	&__item {
		height: 90rpx;
		border-radius: 10rpx;
		border: 0.8px solid #d8d8db;

		text {
			margin-left: 20rpx;
			font-weight: 500;
			font-size: 30rpx;
			color: #1e2134;
			white-space: nowrap;

			&:before {
				content: '*';
				color: red;
			}
		}
		input {
			flex: 1;
			margin-left: 10rpx;
			font-size: 30rpx;
			color: #a8a8a8;
		}

		&__camera {
			width: 50rpx;
			height: 100%;
			image {
				width: 30rpx;
				height: 30rpx;
			}
		}
	}
}

.idcard-wrap > view:not(:first-child) {
	margin-top: 30rpx;
}
</style>
