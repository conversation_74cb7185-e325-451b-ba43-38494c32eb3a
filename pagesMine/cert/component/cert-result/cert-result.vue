<template>
	<view class="result-wrap" v-if="show">
		<view class="flex result-wrap__content">
			<text>{{ displayText }}</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'cert-result',
	props: {
		// 认证信息
		cert: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {}; 
	},
	computed: {
		// 审核驳回才会显示该组件
		show() {
			return this.cert?.auditStatus == this.cons.CERTIFICATION_STATUS_REJECTED;
		},
		displayText() {
			return this.cert?.auditReason || '';
		}
	},
	methods: {}
};
</script>

<style lang="scss" scoped>
.result-wrap {
	&__content {
		padding: 20rpx 30rpx;
		background-color: #ffeff0;
		border-radius: 15rpx;
		font-weight: 400;
		font-size: 26rpx;
		color: #f43a55;
		line-height: 40rpx;
	}
}
</style>
