<template>
	<view class="flex-col idcard-wrap">
		<view class="flex idcard-wrap__license">
			<view class="flex-col idcard-wrap__license__title">
				<text class="idcard-wrap__license__text1">上传营业执照</text>
				<text class="idcard-wrap__license__text2">上传营业执照原件，系统将自动 识别填写证件信息</text>
			</view>
			<image :src="form.businessLicensePic || iImage('img_default_business_license.png', 'pagesMine')" mode="aspectFill" @click="onSelectImage('business')" />
		</view>
		<view class="flex idcard-wrap__item">
			<text>公司名称</text>
			<input v-model="form.businessName" placeholder="上传后自动填写 " />
			<view class="flex idcard-wrap__item__camera" @click="onSelectImage('business')">
				<image :src="iImage('ic_camera.png', 'pagesMine')" mode="aspectFit" />
			</view>
		</view>
		<view class="flex idcard-wrap__item">
			<text>信用代码</text>
			<input v-model="form.businessCode" placeholder="上传后自动填写" />
			<view class="flex idcard-wrap__item__camera" @click="onSelectImage('business')">
				<image :src="iImage('ic_camera.png', 'pagesMine')" mode="aspectFit" />
			</view>
		</view>
		<text class="idcard-wrap__subtitle">上传个人证件信息</text>
		<view class="flex idcard-wrap__item" style="margin-top: 20rpx">
			<text>真实姓名</text>
			<input v-model="form.username" placeholder="请填写真实姓名" />
			<view class="flex idcard-wrap__item__camera" @click="onSelectImage('idcard')">
				<image :src="iImage('ic_camera.png', 'pagesMine')" mode="aspectFit" />
			</view>
		</view>
		<view class="flex idcard-wrap__item">
			<text>身份证号</text>
			<input v-model="form.idNumber" placeholder="请填写身份证号" />
			<view class="flex idcard-wrap__item__camera" @click="onSelectImage('idcard')">
				<image :src="iImage('ic_camera.png', 'pagesMine')" mode="aspectFit" />
			</view>
		</view>
		<view class="flex idcard-wrap__item" style="background-color: #f3f3f5; border: none">
			<text>本人电话</text>
			<input v-model="form.mobile" placeholder="请填写本人电话" disabled />
		</view>
	</view>
</template>

<script>
export default {
	name: 'business-form',
	props: {
		// 认证信息
		cert: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			form: {
				// 营业执照图片
				businessLicensePic: '',
				// 企业名称
				businessName: '',
				// 企业代码
				businessCode: '',
				// 用户姓名
				username: '',
				// 身份证号码
				idNumber: '',
				// 本人手机
				mobile: ''
			}
		};
	},
	watch: {
		// 监听弹窗状态
		cert: {
			handler(newVal, oldVal) {
				this.updateForm();
			},
			deep:true,
			immediate: true
		}
	},
	mounted() {
		this.updateForm();
	},
	methods: {
		updateForm() {
			this.form.businessLicensePic = this.cert?.corpLicenseUrl || '';
			this.form.businessName = this.cert?.corpName || '';
			this.form.businessCode = this.cert?.corpLicenseNumber || '';
			this.form.username = this.cert?.realName || '';
			this.form.idNumber = this.cert?.idCardNumber || '';
			this.form.mobile = this.cert?.mobile || '';
		},
		onSelectImage(type) {
			this.$emit('selectImage', type);
		},
		verifyForm() {
			const validations = [
				{ condition: uni.$u.test.isEmpty(this.form.businessName), message: '请上传营业执照' },
				{ condition: uni.$u.test.isEmpty(this.form.businessCode), message: '请上传营业执照' },
				{ condition: uni.$u.test.isEmpty(this.form.username), message: '请填写真实姓名' },
				{ condition: uni.$u.test.isEmpty(this.form.idNumber), message: '请填写身份证号' },
				{ condition: uni.$u.test.isEmpty(this.form.mobile), message: '请填写本人电话' }
			];
			for (const e of validations) {
				if (e.condition) {
					uni.$u.toast(e.message);
					return;
				}
			}
			return this.form;
		}
	}
};
</script>

<style lang="scss" scoped>
.idcard-wrap {
	padding: 30rpx;

	&__license {
		justify-content: space-between;
		align-items: flex-start;
		&__text1 {
			font-weight: 500;
			font-size: 30rpx;
			color: #1e2134;
			white-space: nowrap;
			&:before {
				content: '*';
				color: red;
			}
		}
		&__text2 {
			max-width: 330rpx;
			margin-top: 15rpx;
			font-weight: 400;
			font-size: 24rpx;
			color: #ff0000;
			line-height: 30rpx;
		}

		image {
			width: 245rpx;
			height: 165rpx;
			border-radius: 15rpx;
		}
	}

	&__item {
		height: 90rpx;
		border-radius: 10rpx;
		border: 0.8px solid #d8d8db;

		text {
			margin-left: 20rpx;
			font-weight: 500;
			font-size: 30rpx;
			color: #1e2134;
			white-space: nowrap;

			&:before {
				content: '*';
				color: red;
			}
		}
		input {
			flex: 1;
			margin-left: 10rpx;
			font-size: 30rpx;
			color: #a8a8a8;
		}

		&__camera {
			width: 50rpx;
			height: 100%;
			image {
				width: 30rpx;
				height: 30rpx;
			}
		}
	}

	&__subtitle {
		margin-top: 30rpx;
		font-size: 24rpx;
		color: #999999;
	}
}

.idcard-wrap > view:not(:first-child) {
	margin-top: 30rpx;
}
</style>
