<template>
	<view class="wrap">
		<public-module></public-module>
		<u-navbar bgColor="transparent" title="入驻认证" :statusBar="true" :placeholder="true" :autoBack="true" />

		<!-- 认证状态 -->
		<cert-result :cert="certDetail" v-if="!$u.test.isEmpty(certDetail)"></cert-result>

		<view class="flex wrap__top">
			<view class="flex-col" style="align-items: center">
				<text class="wrap__top__text1">入驻平台在线获客</text>
				<text class="wrap__top__text2">低成本获客 | 精准推送获客 | 品牌曝光</text>
			</view>
			<image :src="iImage('img_identity_adorn.png', 'pagesMine')"></image>
		</view>
		<!-- 表单 -->
		<view class="flex-col wrap__content">
			<view class="wrap__content__radio">
				<u-radio-group v-model="certType" activeColor="#444BF1">
					<u-radio :customStyle="{ marginRight: '50rpx' }" v-for="(item, index) in certTypeList" :key="index" :label="item.name" :name="item.type" labelColor="#333333" labelSize="30rpx"></u-radio>
				</u-radio-group>
			</view>

			<cert-person-form ref="personFormRef" :cert="certDetail" v-show="certType == cons.CERTIFICATION_TYPE_PERSONAL" @selectImage="handleSelectImage"></cert-person-form>
			<cert-business-form ref="businessFormRef" :cert="certDetail" v-show="certType == cons.CERTIFICATION_TYPE_COMPANY" @selectImage="handleSelectImage"></cert-business-form>
		</view>

		<view class="wrap__bottom">
			<view class="flex">
				<u-checkbox-group v-model="protocolValues" inactiveColor="#333333" activeColor="#444BF1" @change="onProtocolStateChange">
					<u-checkbox name="procotol" shape="circle" label="我已阅读并同意"></u-checkbox>
				</u-checkbox-group>
				<text style="font-size: 14px; color: #444bf1" @click="onJump({ url: '/pagesMine/privacy/agreement', params: { type: cons.PROTOCOL_CERT } })">{{ `《${cons.PROTOCOL_MAPS[cons.PROTOCOL_CERT]}》` }}</text>
			</view>
			<view class="flex wrap__bottom__submit" @click="onPrepareCert">下一步</view>
		</view>

		<view class="flex wrap__tooltip">注：信息仅用于身份核验、信用审核；不会对信息做任何采集与保留，请放心使用。</view>

		<!-- 通用操作弹窗 -->
		<u-popup :show="promptPopup.show" bgColor="transparent" mode="center" :close-on-click-overlay="false" @close="promptPopup.show = false">
			<popup-prompt
				:ident="promptPopup.ident"
				:icon="promptPopup.icon"
				:iconStyle="promptPopup.iconStyle"
				:title="promptPopup.title"
				:message="promptPopup.message"
				:messageStyle="promptPopup.messageStyle"
				:backgroundImage="promptPopup.backgroundImage"
				:actions="promptPopup.actions"
				:tipText="promptPopup.tipText"
				:customStyle="promptPopup.customStyle"
				:showClose="promptPopup.showClose"
				@cancel="promptPopup.show = false"
			></popup-prompt>
		</u-popup>
		<!-- 协议弹窗 -->
		<u-popup :show="agreementPopup.show" bgColor="transparent" mode="bottom" :closeOnClickOverlay="false" :safeAreaInsetBottom="false" :closeable="true" @close="agreementPopup.show = false">
			<popup-agreement :type="agreementPopup.type" :title="agreementPopup.title" :isAgree="agreementPopup.isAgree" @close="agreementPopup.show = false" @confirm="onConfirmAgreemet"></popup-agreement>
		</u-popup>
	</view>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import { aesEncrypt } from '@/common/aesHelper.js';
import { selectImage } from '@/common/utils.js';
import { pathToBase64 } from '@/plugins/img-base64.js';
import CertPersonForm from './component/cert-person-form/cert-person-form.vue';
import CertBusinessForm from './component/cert-business-form/cert-business-form.vue';
import CertResult from './component/cert-result/cert-result.vue';
export default {
	components: {
		CertPersonForm,
		CertBusinessForm,
		CertResult
	},
	data() {
		return {
			// 认证身份类型
			certTypeList: [],
			certType: null,
			// 认证信息
			certDetail: {},
			// 使用协议
			protocolValues: [],
			// 通用操作弹窗
			promptPopup: {
				show: false,
				ident: 'prompt',
				showClose: false
			},
			// 协议弹窗
			agreementPopup: {
				show: false
			}
		};
	},
	computed: {
		...mapState(['currentAddress', 'userInfo'])
	},
	onLoad() {
		this.certTypeList = [
			{ type: this.cons.CERTIFICATION_TYPE_PERSONAL, name: '个人身份' },
			{ type: this.cons.CERTIFICATION_TYPE_COMPANY, name: '企业身份' }
		];
		// 默认个人身份
		this.certType = this.certTypeList[0].type;

		// 查询认证信息
		this.queryCert();
	},
	methods: {
		...mapActions(['agreementProtocol']),
		// 选择图片
		handleSelectImage(type) {
			const that = this;
			selectImage((imgRes) => {
				const filePath = imgRes.filePaths[0];
				pathToBase64(filePath).then((imageBase64) => {
					this.imageOCR(type, imageBase64, (ocr) => {
						if (type == 'business') {
							this.certDetail.corpLicenseUrl = filePath;
							this.certDetail.corpName = ocr.companyName;
							this.certDetail.corpLicenseNumber = ocr.creditCode;
							this.$refs.businessFormRef.updateForm();
						} else {
							this.certDetail.realName = ocr.name;
							this.certDetail.idCardNumber = ocr.idNumber;
							this.$refs.personFormRef.updateForm();
						}
					});
				});

				// uni.$u.http
				// 	.upload('/infra/file/upload', {
				// 		params: { directory: 'cert' },
				// 		custom: { showLoading: true, loadingMsg: '上传中..' },
				// 		filePath: imgRes.path,
				// 		name: 'file'
				// 	})
				// 	.then((res) => {})
				// 	.catch((err) => {
				// 		uni.hideLoading();
				// 	});
			});
		},
		// 图片识别
		imageOCR(type = '', imageBase64, callback) {
			const url = type == 'business' ? '/member/certification/ocr/business-license' : '/member/certification/ocr/id-card';
			uni.$u.http
				.post(url, { imageBase64 }, { custom: { showLoading: true } })
				.then((res) => {
					callback && callback(res);
				})
				.catch((err) => {});
		},
		// 协议状态改变回调
		showProtocolPopup() {
			this.agreementPopup = {
				show: true,
				type: this.cons.PROTOCOL_CERT,
				title: this.cons.PROTOCOL_MAPS[this.cons.PROTOCOL_CERT],
				isAgree: !uni.$u.test.isEmpty(this.protocolValues)
			};
		},
		onProtocolStateChange(e) {
			if (!uni.$u.test.isEmpty(e)) {
				this.protocolValues = [];
				this.showProtocolPopup();
			} else {
				this.protocolValues = e;
			}
		},
		// 确认协议
		onConfirmAgreemet() {
			this.agreementPopup.show = false;
			this.protocolValues = ['procotol'];
			this.onPrepareCert();
		},
		// 认证完成
		hanleRealAuthFinish() {
			this.promptPopup = {
				show: true,
				icon: '/static/image/ic_alert_wait.png',
				title: '认证审核中',
				message: `您的认证信息已提交成功<br/>3个工作日内完成审核`,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: [
					{
						type: 'cancel',
						title: '知道了',
						customStyle: 'width:240rpx;height:90rpx;border-radius: 30rpx;background: #444BF1;color:#FFFFFF;',
						action: () => {
							this.onBack();
						}
					}
				],
				showClose: false
			};
		},
		// 准备认证
		onPrepareCert() {
			if (uni.$u.test.isEmpty(this.protocolValues)) {
				this.showProtocolPopup()
				return;
			}

			const params = { certType: this.certType };
			if (this.certType == this.cons.CERTIFICATION_TYPE_COMPANY) {
				const form = this.$refs.businessFormRef.verifyForm();
				if (form == null) {
					return;
				}
				params.corpName = form.businessName;
				params.corpLicenseNumber = form.businessCode;
				params.corpLicenseUrl = form.businessLicensePic;
				params.realName = form.username;
				params.idCardNumber = form.idNumber;
				params.mobile = form.mobile;
			} else {
				const form = this.$refs.personFormRef.verifyForm();
				if (form == null) {
					return;
				}
				params.realName = form.username;
				params.idCardNumber = form.idNumber;
				params.mobile = form.mobile;
			}

			if (uni.$u.test.isEmpty(this.protocolValues)) {
				this.showProtocolPopup();
				return;
			}

			uni.$u.http
				.post('/member/certification/submit', params, { custom: { showLoading: true } })
				.then((res) => {
					// 协议状态提交服务器
					this.agreementProtocol([this.cons.PROTOCOL_ENTER]);
					// 下一步
					this.onJump({ url: '/pagesMine/provider/profile', params: { certType: this.certType }, login: true });
				})
				.catch((err) => {});
		},
		// 查询认证信息
		queryCert() {
			uni.$u.http
				.get('/member/certification/get')
				.then((res) => {
					if (uni.$u.test.isEmpty(res)) {
						this.certDetail = { mobile: this.userInfo?.mobile };
					} else {
						this.certType = res.certType;
						this.certDetail = res;
						// 有认证信息说明提交过，所以默认选中协议
						this.protocolValues = ['procotol'];
					}
				})
				.catch((err) => {});
		}
	}
};
</script>

<style>
page {
	background: linear-gradient(to bottom, #fcf0f4, #f3ebf6 20%, #ffffff 40%);
}
</style>

<style lang="scss" scoped>
.wrap {
	&__top {
		margin: 0 30rpx;
		justify-content: center;
		align-items: center;
		&__text1 {
			font-weight: bold;
			font-size: 46rpx;
			color: #1e2134;
			line-height: 60rpx;
		}
		&__text2 {
			font-size: 24rpx;
			color: #1e2134;
			line-height: 60rpx;
			opacity: 0.77;
		}

		image {
			width: 180rpx;
			height: 160rpx;
			margin-left: auto;
			flex-shrink: 0;
		}
	}

	&__content {
		margin: 30rpx 30rpx 0 30rpx;
		background-color: #ffffff;
		border-radius: 30rpx 30rpx 0rpx 0rpx;

		&__radio {
			padding: 40rpx 30rpx 10rpx 30rpx;
		}
	}

	&__bottom {
		flex: 1;
		padding: 15rpx 60rpx;
		background: transparent;

		&__submit {
			width: 100%;
			height: 100rpx;
			min-height: 100rpx;
			margin-top: 15rpx;
			background: #444bf1;
			border-radius: 10rpx;
			font-weight: bold;
			font-size: 32rpx;
			color: #ffffff;
			justify-content: center;
		}
	}

	&__tooltip {
		margin: 30rpx 60rpx;
		font-size: 24rpx;
		color: #ff0000;
	}
}
</style>
