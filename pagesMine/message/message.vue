<template>
	<view>
		<z-paging ref="paging" :auto="false" v-model="dataList" @query="queryList">
			<view slot="top">
				<u-navbar bgColor="transparent" title="消息" :statusBar="true" :placeholder="true" :autoBack="true"></u-navbar>
				<x-official-account></x-official-account>
			</view>
			<custom-empty slot="empty" message="暂无消息"></custom-empty>
			<view class="flex wrap-list">
				<view class="flex wrap-list__item" v-for="(item, index) in dataList" :key="index" @click="onListItemClick(index,item)">
					<view class="flex wrap-list__item__title">
						<image class="wrap-list__item__title__icon" src="/pagesMine/static/ic_list_msg.png"></image>
						<text class="wrap-list__item__title__text">{{ item.title }}</text>
						<text class="wrap-list__item__title__time">{{ item.createtime }}</text>
					</view>
					<view class="flex wrap-list__item__content">
						<rich-text class="content" :nodes="item.message"></rich-text>
					</view>
					<view class="flex wrap-list__item__btn" v-if="!$u.test.isEmpty(item.next_key)">{{ item.next_name }}</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';
import { openMiniWx } from '@/common/utils.js';
import xOfficialAccount from '@/components/x-official-account/x-official-account.vue';
export default {
	components: {
		xOfficialAccount
	},
	data() {
		return {
			// 初始化为未加载
			isFirstLoad: true,
			// 记录当前点击条目的位置[为了解决防止每次刷新自动跳转到顶部的问题]
			itemIndex: -1,
			// 数据列表
			dataList: []
		};
	},
	computed: {
		...mapState(['userInfo'])
	},
	onLoad(options) {},
	onShow() {
		this.$nextTick(() => {
			this.refreshToPage();
			this.isFirstLoad = false;
		});
		// 消失红点
		uni.$u.http.post('/user/userClick', { type: 'message' });
	},
	methods: {
		// 点击列表条目
		onListItemClick(index,item) {
			this.itemIndex = index;
			const params = uni.$u.test.jsonString(item.extend) ? JSON.parse(item.extend) : item.extend;
			switch (item.type) {
				case 'order_acc': // 师傅已接单
				case 'visit_conf': // 师傅确认上门
				case 'svc_comp': // 师傅确认完成
				case 'refund_end': // 退单结束
				case 'return_req': //申请退回大厅
					this.onJump({
						url: '/pagesOrder/order/order-detail',
						params: { id: params?.product_id }
					});
					break;
				case 'follow_pub':
					// 关注公众号
					this.judgeLogin(() => {
						openMiniWx(iWxMiniProgram('oa', this.$conf.miniProgramId));
					});
					break;
			}
		},
		// 刷新数据，刷新列表数据至指定页。
		refreshToPage() {
			if (this.isFirstLoad) {
				this.$refs.paging && this.$refs.paging.reload(true);
				return;
			}
			if (this.itemIndex == -1) {
				this.$refs.paging && this.$refs.paging.reload(false);
				console.log('消息列表--', `重置分页刷新数据`);
			} else {
				const currentPageNo = Math.ceil((this.itemIndex + 1) / 10);
				this.$refs.paging && this.$refs.paging.refreshToPage(currentPageNo);
				console.log('消息列表--', `刷新数据到指定分页【${currentPageNo}】`);
			}
		},
		// 查询数据列表
		queryList(pageNo, pageSize) {
			uni.$u.http
				.post('/msg/getList', {
					page: pageNo,
					limit: pageSize
				})
				.then((res) => {
					this.$refs.paging.complete(res.data);
				})
				.catch((err) => {
					this.$refs.paging.complete(false);
				});
		}
	}
};
</script>

<style lang="scss" scoped>
.wrap-list {
	width: 100%;
	flex-direction: column;
	&__item {
		width: calc(100% - 60rpx);
		margin: 20rpx 30rpx 0 30rpx;
		padding: 30rpx 20rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		flex-direction: column;
		align-items: flex-start;
		&__title {
			width: 100%;
			&__icon {
				width: 50rpx;
				height: 50rpx;
			}
			&__text {
				margin-left: 20rpx;
				font-size: 30rpx;
				font-weight: 600;
				color: #333333;
			}
			&__time {
				margin-left: auto;
				font-size: 24rpx;
				color: #999999;
				white-space: nowrap;
			}
		}
		&__content {
			margin-top: 10rpx;
			margin-left: 70rpx;
			font-size: 26rpx;
			color: #666666;
			line-height: 35rpx;
		}
		&__btn {
			margin-top: 20rpx;
			margin-left: auto;
			font-size: 24rpx;
			color: #444BF1;
		}
	}
}
</style>
