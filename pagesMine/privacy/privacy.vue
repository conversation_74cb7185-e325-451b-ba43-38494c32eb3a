<template>
	<view>
		<public-module></public-module>
		<u-navbar title="隐私" :fixed="true" :statusBar="true" :placeholder="true" autoBack></u-navbar>
		<!-- #ifdef APP-PLUS -->
		<view class="flex wrap-item" @click="onItmeClick('sys-setting')">
			<text class="wrap-item__name">管理App系统权限</text>
			<image class="wrap-item__arrow" src="/static/image/ic_small_arrow_right.png"></image>
		</view>
		<!-- #endif -->
		<view class="flex wrap-item" @click="onItmeClick('signed-agreement')">
			<text class="wrap-item__name">已签署协议</text>
			<image class="wrap-item__arrow" src="/static/image/ic_small_arrow_right.png"></image>
		</view>
		<view class="flex wrap-item" @click="onItmeClick('user-agreement')">
			<text class="wrap-item__name">用户协议</text>
			<image class="wrap-item__arrow" src="/static/image/ic_small_arrow_right.png"></image>
		</view>
		<view class="flex wrap-item" @click="onItmeClick('privacy-agreement')">
			<text class="wrap-item__name">隐私政策</text>
			<image class="wrap-item__arrow" src="/static/image/ic_small_arrow_right.png"></image>
		</view>
		<view class="flex wrap-item" @click="onItmeClick('personal-agreement')">
			<text class="wrap-item__name">个人信息收集清单</text>
			<image class="wrap-item__arrow" src="/static/image/ic_small_arrow_right.png"></image>
		</view>
		<view class="flex wrap-item" @click="onItmeClick('third-agreement')">
			<text class="wrap-item__name">第三方信息共享清单</text>
			<image class="wrap-item__arrow" src="/static/image/ic_small_arrow_right.png"></image>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {};
	},
	methods: {
		// 操作按钮点击
		onItmeClick(id) {
			switch (id) {
				case 'sys-setting': // 管理App系统权限
					// #ifdef APP-PLUS
					uni.openAppAuthorizeSetting();
					// #endif
					break;
				case 'signed-agreement': // 已签署协议
					this.onJump({ url: '/pagesMine/privacy/signed-agreements' });
					break;
				case 'user-agreement': // 用户协议
					this.onJump({ url: '/pagesMine/privacy/agreement', params: { type: this.cons.PROTOCOL_USER } });
					break;
				case 'privacy-agreement': // 隐私政策
					this.onJump({ url: '/pagesMine/privacy/agreement', params: { type: this.cons.PROTOCOL_PRIVACY } });
					break;
				case 'personal-agreement': // 个人信息收集清单
					this.onJump({ url: '/pagesMine/privacy/agreement', params: { type: this.cons.PROTOCOL_PERSONAL_INFO_LIST } });
					break;
				case 'third-agreement': // 第三方信息共享清单
					this.onJump({ url: '/pagesMine/privacy/agreement', params: { type: this.cons.PROTOCOL_THIRD_INFO_LIST } });
					break;
			}
		}
	}
};
</script>

<style>
page {
	background: #ffffff;
}
</style>
<style lang="scss" scoped>
.wrap-item {
	width: 100%;
	padding: 35rpx 30rpx;
	justify-content: space-between;
	&__name {
		font-weight: 500;
		font-size: 30rpx;
		color: #333333;
	}
	&__value {
		margin-left: auto;
		font-weight: 500;
		font-size: 28rpx;
		color: #666666;
	}
	&__arrow {
		width: 12rpx;
		height: 20rpx;
		margin-left: 15rpx;
	}
}
.exit {
	width: 690rpx;
	height: 100rpx;
	margin: 160rpx auto 0 auto;
	background: #444BF1;
	border-radius: 30rpx;
	justify-content: center;
	font-weight: bold;
	font-size: 36rpx;
	color: #ffffff;
}

.icp {
	justify-content: center;
	position: fixed;
	left: 0;
	right: 0;
	bottom: 200rpx;
	&__text {
		font-weight: 500;
		font-size: 22rpx;
		color: #999999;
	}
	&__arrow {
		width: 12rpx;
		height: 20rpx;
		margin-left: 15rpx;
	}
}
</style>
