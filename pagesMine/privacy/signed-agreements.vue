<template>
	<view>
		<public-module></public-module>
		<z-paging ref="paging" :loading-more-enabled="false" v-model="dataList" @query="queryList">
			<u-navbar slot="top" bgColor="#FFFFFF" title="已签署协议" :statusBar="true" :placeholder="true" :autoBack="true"></u-navbar>
			<view class="flex wrap-list">
				<view class="flex wrap-list__item" v-for="(item, index) in dataList" :key="index" @click="onClickItem(item)">
					<text class="wrap-list__item__title">{{item.title}}</text>
					<text class="wrap-list__item__status">已签署</text>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
export default {
	data() {
		return {
			dataList: []
		};
	},
	methods: {
		// 查询数据列表
		queryList(pageNo, pageSize) {
			uni.$u.http
				.post('/user/getUserSignAgree')
				.then((res) => {
					this.$refs.paging.complete(res);
				})
				.catch((err) => {
					this.$refs.paging.complete(false);
				});
		}
	}
};
</script>
<style>
page {
	background: #ffffff;
}
</style>
<style lang="scss" scoped>
.wrap-list {
	width: 100%;
	flex-direction: column;
	&__item {
		width: 100%;
		padding: 30rpx;
		justify-content: space-between;
		&__title {
			font-size: 30rpx;
			font-weight: 500;
			color: #333333;
		}
		&__status {
			font-weight: 500;
			font-size: 28rpx;
			color: #444BF1;
		}
	}
}
</style>
