<template>
	<web-view :src="webViewUrl"></web-view>
</template>

<script>
export default {
	data() {
		return {
			webViewUrl: ''
		};
	},
	onLoad(options) {
		switch (options.type) {
			case this.cons.PROTOCOL_PRIVACY:
				this.webViewUrl = this.cons.PROTOCOL_PRIVACY_URL;
				break;
			case this.cons.PROTOCOL_ICP_URL:
				this.webViewUrl = this.cons.PROTOCOL_ICP_URL;
				break;
			default:
				this.webViewUrl = this.iProtocolUrl(options.type);
				break;
		}
	}
};
</script>
<style lang="scss" scoped>
.html {
	background-color: #fff;
	padding: 30upx;
	font-size: 30upx;
	line-height: 180%;
}
</style>
