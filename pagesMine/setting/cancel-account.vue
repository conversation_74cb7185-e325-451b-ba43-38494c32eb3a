<template>
	<view>
		<public-module></public-module>
		<z-paging ref="paging">
			<u-navbar slot="top" title="账号注销" :placeholder="true" :autoBack="true"></u-navbar>
			<view class="content">
				<u-parse :content="content || ''"></u-parse>
			</view>
			<view class="bottom" slot="bottom">
				<u-row :customStyle="{ height: '100rpx' }">
					<u-checkbox-group v-model="protocolValues" activeColor="#444BF1">
						<u-checkbox name="procotol" shape="circle" />
					</u-checkbox-group>
					<text style="font-size: 15px; color: #606266">我已阅读并同意上述</text>
					<text style="font-size: 15px; color: #606266">{{ `《${cons.PROTOCOL_MAPS[cons.PROTOCOL_CANCEL]}》` }}</text>
				</u-row>
				<view class="flex bottom__submit" @click="onPrepareCanceAccount">确定注销</view>
			</view>
		</z-paging>
		<!-- 注销登录弹窗 -->
		<u-popup :show="showCancelAccountPopup" bgColor="transparent" mode="center" @close="showCancelAccountPopup = false">
			<popup-cancel-account @cancel="showCancelAccountPopup = false" @confirm="onConfirmCancelAccount"></popup-cancel-account>
		</u-popup>
	</view>
</template>

<script>
import { mapActions } from 'vuex';
import { exitLogin } from '@/common/login.js';
import PopupCancelAccount from '@/components/popup-cancel-account/popup-cancel-account.vue';
export default {
	components: {
		PopupCancelAccount
	},
	data() {
		return {
			pageParams: {},
			content: '',
			protocolValues: [],
			// 弹窗【注销登录】
			showCancelAccountPopup: false
		};
	},
	onLoad(options) {
		this.pageParams = options;
		this.queryInfo();
	},
	methods: {
		...mapActions(['submitSignedAgreement', 'logoutUser']),
		// 查询须知说明
		queryInfo() {
			uni.$u.http
				.post('/agree/agreeJson', {
					type: this.cons.PROTOCOL_CANCEL
				})
				.then((res) => {
					this.content = res.content;
				})
				.catch((err) => {});
		},
		// 弹窗事件-> 确认注销帐号
		onConfirmCancelAccount() {
			this.showCancelAccountPopup = false;
			this.canceAccount();
		},
		// 注销
		onPrepareCanceAccount() {
			if (uni.$u.test.isEmpty(this.protocolValues)) {
				uni.$u.toast(`请阅读并同意《${this.cons.PROTOCOL_MAPS[this.cons.PROTOCOL_CANCEL]}》`);
				return;
			}
			this.showCancelAccountPopup = true;
		},
		canceAccount() {
			// 协议状态提交服务器
			uni.showLoading({ title: '注销中..', mask: true });
			this.submitSignedAgreement(encodeURIComponent(this.cons.PROTOCOL_CANCEL));
			uni.$u.http
				.post('/user/cancelUser')
				.then((res) => {
					uni.hideLoading();
					this.logoutUser();
					uni.switchTab({
						url: '/pages/home/<USER>'
					});
				})
				.catch((err) => {
					uni.hideLoading();
				});
		}
	}
};
</script>
<style>
page {
	background: #ffffff;
}
</style>
<style lang="scss" scoped>
.content {
	padding: 30rpx;
	line-height: 50rpx;
}
.bottom {
	padding: 30rpx;
	background: #ffffff;
	&__submit {
		height: 100rpx;
		margin-top: 10rpx;
		background: #444BF1;
		border-radius: 30rpx;
		font-size: 36rpx;
		font-weight: bold;
		color: #ffffff;
		justify-content: center;
	}
}
</style>
