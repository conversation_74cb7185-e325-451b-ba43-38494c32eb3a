<template>
	<view>
		<public-module></public-module>
		<u-navbar title="意见反馈" :fixed="true" :statusBar="true" :placeholder="true" :autoBack="true" />
		<view class="wrap">
			<view class="wrap__title">反馈内容</view>
			<view class="wrap__input">
				<u--textarea :customStyle="{ padding: '0rpx', backgroundColor: '#F7F7F7' }" class="note" height="300rpx" v-model="remark" placeholder="请写下您的意见，我们将认真处理 (200字以内)" border="none" maxlength="200"></u--textarea>
			</view>
			<view class="flex wrap__submit" @click="onSubmit()">提 交</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			remark: ''
		};
	},
	onLoad(options) {},
	//方法
	methods: {
		/* 提交 */
		onSubmit() {
			if (uni.$u.test.isEmpty(this.remark)) {
				uni.$u.toast('请输入反馈内容');
				return;
			}
			uni.showLoading({ title: '提交中..', mask: true });
			uni.$u.http
				.post('/feedback/feedback', { content: encodeURIComponent(this.remark) })
				.then((res) => {
					uni.hideLoading();
					uni.$u.toast('您的反馈已提交成功,我们会及时改进!');
					setTimeout(() => {
						this.onBack();
					}, 800);
				})
				.catch((err) => {
					uni.hideLoading();
				});
		}
	}
};
</script>
<style>
page {
	background: #ffffff;
}
</style>
<style lang="scss" scoped>
.wrap {
	padding: 30rpx;
	&__title {
		font-size: 34rpx;
		font-weight: 500;
		color: #333333;
	}
	&__input {
		height: 360rpx;
		margin-top: 30rpx;
		padding: 30rpx;
		background-color: #f7f7f7;
		border-radius: 10rpx;
		font-size: 30rpx;
		font-weight: 500;
		color: #333333;
	}
	&__submit {
		margin-top: 300rpx;
		height: 100rpx;
		background: #444BF1;
		border-radius: 30rpx;
		font-size: 36rpx;
		font-weight: bold;
		color: #ffffff;
		justify-content: center;
	}
}
</style>
