<template>
	<view>
		<public-module></public-module>
		<u-navbar title="设置" :fixed="true" :statusBar="true" :placeholder="true" autoBack></u-navbar>
		<view class="flex wrap-item">
			<text class="wrap-item__name">当前账号</text>
			<text class="wrap-item__value">{{ userInfo.mobile }}</text>
			<image class="wrap-item__arrow" src="/static/image/ic_small_arrow_right.png"></image>
		</view>
		<view class="flex wrap-item" @click="onItmeClick('edit-password')">
			<text class="wrap-item__name">修改密码</text>
			<image class="wrap-item__arrow" src="/static/image/ic_small_arrow_right.png"></image>
		</view>
		<view class="flex wrap-item" @click="onItmeClick('privacy')">
			<text class="wrap-item__name">隐私</text>
			<image class="wrap-item__arrow" src="/static/image/ic_small_arrow_right.png"></image>
		</view>
		<!-- #ifdef APP-PLUS -->
		<view class="flex wrap-item" @click="onItmeClick('cache')">
			<text class="wrap-item__name">清除缓存</text>
			<text class="wrap-item__value">{{ cacheSize }}</text>
		</view>
		<!-- #endif -->
		<view class="flex wrap-item" @click="onItmeClick('feedback')">
			<text class="wrap-item__name">意见反馈</text>
			<image class="wrap-item__arrow" src="/static/image/ic_small_arrow_right.png"></image>
		</view>
		<view class="flex wrap-item" @click="onItmeClick('cancel')">
			<text class="wrap-item__name">账号注销</text>
			<image class="wrap-item__arrow" src="/static/image/ic_small_arrow_right.png"></image>
		</view>
		<view class="flex wrap-item" @click="onItmeClick('technology')" v-if="sysConf.is_show_develop">
			<rich-text class="title" :nodes="`平台开发<br/><span style='color:#999999;line-height:26px;font-weight:400;font-size:12px;'>成熟稳定盈利行业App平台OEM定制</span>`"></rich-text>
			<image class="wrap-item__arrow" src="/static/image/ic_small_arrow_right.png"></image>
		</view>
		<view class="flex exit" @click="onItmeClick('exit-login')">退出登录</view>
		<!-- #ifdef APP-PLUS -->
		<view class="flex-col beian">
			<view class="flex beian__ga" v-if="!$u.test.isEmpty(sysConf.beian_text)">
				<image class="beian__ga__ic" src="/static/image/ic_beian_gonga.png" mode="aspectFit"></image>
				<text class="beian__ga__text">{{ sysConf.beian_text }}</text>
			</view>
			<view class="flex beian__icp" v-if="!$u.test.isEmpty(sysConf.icp_text)" @click="onItmeClick('icp')">
				<text class="beian__icp__text">{{ sysConf.icp_text }}</text>
				<image class="beian__icp__arrow" src="/static/image/ic_small_arrow_right.png"></image>
			</view>
		</view>
		<!-- #endif -->
		<!-- 通用操作弹窗 -->
		<u-popup :show="promptPopup.show" bgColor="transparent" mode="center" @close="promptPopup.show = false">
			<popup-prompt
				:ident="promptPopup.ident"
				:icon="promptPopup.icon"
				:title="promptPopup.title"
				:message="promptPopup.message"
				:actions="promptPopup.actions"
				:tipText="promptPopup.tipText"
				:customStyle="promptPopup.customStyle"
				:showClose="promptPopup.showClose"
				@cancel="promptPopup.show = false"
			></popup-prompt>
		</u-popup>
		<!-- 客服 -->
		<u-popup :show="kfPopup.show" bgColor="transparent" mode="center" :close-on-click-overlay="false" @close="kfPopup.show = false">
			<popup-kf :show="kfPopup.show" :kfType="kfPopup.type" @close="kfPopup.show = false"></popup-kf>
		</u-popup>
	</view>
</template>

<script>
// #ifdef APP-PLUS
import { clearCache, cacheFormatSize } from '@/common/appCache.js';
// #endif
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';
export default {
	data() {
		return {
			cacheSize: '0B',
			// 通用操作弹窗
			promptPopup: {
				show: false,
				ident: 'prompt',
				showClose: false
			},
			// 客服弹窗
			kfPopup: {
				show: false
			}
		};
	},
	computed: {
		...mapState(['sysConf', 'userInfo'])
	},
	onLoad(options) {
		// #ifdef APP-PLUS
		// 计算缓存大小
		cacheFormatSize((res) => {
			this.cacheSize = res;
		});
		// #endif
	},
	methods: {
		...mapActions(['logoutUser']),
		// 操作按钮点击
		onItmeClick(id) {
			switch (id) {
				case 'edit-password':
					// 修改密码
					this.onJump({ url: '/pages/login/edit-password' });
					break;
				case 'privacy':
					// 隐私
					this.onJump({ url: '/pagesMine/privacy/privacy' });
					break;
				case 'feedback':
					// 意见反馈
					this.onJump({ url: '/pagesMine/setting/feedback' });
					break;
				case 'cache':
					// 清除缓存
					clearCache(() => {
						this.cacheSize = '0B';
					});
					break;
				case 'cancel':
					// 帐号注销
					this.onJump({ url: '/pagesMine/setting/cancel-account' });
					break;
				case 'technology':
					// 技术客服
					this.kfPopup = { show: true, type: 'jskf' };
					break;
				case 'exit-login':
					// 退出登录
					this.promptPopup = {
						show: true,
						ident: 'prompt',
						title: '退出登录',
						message: '您确定要退出登录？',
						customStyle: {
							width: '600rpx',
							backgroundColor: '#FFFFFF'
						},
						actions: [
							{
								type: 'exit-login',
								title: '确定',
								customStyle: 'width:240rpx;background: #FFFFFF;color:#444BF1;border: 0.8px solid #444BF1;',
								action: () => {
									this.logoutUser();
									uni.switchTab({
										url: '/pages/home/<USER>'
									});
									this.promptPopup.show = false;
								}
							},
							{
								type: 'cancel',
								title: '取消',
								customStyle: 'width:240rpx;background: #444BF1;color:#FFFFFF;',
								action: () => {
									this.promptPopup.show = false;
								}
							}
						],
						showClose: false
					};
					break;
				case 'icp':
					// ICP查询
					plus.runtime.openURL(this.cons.PROTOCOL_ICP_URL, (res) => {});
					break;
			}
		}
	}
};
</script>

<style>
page {
	background: #ffffff;
}
</style>
<style lang="scss" scoped>
.wrap-item {
	width: 100%;
	padding: 35rpx 30rpx;
	justify-content: space-between;
	&__name {
		font-weight: 500;
		font-size: 30rpx;
		color: #333333;
	}
	&__value {
		margin-left: auto;
		font-weight: 500;
		font-size: 28rpx;
		color: #666666;
	}
	&__arrow {
		width: 12rpx;
		height: 20rpx;
		margin-left: 15rpx;
	}
}
.exit {
	width: 690rpx;
	height: 100rpx;
	margin: 120rpx auto 0 auto;
	background: #444BF1;
	border-radius: 30rpx;
	justify-content: center;
	font-weight: bold;
	font-size: 36rpx;
	color: #ffffff;
}

.beian {
	align-items: center;
	justify-content: center;
	position: fixed;
	left: 0;
	right: 0;
	bottom: 160rpx;

	&__ga {
		&__ic {
			width: 30rpx;
			height: 30rpx;
		}
		&__text {
			margin-left: 15rpx;
			font-weight: 500;
			font-size: 22rpx;
			color: #999999;
		}
	}

	&__icp {
		margin-top: 15rpx;
		&__text {
			font-weight: 500;
			font-size: 22rpx;
			color: #999999;
		}
		&__arrow {
			width: 10rpx;
			height: 16rpx;
			margin-left: 15rpx;
		}
	}
}
</style>
