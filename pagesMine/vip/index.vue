<template>
	<view class="wrap">
		<public-module></public-module>
		<view class="wrap-gradient"></view>
		<z-paging ref="paging">
			<template #top>
				<u-navbar bgColor="transparent" title="会员中心" :statusBar="true" :placeholder="true" :autoBack="true" />
			</template>

			<view class="flex wrap-user">
				<image class="wrap-user__avatar" :src="iImage('img_default_avatar.png')" mode="aspectFill"></image>
				<view class="flex-col wrap-user__content">
					<text class="wrap-user__content__title">{{ uidata.vipName }}</text>
					<text class="wrap-user__content__subtitle">{{ uidata.vipDesc }}</text>
				</view>
				<image class="wrap-user__decor" :src="iImage('img_vip_center_decor.png', 'pagesMine')" mode="aspectFill"></image>
			</view>
			<view class="flex wrap-package">
				<view class="flex-col wrap-package__cell" :class="{ active: selectPackageIndex == index }" v-for="(item, index) in packageList" :key="index" @click="() => (selectPackageIndex = index)">
					<text class="wrap-package__cell__name" :class="{ active: selectPackageIndex == index }">{{ item.name }}</text>
					<text class="wrap-package__cell__price" :class="{ active: selectPackageIndex == index }">{{ item.price | fen2yuanSimple }}</text>
					<text class="wrap-package__cell__original-price" :class="{ active: selectPackageIndex == index }">￥{{ item.originalPrice | fen2yuanSimple }}</text>
					<view class="flex wrap-package__cell__recommend animate__animated animate__rotateInUpLeft" :class="{ active: selectPackageIndex == index }" v-if="item.promotionLabel">{{ item.promotionLabel }}</view>
				</view>
			</view>
			<view class="flex wrap-pay">
				<x-payment-option v-model="payChannel"></x-payment-option>
			</view>
			<view class="flex-col wrap-submit">
				<view class="flex wrap-submit__btn" @click="onCreateOrder">{{ uidata.buttonLabel }}</view>
				<view class="flex">
					<u-checkbox-group v-model="agreementState" inactiveColor="#B4B4B4" activeColor="#444BF1">
						<u-checkbox name="procotol" shape="circle" label="我已阅读并同意"></u-checkbox>
					</u-checkbox-group>
					<text style="font-size: 14px; color: #444bf1" @click="onJump({ url: '/pagesMine/privacy/agreement', params: { type: cons.PROTOCOL_PAYMENT } })">{{ `《${cons.PROTOCOL_MAPS[cons.PROTOCOL_PAYMENT]}》` }}</text>
				</view>
			</view>
			<view class="line"></view>
			<view class="flex-col wrap-privilege">
				<text class="wrap-privilege__title">会员权益</text>
				<view class="flex wrap-privilege__content">
					<view class="flex wrap-privilege__content__item" v-for="(item, index) in privileges" :key="index">
						<image class="wrap-privilege__content__item__ic" :src="item.iconUrl"></image>
						<view class="flex-col">
							<text class="wrap-privilege__content__item__name line1">{{ item.title }}</text>
							<text class="wrap-privilege__content__item__descr line1">{{ item.description }}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="flex-col wrap-tooltip">
				<text class="wrap-tooltip__title">使用说明</text>
				<view class="wrap-tooltip__content">
					<rich-text :nodes="descContent"></rich-text>
				</view>
			</view>
		</z-paging>
		<!-- 通用操作弹窗 -->
		<u-popup :show="promptPopup.show" bgColor="transparent" mode="center" @close="promptPopup.show = false">
			<popup-prompt
				:ident="promptPopup.ident"
				:icon="promptPopup.icon"
				:title="promptPopup.title"
				:message="promptPopup.message"
				:actions="promptPopup.actions"
				:tipText="promptPopup.tipText"
				:customStyle="promptPopup.customStyle"
				:showClose="promptPopup.showClose"
				@cancel="promptPopup.show = false"
			></popup-prompt>
		</u-popup>
		<!-- 协议确认 -->
		<u-popup :show="showAgreementValidationPopup" :safe-area-inset-bottom="false" mode="bottom" @close="showAgreementValidationPopup = false" closeable>
			<popup-agreement-validation v-model="agreementState" :protocol-type="[cons.PROTOCOL_USER, cons.PROTOCOL_PRIVACY]" @confirm="onCreateOrder" @close="showAgreementValidationPopup = false"></popup-agreement-validation>
		</u-popup>
	</view>
</template>

<script>
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';
import { setPay } from '@/common/pay.js';
export default {
	data() {
		return {
			// VIP套餐
			packageList: [],
			selectPackageIndex: 0,
			// 支付渠道
			payChannel: 'wx_app',
			// 使用协议
			agreementState: [],

			// 协议确认弹窗
			showAgreementValidationPopup: false,
			// 通用操作弹窗
			promptPopup: {
				show: false,
				ident: 'prompt',
				showClose: false
			}
		};
	},
	computed: {
		...mapState(['userInfo', 'userVip', 'sysConf']),
		...mapGetters(['getterIsLogin', 'vipStatus']),
		// 权益列表
		privileges() {
			if (uni.$u.test.isEmpty(this.packageList)) {
				return [];
			}
			return this.packageList?.[this.selectPackageIndex]?.privileges || [];
		},
		// 会员说明
		descContent() {
			return (this.sysConf.vip_con || '').replace(/[\r\n]/g, '');
		},
		// 界面数据
		uidata() {
			const data = { vipName: '开通会员', vipDesc: '加入会员，尊享服务特权', buttonLabel: '开通会员' };
			if (!this.getterIsLogin) {
				return res;
			}
			data.vipName = this.userInfo?.mobile;

			if (this.vipStatus?.isValid) {
				const remainingDays = this.vipStatus?.remainingDays;
				if (remainingDays > 0 && remainingDays <= 5) {
					data.vipDesc = `您的会员将会在${remainingDays}天后过期`;
				} else {
					data.vipDesc = `有效期：${uni.$u.timeFormat(this.userVip?.endTime, 'yyyy-mm-dd')}`;
				}
			}

			const curPackage = this.packageList?.[this.selectPackageIndex] || {};
			if (curPackage.id === this.userVip.packageId) {
				data.buttonLabel = '续费会员';
			}

			return data;
		}
	},
	onLoad(options) {
		this.fetchPackages();
	},
	onShow() {
		// 查询用户VIP信息
		this.fetchUserVip().then((res) => {
			if (this.vipStatus?.isValid) {
				const index = this.packageList?.findIndex((e) => e.id == this.userVip?.packageId);
				if (index !== -1) {
					this.selectPackageIndex = index;
				}
			}
		});
	},
	methods: {
		...mapActions(['fetchUser', 'fetchUserVip']),
		// 创建订单
		onCreateOrder() {
			if (uni.$u.test.isEmpty(this.agreementState)) {
				this.showAgreementValidationPopup = true;
				return;
			}

			const _that = this;
			const curPackage = this.packageList[this.selectPackageIndex];
			uni.$u.http
				.post('/member/vip-order/create', {
					packageId: curPackage.id
				})
				.then((res) => {
					const payParams = { payId: res.payOrderId, channel: this.payChannel };
					setPay(payParams, (payResult) => {
						if (!uni.$u.test.isEmpty(payResult?.msg)) {
							uni.showToast({
								title: payResult.msg,
								icon: 'none'
							});
						}

						if (payResult?.success) {
							// 支付成功
							_that.fetchUserVip();
						}
					});
				})
				.catch((err) => {});
		},
		// 支付完成
		handlePayFinish() {
			const item = this.packageList?.[this.selectPackageIndex];
			const isAuthorized = String(this.userInfo?.is_auth) === this.cons.AUTH_STATUS_AUTHORIZED;

			let content = `您已成功开通${item.title}`;
			let actions = [];
			let showClose = false;

			if (!isAuthorized) {
				content = `您已开通${item.title}<br/>请完成实名认证即可抢单`;
				actions.push({
					type: 'auth',
					title: '前往认证',
					customStyle: 'width:480rpx;height:90rpx;border-radius: 20rpx;background: #444BF1;color:#FFFFFF;',
					action: () => {
						this.promptPopup.show = false;
						this.onJump({
							url: '/pagesMine/auth/real-auth'
						});
					}
				});
			} else {
				actions.push({
					type: 'ok',
					title: '知道了',
					customStyle: 'width:480rpx;height:90rpx;border-radius: 20rpx;background: #444BF1;color:#FFFFFF;',
					action: () => {
						this.promptPopup.show = false;
					}
				});
				showClose = false;
			}
			this.promptPopup = {
				show: true,
				ident: 'prompt',
				icon: '/static/image/ic_alert_success.png',
				title: '支付成功',
				message: content,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: actions,
				showClose: showClose
			};
		},
		// 查询VIP套餐
		fetchPackages() {
			uni.$u.http.get('/member/vip-package/list').then((res) => {
				this.packageList = res;
			});
		}
	}
};
</script>
<style>
page {
	background-color: #ffffff;
}
</style>

<style lang="scss" scoped>
.wrap {
	position: relative;
}

.wrap-gradient {
	width: 100%;
	height: 30vh; /* 30% 视口高度 */
	position: absolute;
	left: 0;
	top: 0;
	background: linear-gradient(to bottom, /* 垂直渐变（从上到下） */ #ff9a9e 0%, /* 顶部颜色 */ #fad0c4 50%, /* 中间颜色 */ #ffffff 100%),
		/* 底部颜色 */ linear-gradient(to right, /* 水平渐变（从左到右） */ #e3ecfe 0%, /* 左侧颜色 */ #fbc2eb 50%, /* 中间颜色 */ #eed9e1 100%); /* 右侧颜色 */
	background-blend-mode: overlay; /* 混合模式（可选：multiply/screen/overlay等） */
}

.wrap-user {
	flex: 1;
	padding: 20rpx 30rpx;
	align-items: center;
	position: relative;

	&__avatar {
		width: 100rpx;
		height: 100rpx;
		background: #f7f7f7;
		border-radius: 15rpx;
		border: 1.5px solid #ffffff;
		overflow: hidden;
		image {
			width: 100%;
			height: 100%;
		}
	}
	&__content {
		height: 100rpx;
		margin-left: 15rpx;
		justify-content: space-around;

		&__title {
			font-weight: 600;
			font-size: 36rpx;
			color: #1a1a2a;
		}
		&__subtitle {
			font-size: 28rpx;
			color: #5f6069;
		}
	}
	&__decor {
		width: 167rpx;
		height: 111rpx;
		margin-left: auto;
	}
}

.wrap-package {
	flex: 1;
	padding: 30rpx 30rpx 0 30rpx;
	justify-content: space-between;
	position: relative;

	&__cell {
		width: 220rpx;
		height: 240rpx;
		border: 1px solid transparent;
		border-radius: 20rpx;
		background: linear-gradient(25deg, #f5f5ff 0%, #fff2ff 100%) padding-box, linear-gradient(28deg, #e5dcfc, #ecc6f3) border-box;
		align-items: center;
		justify-content: center;
		position: relative;

		&.active {
			border-width: 2px;
			background: linear-gradient(226deg, #fecbe3 0%, #d8c8ff 100%) padding-box, linear-gradient(35deg, #fa68ad, #936df4) border-box;
		}

		&__name {
			font-weight: bold;
			font-size: 36rpx;
			color: #676878;
			&.active {
				color: #090b34;
			}
		}
		&__price {
			margin-top: 15rpx;
			font-weight: 600;
			font-size: 60rpx;
			color: #676878;
			&:before {
				content: '¥';
				font-size: 24rpx;
			}
			&.active {
				color: #090b34;
			}
		}
		&__original-price {
			margin-top: 15rpx;
			font-size: 24rpx;
			color: #b4b6e6;
			text-decoration-line: line-through;
		}
		&__recommend {
			padding: 6rpx 15rpx;
			background: linear-gradient(-54deg, #e4d0ff 0%, #f3cee3 100%);
			border-radius: 20rpx 0rpx 20rpx 0rpx;
			font-weight: 400;
			font-size: 22rpx;
			color: #6c2490;
			position: absolute;
			left: -1rpx;
			top: -18rpx;

			&.active {
				left: -4rpx;
				color: #ffffff;
				background: linear-gradient(-54deg, #862ffe 0%, #fa1b99 100%);
			}
		}
	}
}

.wrap-pay {
	padding: 20rpx 30rpx;
}

.wrap-submit {
	padding: 0 30rpx;
	align-items: center;
	justify-content: center;

	&__btn {
		width: 100%;
		height: 100rpx;
		margin-bottom: 25rpx;
		background: linear-gradient(226deg, #f24593 0%, #5551f6 100%);
		border-radius: 10rpx;
		font-weight: bold;
		font-size: 32rpx;
		color: #ffffff;
		justify-content: center;
		position: relative;

		text {
			position: absolute;
			left: 30rpx;
			font-size: 30rpx;
			font-weight: 400;
			line-height: 100rpx;
		}
	}
}

.line {
	height: 12rpx;
	margin: 30rpx 0;
	background-color: #f3f4f6;
}

.wrap-privilege {
	flex: 1;
	padding: 0 30rpx;
	&__title {
		font-weight: 600;
		font-size: 34rpx;
		color: #2b2b42;
	}
	&__content {
		width: 100%;
		margin-top: 15rpx;
		align-items: center;
		justify-content: space-between;
		flex-wrap: wrap;
		&__item {
			width: 336rpx;
			height: 140rpx;
			margin: 10rpx 0;
			padding: 0 20rpx;
			background: linear-gradient(25deg, #f5f5ff 0%, #fff2ff 100%);
			border-radius: 10rpx;
			justify-content: flex-start;
			&__ic {
				width: 67rpx;
				height: 67rpx;
				margin-right: 10rpx;
				flex-shrink: 0;
			}
			&__name {
				font-weight: 600;
				font-size: 30rpx;
				color: #090b35;
			}
			&__descr {
				margin-top: 15rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: #909099;
			}
		}
	}
}

.wrap-tooltip {
	margin: 30rpx 30rpx 30rpx 30rpx;
	&__title {
		font-size: 30rpx;
		color: #1a1a2a;
	}
	&__content {
		margin-top: 10rpx;
		line-height: 40rpx;
		font-size: 26rpx;
		color: #333333;
	}
}
</style>
