<template>
	<view>
		<public-module></public-module>
		<z-paging ref="paging" v-model="pinList" :auto-clean-list-when-reload="false" hide-no-more-inside @query="queryList">
			<template #top>
				<u-navbar title="置顶" :statusBar="true" :placeholder="true" :autoBack="true" />
			</template>

			<view class="flex-row wrap-package">
				<view class="flex-col wrap-package__cell" :class="{ active: selectPackageIndex == index }" v-for="(item, index) in packageList" :key="index" @click="() => (selectPackageIndex = index)">
					<text class="wrap-package__cell__price" :class="{ active: selectPackageIndex == index }">{{ item.price | fen2yuanSimple }}</text>
					<text class="wrap-package__cell__name" :class="{ active: selectPackageIndex == index }">{{ item.name }}</text>
				</view>
			</view>
			<view class="flex-col wrap-city" @click.stop="showCityPopup">
				<text class="wrap-city__title">置顶城市</text>
				<view class="flex">
					<view class="wrap-city__select">
						<text class="wrap-city__select__placeholder" v-if="$u.test.isEmpty(cityValues)">{{ `请选择置顶(最多${maxCityCount}个)` }}</text>
						<view class="wrap-city__select__content" v-else>
							<view class="flex tag" v-for="(ct, idx) in cityValues" :key="idx">
								<view class="tag__name">{{ ct.name }}</view>
								<image class="tag__delete" :src="iImage('ic_tag_delete.png', 'pagesMine')" @click.stop="deleteCity(ct.id)"></image>
							</view>
						</view>
					</view>
					<image class="wrap-city__arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
				</view>
			</view>

			<view class="flex wrap-pay">
				<x-payment-option v-model="payChannel"></x-payment-option>
			</view>

			<view class="flex-col wrap-submit">
				<view class="flex">
					<u-checkbox-group v-model="agreementState" inactiveColor="#333333" activeColor="#444BF1">
						<u-checkbox name="procotol" shape="circle" label="我已阅读并同意"></u-checkbox>
					</u-checkbox-group>
					<text style="font-size: 14px; color: #444bf1" @click="onJump({ url: '/pagesMine/privacy/agreement', params: { type: cons.PROTOCOL_PAYMENT } })">{{ `《${cons.PROTOCOL_MAPS[cons.PROTOCOL_PAYMENT]}》` }}</text>
				</view>
				<view class="flex wrap-submit__btn" @click="onCreateOrder">
					<text>合计：{{ totalPrice }}元</text>
					确认置顶
				</view>
			</view>

			<view class="line"></view>

			<view class="flex wrap-list-header">
				<text class="wrap-list-header__title">置顶明细</text>
				<view class="flex">
					<image class="wrap-list-header__ic" :src="iImage('ic_shuom.png', 'pagesMine')"></image>
					<text class="wrap-list-header__t">置顶说明</text>
				</view>
			</view>
			<list-pin-item :item="item" v-for="(item, index) in pinList" :key="index"></list-pin-item>
		</z-paging>

		<!-- 城市选择器 -->
		<u-popup :show="multipleCityPopup.show" bgColor="transparent" mode="bottom" :safe-area-inset-bottom="false" closeable @close="multipleCityPopup.show = false">
			<popup-multiple-city :max-num="maxCityCount" :show="multipleCityPopup.show" :value="multipleCityPopup.value" :complete="multipleCityPopup.complete" @close="multipleCityPopup.show = false"></popup-multiple-city>
		</u-popup>
		<!-- 通用操作弹窗 -->
		<u-popup :show="promptPopup.show" bgColor="transparent" mode="center" @close="promptPopup.show = false">
			<popup-prompt
				:ident="promptPopup.ident"
				:icon="promptPopup.icon"
				:title="promptPopup.title"
				:message="promptPopup.message"
				:actions="promptPopup.actions"
				:tipText="promptPopup.tipText"
				:customStyle="promptPopup.customStyle"
				:showClose="promptPopup.showClose"
				@cancel="promptPopup.show = false"
			></popup-prompt>
		</u-popup>
		<!-- 协议确认 -->
		<u-popup :show="showAgreementValidationPopup" :safe-area-inset-bottom="false" mode="bottom" @close="showAgreementValidationPopup = false" closeable>
			<popup-agreement-validation v-model="agreementState" :protocol-type="[cons.PROTOCOL_PAYMENT]" @confirm="onCreateOrder" @close="showAgreementValidationPopup = false"></popup-agreement-validation>
		</u-popup>
	</view>
</template>

<script>
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';
import { setPay } from '@/common/pay.js';
import ListPinItem from './component/list-pin-item/list-pin-item.vue';
export default {
	components: {
		ListPinItem
	},
	data() {
		return {
			pageParams: {},
			// 最大选择城市数
			maxCityCount: 5,

			// 置顶套餐
			packageList: [],
			selectPackageIndex: 0,
			// 城市数组
			citys: [],
			// 支付渠道
			payChannel: 'wx_app',

			// 置顶记录列表
			pinList: [],

			// 协议
			agreementState: [],

			// 协议确认弹窗
			showAgreementValidationPopup: false,
			// 城市选择器
			multipleCityPopup: {
				show: false
			},
			// 通用操作弹窗
			promptPopup: {
				show: false,
				ident: 'prompt',
				showClose: false
			}
		};
	},
	computed: {
		...mapState(['userInfo', 'sysConf']),
		...mapGetters(['getterIsLogin']),

		// 选择的城市
		cityValues() {
			if (!uni.$u.test.isEmpty(this.citys)) {
				return this.citys.flatMap((e) => e.child);
			}
			return [];
		},

		// 总价格
		totalPrice() {
			const unitPrice = this.packageList?.[this.selectPackageIndex]?.price || 0;
			const yuan = this.$options.filters.fen2yuanSimple(unitPrice);
			return yuan * this.cityValues.length;
		}
	},
	onLoad(options) {
		this.pageParams = options;

		this.getPackages();
	},
	methods: {
		...mapActions(['fetchUser']),
		// 打开城市选择器
		showCityPopup() {
			this.multipleCityPopup = {
				show: true,
				value: this.citys,
				complete: (res) => {
					this.citys = res.detail;
				}
			};
		},
		// 删除城市
		deleteCity(targetId) {
			this.citys = this.citys.map((parent) => {
				return {
					...parent,
					child: parent.child?.filter((child) => child.id !== targetId) || []
				};
			});
		},
		//  下单
		onCreateOrder() {
			if (uni.$u.test.isEmpty(this.cityValues)) {
				uni.showToast({
					title:'请选择置顶城市',
					icon:'none'
				})
				return;
			}
			if (uni.$u.test.isEmpty(this.agreementState)) {
				this.showAgreementValidationPopup = true;
				return;
			}

			const that = this;
			uni.$u.http
				.post(
					'/pin/order/create',
					{
						bizType: this.pageParams.pinBizType,
						bizId: this.pageParams.id,
						packageId: this.packageList?.[this.selectPackageIndex]?.id,
						cityCodes: this.cityValues.map((e) => e.id)
					},
					{ custom: { showLoading: true } }
				)
				.then((res) => {
					const payParams = { payId: res.payOrderId, channel: this.payChannel };
					setPay(payParams, (payResult) => {
						if (!uni.$u.test.isEmpty(payResult?.msg)) {
							uni.showToast({
								title: payResult.msg,
								icon: 'none'
							});
						}
						
						if (payResult?.success){
							that.getOpenerEventChannel().emit('reloadProductList', {});
							that.onBack();
						}
					});
				})
				.catch((err) => {});
		},
		// 查询VIP套餐
		getPackages(pageNo, pageSize) {
			uni.$u.http
				.get('/pin/package/list')
				.then((res) => {
					this.packageList = res;
				})
				.catch((err) => {});
		},
		// 获取置顶列表
		queryList(pageNo, pageSize) {
			uni.$u.http
				.get('/pin/item/page', {
					params: {
						page: pageNo,
						limit: pageSize
					},
					custom: {
						showLoading: false
					}
				})
				.then((res) => {
					this.$refs.paging.complete(res.list);
				})
				.catch((err) => {
					this.$refs.paging.complete(false);
				});
		}
	}
};
</script>

<style>
page {
	background-color: #ffffff;
}
</style>

<style lang="scss" scoped>
.tooltip {
	margin: 30rpx;
	font-size: 24rpx;
	color: #ff0000;
}

.wrap-package {
	flex: 1;
	margin: 30rpx 30rpx 0 30rpx;
	justify-content: space-between;
	position: relative;

	&__cell {
		width: 220rpx;
		height: 140rpx;
		border-radius: 10rpx;
		border: 1px solid #d8d8db;
		align-items: center;
		justify-content: center;
		position: relative;

		&.active {
			background-color: #444bf1;
			border: 1.5px solid #444bf1;
		}

		&__price {
			font-weight: 600;
			font-size: 46rpx;
			color: #f43a55;

			&::after {
				content: '元';
				font-size: 24rpx;
			}

			&.active {
				color: #ffffff;
			}
		}

		&__name {
			margin-top: 10rpx;
			font-size: 28rpx;
			color: #5f6069;

			&.active {
				color: #ffffff;
			}
		}
	}
}

.wrap-city {
	flex: 1;
	margin: 50rpx 30rpx 0 30rpx;
	background: #ffffff;
	border-bottom: 0.5px solid #d8d8db;

	&__title {
		font-weight: bold;
		font-size: 34rpx;
		color: #1e2134;
	}

	&__select {
		flex: 1;
		padding: 15rpx 0;
		font-size: 30rpx;

		&__placeholder {
			line-height: 60rpx;
			font-size: 30rpx;
			color: #909099;
		}

		&__content {
			display: flex;
			flex-wrap: wrap;
			margin: 0 -8rpx 0 -8rpx;

			.tag {
				height: 44rpx;
				margin: 8rpx;
				padding: 0 10rpx;
				background: #f3f3f5;
				border-radius: 4rpx;
				border: 1px solid #d8d8db;

				&__name {
					font-weight: 400;
					font-size: 26rpx;
					color: #1e2134;
				}

				&__delete {
					width: 20rpx;
					height: 20rpx;
					margin-left: 5rpx;
				}
			}
		}
	}

	&__arrow {
		width: 10rpx;
		height: 60rpx;
	}
}

.wrap-pay {
	padding: 20rpx 30rpx;
}

.wrap-submit {
	padding: 0 30rpx;

	&__btn {
		width: 100%;
		height: 100rpx;
		margin-top: 25rpx;
		background: #444bf1;
		border-radius: 10rpx;
		font-weight: bold;
		font-size: 32rpx;
		color: #ffffff;
		justify-content: center;
		position: relative;

		text {
			position: absolute;
			left: 30rpx;
			font-size: 30rpx;
			font-weight: 400;
			line-height: 100rpx;
		}
	}
}

.line {
	flex: 1;
	height: 10rpx;
	margin: 30rpx 0;
	background: #f3f4f6;
}

.wrap-list-header {
	padding: 0 30rpx 15rpx 30rpx;
	justify-content: space-between;

	&__title {
		font-weight: bold;
		font-size: 36rpx;
		color: #1e2134;
	}

	&__ic {
		width: 26rpx;
		height: 26rpx;
	}

	&__t {
		margin-left: 5rpx;
		font-weight: 400;
		font-size: 28rpx;
		color: #909099;
	}
}
</style>
