<template>
	<view class="flex-col wraper">
		<view class="flex wraper__header">
			<view class="flex-col">
				<text class="wraper__header__title">{{ bizTypeName }}</text>
				<text class="wraper__header__date">{{ item.createTime | date('yyyy-mm-dd hh:MM') }}</text>
			</view>
			<text class="wraper__header__price">{{ item.payPrice | fen2yuanSimple }}</text>
		</view>
		<view class="flex-col wraper__content">	
			<view class="flex wraper__content__city" v-for="(str, idx) in item.pinCityNames" :key="idx">
				<text>{{str}}</text>
				<text>{{ item.endTime | date('yyyy-mm-dd') }}到期</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'list-pin-item',
	props: {
		item: {
			type: Object,
			default: () => {}
		}
	},
	computed: {
		bizTypeName() {
			const typeMap = {
				[this.cons.PIN_BIZ_TYPE_PRODUCT]: '服务置顶',
				[this.cons.PIN_BIZ_TYPE_PROVIDER]: '名片置顶'
			};
			return typeMap[this.item.bizType] || '';
		},
		// 格式化价格
		formatPrice() {
			const yuan = this.$options.filters.fen2yuanSimple(this.item.price);
			return this.$options.filters.formattedPrice({
				price: yuan,
				unit: '',
				highlightSize: '18px'
			});
		}
	},
	data() {
		return {};
	},
	methods: {}
};
</script>

<style lang="scss" scoped>
.wraper {
	padding: 20rpx 30rpx;
	position: relative;

	&__header {
		justify-content: space-between;
		&__title {
			font-weight: bold;
			font-size: 30rpx;
			color: #1e2134;
			line-height: 40rpx;
		}
		&__date {
			font-weight: 400;
			font-size: 24rpx;
			color: #909099;
			line-height: 40rpx;
		}
		&__price {
			font-weight: bold;
			font-size: 36rpx;
			color: #1e2134;
			&:before {
				content: '¥';
				font-size: 24rpx;
			}
		}
	}

	&__content {
		margin-top: 10rpx;
		padding: 15rpx 20rpx;
		background: #f3f4f6;
		border-radius: 10rpx;

		&__city {
			justify-content: space-between;
			font-weight: 400;
			font-size: 28rpx;
			color: #1e2134;
			line-height: 50rpx;
		}
	}
}
</style>
