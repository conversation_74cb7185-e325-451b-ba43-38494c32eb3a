# 会计代账宝 【6月19号开始布局】

## 包名
- iOS: `vip.acc.easy`
- Android: `vip.acc.easy`

### Android
- 别名: `regagency`
- 密码: `vip2024finc`

## 修改的框架
1. UView框架
   - 修改u-navbar的leftClick方法， 修改之前【返回上一页】->修改之后【如果当前页面是第一页，则返回首页】
   - 修改u-swipe-action-item中的【marginRight: item.text ? '2px' : 0】】->【marginBottom: item.text ? '10px' : 0】
   - 修改transition.js 【```javascript// #ifdef H5 await uni.$u.sleep(20)// #endif```】 -> 【```javascript await uni.$u.sleep(20)```】
   - 修改u-index-anchor.vue 【 class="u-index-anchor u-border-bottom"】 -> 【 class="u-index-anchor"】
   - 修改u-tabs 【```vue <text:class="[item.disabled && 'u-tabs__wrapper__nav__item__text--disabled']" class="u-tabs__wrapper__nav__item__text" :style="[textStyle(index)]">{{ item[keyName] }}</text>```】-> 
               【```vue <rich-text :class="[item.disabled && 'u-tabs__wrapper__nav__item__text--disabled']" class="u-tabs__wrapper__nav__item__text" :style="[textStyle(index)]" :nodes=" item[keyName]"></rich-text>```】
   - 修改u-collapse-item 【u-cell添加```:customStyle="customStyle"```】			   
2. ZPaging框架
   - 修改【`class="zp-page-bottom-container"`的style中添加`'zIndex':topZIndex`】



## 项目需要优化点 【后期维护时候优化】



## 需要优化的问题



## 打包APP注意


## 打包小程序注意


## 会计代账宝马甲小程序

### 会计代账宝
- AppID: ``
- 原始ID: ``



#### 画页面问题总结
- 优惠专区导致页面的列表无法展示在一屏幕之内
- Tab分类页面是否需要城市选择，城市选择做什么用
- Tab分类页面的分类有几级？如果是三级 建议再这里做成两级  方便用户直接选择


#### 存在问题
- 订单详情的师傅名称


讨论问题：
服务中是否可以评价