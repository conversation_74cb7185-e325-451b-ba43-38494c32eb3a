<template>
	<view>
		<public-module></public-module>
		<z-paging ref="paging">
			<template #top>
				<u-navbar title="需求详情" :statusBar="true" :placeholder="true" autoBack />
			</template>
			<view class="flex-col wrap-base">
				<view class="flex">
					<view class="flex wrap-base__short-title" v-if="!$u.test.isEmpty(demandTitleObj.shortTitle)">
						{{demandTitleObj.shortTitle}}
					</view>
					<text class="wrap-base__title line1">{{demandTitleObj.title}}</text>
				</view>
				<view class="flex" style="margin-top: 30rpx">
					<image class="wrap-base__avatar" :src="iImage('img_demand_user_avatar.png')" mode="aspectFit">
					</image>
					<view class="flex-col" style="margin-left: 20rpx">
						<text class="wrap-base__contact-title">{{contactTitle}}</text>
						<text class="wrap-base__mobile">{{detail.contactMobile}}</text>
					</view>
					<view class="flex wrap-base__button">
						<image :src="iImage('ic_demand_detail_tel.png', 'pagesCustomer')"></image>
						<text>立即联系</text>
					</view>
				</view>
			</view>

			<view class="flex-col wrap-demand">
				<text class="wrap-demand__title">服务内容</text>

				<view class="flex wrap-demand__item" v-for="(item, index) in contentTags" :key="index">
					<text class="wrap-demand__item__name">{{item.name}}：</text>
					<text class="wrap-demand__item__value">{{item.value}}</text>
				</view>
			</view>

			<view class="flex-col wrap-tooltip">
				<text class="wrap-tooltip__title">温馨提示</text>

				<view class="flex wrap-tooltip__content">1.电话联系发布需求人时请告知是在【财会接单宝App】看到的。 2.办理前请签订合约，以保证双方权益，平台仅提供线索，不参与具
				</view>
			</view>

			<!-- 底部操作条 -->
			<template #bottom>
				<view class="flex wrap-bottom">
					<x-share class="wrap-bottom__share">
						<view class="flex" @click="onClickBottomItem({ type: 'feedback' })">
							<image :src="iImage('ic_demand_detail_share.png', 'pagesCustomer')"></image>
							<text>分享</text>
						</view>
					</x-share>
					<view class="flex wrap-bottom__contact" @click="onClickBottomItem({ type: 'feedback' })">
						<image :src="iImage('ic_demand_detail_tel.png', 'pagesCustomer')"></image>
						<text>立即联系</text>
					</view>
				</view>
			</template>
		</z-paging>
		<!-- 通用操作弹窗 -->
		<u-popup :show="promptPopup.show" bgColor="transparent" mode="center" :close-on-click-overlay="false"
			@close="promptPopup.show = false">
			<popup-prompt :ident="promptPopup.ident" :icon="promptPopup.icon" :iconStyle="promptPopup.iconStyle"
				:title="promptPopup.title" :message="promptPopup.message" :actions="promptPopup.actions"
				:tipText="promptPopup.tipText" :buttonBoxJustifyContent="promptPopup.buttonBoxJustifyContent"
				:customStyle="promptPopup.customStyle" :showClose="promptPopup.showClose"
				@cancel="promptPopup.show = false"></popup-prompt>
		</u-popup>
		<!-- 客服 -->
		<u-popup :show="kfPopup.show" bgColor="transparent" mode="center" :close-on-click-overlay="false"
			:overlay-opacity="0.8" @close="kfPopup.show = false">
			<popup-kf @close="kfPopup.show = false"></popup-kf>
		</u-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions,
		mapMutations
	} from 'vuex';
	import {
		callTel
	} from '@/common/utils.js';
	import tag from '../../uni_modules/uview-ui/libs/config/props/tag';
	export default {
		data() {
			return {
				pageParams: {},
				// 明细
				detail: {},
				// 通用操作弹窗
				promptPopup: {
					show: false
				},
				// 客服弹窗
				kfPopup: {
					show: false
				}
			};
		},
		computed: {
			...mapState(['currentAddress', 'selectedAddress', 'userInfo', 'sysConf']),
			/**
			 * 需求标题信息（包含主标题 title 和简称 shortTitle）
			 * - 发布来源（originType=3）根据子类型判断展示哪个分类名称
			 * - 预约产品 / 商家，统一展示服务分类名称
			 */
			demandTitleObj() {
				const subtypeMap = {
					[this.cons.DEMAND_ORIGIN_SUBTYPE_NORMAL]: {
						title: this.detail?.serviceCategoryName,
						shortTitle: this.detail?.serviceCategoryShortName
					},
					[this.cons.DEMAND_ORIGIN_SUBTYPE_QUALIFICATION]: {
						title: this.detail?.qualificationCategoryName,
						shortTitle: this.detail?.qualificationCategoryShortName
					},
					[this.cons.DEMAND_ORIGIN_SUBTYPE_DECLARATION]: {
						title: this.detail?.declarationCategoryName,
						shortTitle: this.detail?.declarationCategoryShortName
					}
				};

				// 主动发布的需求（根据子类型选择标题）
				if (this.detail?.originType === this.cons.DEMAND_ORIGIN_TYPE_PUBLISH) {
					return subtypeMap[this.detail?.originSubtype] || {
						title: '',
						shortTitle: ''
					};
				}

				// 预约产品/服务商的需求（统一用服务分类）
				if (
					this.detail?.originType === this.cons.DEMAND_ORIGIN_TYPE_APPOINT_PRODUCT ||
					this.detail?.originType === this.cons.DEMAND_ORIGIN_TYPE_APPOINT_MERCHANT
				) {
					const title = this.detail?.serviceCategoryName || '';
					const shortTitle = this.detail?.serviceCategoryShortName || '';
					return {
						title,
						shortTitle
					};
				}
				// 默认空值
				return {
					title: '',
					shortTitle: ''
				};
			},
			// 称呼
			contactTitle() {
				const title = this.detail?.contactTitle || ''
				const suffixMap = {
					[this.cons.SEX_MALE]: '先生',
					[this.cons.SEX_FEMALE]: '女士'
				}
				const suffix = suffixMap[this.detail?.sex] || ''
				return title + suffix
			},
			// 服务内容
			contentTags() {
				const tags = [];
				const item = this.detail || {};

				// 发布时间
				if (!uni.$u.test.isEmpty(item.createTime)) {
					tags.push({
						name: '发布时间',
						value: uni.$u.timeFormat(item.createTime, 'yyyy-mm-dd')
					});
				}

				// 线索类型
				const demandTypeMap = {
					[this.cons.DEMAND_TYPE_CLUE]: '线索单',
					[this.cons.DEMAND_TYPE_APPOINT]: '预约单',
				};
				tags.push({
					name: '线索类型',
					value: demandTypeMap[item.demandType] || ''
				});

				// 分类信息（根据来源和子类型判断）
				if (item.originType === this.cons.DEMAND_ORIGIN_TYPE_PUBLISH) {
					switch (item.originSubtype) {
						case this.cons.DEMAND_ORIGIN_SUBTYPE_NORMAL:
							tags.push({
								name: '办理业务',
								value: item.serviceCategoryName
							});
							break;
						case this.cons.DEMAND_ORIGIN_SUBTYPE_QUALIFICATION:
							tags.push({
								name: '资质类型',
								value: item.qualificationCategoryName
							});
							tags.push({
								name: '所属行业',
								value: item.industryCategoryName
							});
							break;
						case this.cons.DEMAND_ORIGIN_SUBTYPE_DECLARATION:
							tags.push({
								name: '申报类型',
								value: item.declarationCategoryName
							});
							tags.push({
								name: '主体类型',
								value: item.subjectCategoryName
							});
							break;
					}
				} else {
					tags.push({
						name: '办理业务',
						value: item.serviceCategoryName
					});
				}

				// 办理地区
				tags.push({
					name: '办理地区',
					value: item.areaName
				});

				// 办理预算（兼容单一预算或区间）
				const min = item.budgetMin || 0;
				const max = item.budgetMax || 0;
				const budgetText = min === max ? `${min}` : `${min}-${max}`;
				tags.push({
					name: '办理预算',
					value: budgetText
				});

				// 说明
				tags.push({
					name: '要求说明',
					value: item.remark
				});

				// 过滤空值
				return tags.filter(tag => !uni.$u.test.isEmpty(tag.value));
			},

		},
		onLoad(options) {
			this.pageParams = options;
		},
		onShow() {
			this.getDetail();
		},
		methods: {
			...mapActions(['fetchSysConf']),
			// 联系师傅
			contactMaster() {
				callTel({
					tel: this.detail?.detail?.user?.mobile
				});
			},
			// 查询详情
			getDetail(callback) {
				uni.$u.http
					.get('/demand/hall/get-detail', {
						params: {
							id: this.pageParams.id
						}
					})
					.then((res) => {
						this.detail = res;
						callback && callback(true);
					})
					.catch((err) => {
						callback && callback(false);
					});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.wrap-base {
		padding: 30rpx;
		background-color: #ffffff;

		&__short-title {
			width: 68rpx;
			height: 36rpx;
			margin-right: 15rpx;
			background-color: #444bf1;
			justify-content: center;
			border-radius: 0rpx 18rpx 18rpx 0rpx;
			font-size: 24rpx;
			color: #ffffff;
		}

		&__title {
			font-weight: bold;
			font-size: 36rpx;
			color: #1e2134;
		}

		&__avatar {
			width: 66rpx;
			height: 66rpx;
		}

		&__contact-title {
			font-size: 30rpx;
			color: #909099;
			line-height: 36rpx;
		}

		&__mobile {
			font-size: 30rpx;
			font-weight: bold;
			color: #1e2134;
			line-height: 36rpx;
		}

		&__button {
			width: 200rpx;
			height: 80rpx;
			margin-left: auto;
			background: #444bf1;
			border-radius: 40rpx;
			justify-content: center;

			image {
				width: 28rpx;
				height: 28rpx;
			}

			text {
				margin-left: 10rpx;
				font-weight: 600;
				font-size: 28rpx;
				color: #ffffff;
			}
		}
	}

	.wrap-demand {
		padding: 30rpx;
		margin-top: 10rpx;
		background-color: #ffffff;

		&__title {
			margin-bottom: 20rpx;
			font-weight: 600;
			font-size: 36rpx;
			color: #1e2134;
		}

		&__item {
			padding: 20rpx 0;
			align-items: flex-start;

			&__name {
				color: #909099;
				font-weight: 500;
				font-size: 30rpx;
				white-space: nowrap;
			}

			&__value {
				font-weight: 600;
				font-size: 30rpx;
				color: #333333;
			}
		}
	}

	.wrap-tooltip {
		padding: 30rpx;
		margin-top: 10rpx;
		background-color: #ffffff;

		&__title {
			margin-bottom: 20rpx;
			font-weight: 600;
			font-size: 36rpx;
			color: #1e2134;
		}

		&__content {
			padding: 20rpx;
			background: #f5f5f7;
			border-radius: 10rpx;
			font-size: 28rpx;
			color: #626373;
			line-height: 42rpx;
		}
	}

	/* 底部操作条 */
	.wrap-bottom {
		width: 100%;
		height: 120rpx;
		padding: 0 30rpx;
		background: #ffffff;
		box-shadow: 0rpx 2rpx 10rpx 0rpx rgba(174, 174, 174, 0.53);
		justify-content: space-between;

		&__share,
		&__contact {
			height: 90rpx;
			border-radius: 15rpx;

			image {
				width: 34rpx;
				height: 34rpx;
			}

			text {
				margin-left: 15rpx;
				font-weight: 600;
				font-size: 34rpx;
				color: #ffffff;
			}
		}

		&__share {
			width: 280rpx;
			background: #2cbdb9;
		}

		&__contact {
			width: 400rpx;
			background: #444bf1;
			justify-content: center;
		}
	}
</style>