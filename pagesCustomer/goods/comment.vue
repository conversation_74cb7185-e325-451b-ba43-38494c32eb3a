<template>
	<view>
		<public-module></public-module>
		<z-paging ref="paging">
			<view slot="top">
				<u-navbar bgColor="#FFFFFF" title="评价" :statusBar="true" :placeholder="true" autoBack></u-navbar>
			</view>
			<view class="flex wrap">
				<text class="wrap__title">本次服务怎么样？</text>
				<view class="flex wrap__emoji">
					<view
						class="flex wrap__emoji__item"
						v-for="(item, index) in emojiList"
						:key="index"
						@animationend="
							() => {
								emojiList.forEach((e) => (e.animation = false));
							}
						"
						@click="onEmojiClick(index)"
					>
						<!-- animate__jello animate__rubberBand -->
						<image class="animate__animated wrap__emoji__item__ic" :class="[item.animation ? 'animate__jello' : '']" :src="`/pagesOrder/static/${index == emojiIndex ? item.iconHigh : item.icon}`"></image>
						{{ item.name }}
					</view>
				</view>
				<!-- <view class="flex wrap__tags">
					<view class="flex wrap__tags__item" :class="[tagIndexs.includes(index) ? 'wrap__tags__item--active' : '']" v-for="(item, index) in tagList" :key="index" @click="onTagClick(index)">
						{{ item }}
					</view>
				</view> -->
				<!-- <view class="flex wrap__rate">
					<view class="flex wrap__rate__item" v-for="(item, index) in rateList" :key="index">
						<text class="wrap__rate__item__name">{{ item.name }}</text>
						<view class="flex">
							<u-rate v-model="item.value" activeColor="#FF932C" size="50rpx" gutter="1" allowHalf></u-rate>
							<text class="wrap__rate__item__value">{{ item.value }}分</text>
						</view>
					</view>
				</view> -->
				<view class="flex wrap__input">
					<u-textarea customStyle="background: transparent;" v-model="explain" border="none" placeholder="请写下您想对师傅说的话~~（50字以内）" :maxlength="50"></u-textarea>
				</view>
				<view class="flex-col wrap__files">
					<x-upload :fileList="files" accept="image" imageMode="aspectFill" :sizeType="['original']" multiple :maxCount="8" width="166rpx" height="166rpx" @beforeChoose="beforeChoose" @afterRead="afterRead" @delete="deletePic">
						<view class="flex-col wrap__files__add" :style="{ width: '166rpx', height: '166rpx' }">
							<image src="/static/image/ic_camera.png"></image>
							<text>点击上传</text>
						</view>
					</x-upload>
					<text style="margin-top: 30rpx; font-size: 24rpx; color: #fe3734">*图片最多不超过8张</text>
				</view>
			</view>
			<view slot="bottom">
				<view class="flex wrap-bottom">
					<view class="flex wrap-bottom__confirm" @click="onPrepareSubmit">提交</view>
				</view>
				<u-safe-bottom :customStyle="{ background: '#FFFFFF' }"></u-safe-bottom>
			</view>
		</z-paging>
	</view>
</template>

<script>
import SysPermission from '@/common/sysPermission.js';
import xUpload from '@/components/x-upload/x-upload.vue';
export default {
	components: {
		xUpload
	},
	data() {
		return {
			pageParams: {},
			// 表情
			emojiList: [
				{ type: 'SS', name: '非常满意', icon: 'ic_comment_emoji_1.png', iconHigh: 'ic_comment_emoji_1s.png', animation: false },
				{ type: 'S', name: '满意', icon: 'ic_comment_emoji_2.png', iconHigh: 'ic_comment_emoji_2s.png', animation: false },
				{ type: 'A', name: '一般', icon: 'ic_comment_emoji_3.png', iconHigh: 'ic_comment_emoji_3s.png', animation: false },
				{ type: 'B', name: '不满意', icon: 'ic_comment_emoji_4.png', iconHigh: 'ic_comment_emoji_4s.png', animation: false }
			],
			emojiIndex: 0,
			// 标签
			tagList: [],
			tagIndexs: [],
			// 评分
			rateList: [
				{ name: '服务态度', value: 5 },
				{ name: '时间观念', value: 5 },
				{ name: '技能水平', value: 5 }
			],
			explain: '',
			files: [],
			// 媒体资源是否上传中
			mediaUploading: false
		};
	},
	onLoad(options) {
		this.pageParams = options;

		this.getPageData();
	},
	methods: {
		// 点击表情
		onEmojiClick(index) {
			this.emojiIndex = index;
			this.emojiList.forEach((e) => (e.animation = false));
			this.emojiList[index].animation = true;

			// let item = this.emojiList[index];
			// switch (item.type) {
			// 	case 'SS':
			// 		this.rateList.forEach((e) => (e.value = 5));
			// 		break;
			// 	case 'S':
			// 		this.rateList.forEach((e) => (e.value = 3));
			// 		break;
			// 	case 'A':
			// 		this.rateList.forEach((e) => (e.value = 2));
			// 		break;
			// 	case 'D':
			// 		this.rateList.forEach((e) => (e.value = 1));
			// 		break;
			// }
		},
		// 选择标题
		onTagClick(index) {
			if (this.tagIndexs.includes(index)) {
				this.tagIndexs = this.tagIndexs.filter((item) => item !== index);
			} else {
				this.tagIndexs.push(index);
			}
		},
		// 【媒体资源选择器】->删除媒体资源
		deletePic(event) {
			this.files.splice(event.index, 1);
		},
		// 【媒体资源选择器】-> 选择之前
		async beforeChoose(e) {
			const _that = this;
			var options =
				e.type === 'album'
					? {
							permission: 'album',
							permissionTitle: this.cons.kPermission_Tip_Camera_Title,
							permissionSubtitle: this.cons.kPermission_Tip_Camera_Text
					  }
					: {
							permission: 'camera',
							permissionTitle: this.cons.kPermission_Tip_Camera_Title,
							permissionSubtitle: this.cons.kPermission_Tip_Camera_Text
					  };
			SysPermission.judgePermission(options.permission, _that.cons.kPermission_Tip_Camera_Title, _that.cons.kPermission_Tip_Camera_Text, (result) => {
				if (result == 1) {
					// 已授权
					e.resolve({
						next: true
					});
				} else {
					e.resolve({
						next: false
					});
				}
			});
		},
		// 【媒体资源选择器】-> 新增file
		async afterRead(event) {
			this.mediaUploading = true;
			// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
			let lists = [].concat(event.file);
			let fileListLen = this.files.length;
			lists.map((item) => {
				this.files.push({
					...item,
					status: 'uploading',
					message: '上传中'
				});
			});
			for (let i = 0; i < lists.length; i++) {
				let item = this.files[fileListLen];
				const result = await this.uplodaImage(lists[i].url, (progress) => {
					// 展示上传进度
					// this.files[fileListLen].progress !== 100 (此判断ios中progress为100的时候会回调两次导致进入为100的时候会再多出一条数据)
					// if (this.files[fileListLen].progress !== 100) {
					// 	this.files.splice(fileListLen, 1, Object.assign(item, {
					// 		...item,
					// 		status: 'uploading',
					// 		message: '上传中',
					// 		progress: progress
					// 	}))
					// }
				});
				console.log('上传结果', result);
				this.files.splice(
					fileListLen,
					1,
					Object.assign(item, {
						status: !uni.$u.test.isEmpty(result) ? 'success' : 'failed',
						message: !uni.$u.test.isEmpty(result) ? '' : '上传失败',
						url: result
					})
				);
				fileListLen++;
			}
			this.mediaUploading = false;
			console.log('等待上传结果------', this.files);
		},
		// 上传媒体资源
		uplodaImage(path, callback) {
			return new Promise((resolve, reject) => {
				uni.$u.http
					.upload('/common/upload', {
						filePath: path,
						name: 'file',
						params: { model: 'order' },
						custom: {
							load: false
						},
						getTask: (task, options) => {
							task.onProgressUpdate((res) => {
								console.log('上传进度' + res.progress);
								// console.log('已经上传的数据长度' + res.totalBytesSent);
								// console.log('预期需要上传的数据总长度' + res.totalBytesExpectedToSend);
								callback && callback(res.progress);
							});
						}
					})
					.then((res) => {
						resolve(res.file_path);
					})
					.catch((err) => {
						uni.$u.toast('文件上传失败,请重新上传');
						resolve(null);
					});
			});
		},
		// 获取页面数据
		getPageData() {
			uni.$u.http
				.get('/common/getCommonWordList')
				.then((res) => {
					this.tagList = res.comment_entry || [];
				})
				.catch((err) => {});
		},
		onPrepareSubmit() {
			// 检测媒体资源上传状态的递归函数
			const checkMediaUploadStatus = () => {
				if (this.mediaUploading) {
					setTimeout(checkMediaUploadStatus, 100); // 避免不必要的多次操作
					return;
				}
				this.onSubmit(); // 媒体上传完成后，直接调用提交申请函数
			};

			// 初始调用检测函数
			uni.showLoading({ title: '提交中' });
			checkMediaUploadStatus();
		},
		// 提交
		onSubmit() {
			let fileUrls = this.files.map((item, index) => item.url);
			const tags = this.tagIndexs.map((index) => this.tagList[index]);
			uni.$u.http
				.post('/comment/comment', {
					product_detail_id: this.pageParams.id || '',
					level: this.emojiList[this.emojiIndex].type,
					service_attitude: this.rateList[0].value,
					time_concept: this.rateList[1].value,
					skill_level: this.rateList[2].value,
					tags: encodeURIComponent(tags.join(',')),
					content: encodeURIComponent(this.explain),
					images: encodeURIComponent(fileUrls)
				})
				.then((res) => {
					uni.hideLoading();
					uni.showToast({
						title: '评价成功',
						icon: 'none'
					});
					this.onBack();
				})
				.catch((err) => {
					uni.hideLoading();
				});
		}
	}
};
</script>
<style>
page {
	background: #ffffff;
}
</style>
<style lang="scss" scoped>
.wrap {
	width: 100%;
	padding: 30rpx;
	flex-direction: column;
	align-items: flex-start;
	&__title {
		font-weight: 800;
		font-size: 34rpx;
		color: #333333;
	}
	&__emoji {
		width: 100%;
		margin-top: 30rpx;
		justify-content: space-around;
		&__item {
			flex-direction: column;
			font-weight: 500;
			font-size: 30rpx;
			color: #999999;
			&__ic {
				width: 85rpx;
				height: 75rpx;
				margin-bottom: 15rpx;
			}
		}
	}
	&__tags {
		width: 100%;
		margin-top: 50rpx;
		flex-wrap: wrap;
		justify-content: space-between;

		&__item {
			width: 200rpx;
			height: 60rpx;
			margin: 10rpx 0;
			background: #f7f7f7;
			border-radius: 8rpx;
			border: 0.8px solid #f7f7f7;
			font-size: 30rpx;
			color: #333333;
			justify-content: center;
			&--active {
				background: #f6f5ff;
				border: 0.8px solid #444BF1;
				color: #444BF1;
			}
		}
	}

	&__rate {
		width: 100%;
		margin-top: 30rpx;
		flex-direction: column;
		align-items: flex-start;
		&__item {
			width: 100%;
			padding: 20rpx;
			justify-content: space-between;
			&__name {
				font-weight: 500;
				font-size: 30rpx;
				color: #333333;
			}
			&__value {
				width: 60rpx;
				margin-left: 20rpx;
				font-weight: 500;
				font-size: 24rpx;
				color: #999999;
			}
		}
	}

	&__input {
		width: 100%;
		margin-top: 60rpx;
		background: #f7f7f7;
		border-radius: 15rpx;
	}

	&__files {
		margin-top: 50rpx;
		&__add {
			margin-right: 8rpx;
			align-items: center;
			justify-content: center;
			background: #f3f3f3;
			border-radius: 15rpx;

			image {
				width: 36rpx;
				height: 33rpx;
			}

			text {
				margin-top: 15rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
				text-align: center;
			}
		}
	}
}
.wrap-bottom {
	width: 100%;
	padding: 30rpx;

	&__confirm {
		width: 100%;
		height: 100rpx;
		background: #444BF1;
		border-radius: 30rpx;
		justify-content: center;
		font-weight: 500;
		font-size: 34rpx;
		color: #ffffff;
	}
}
</style>
