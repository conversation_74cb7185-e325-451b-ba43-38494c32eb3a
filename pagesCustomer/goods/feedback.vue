<template>
	<view>
		<public-module></public-module>
		<z-paging ref="paging">
			<view slot="top">
				<u-navbar bgColor="#444BF1" leftIconColor="#FFFFFF" title="建议反馈" titleStyle="color:#FFFFFF;fontSize:33rpx;fontWeight:700;" :statusBar="true" :placeholder="true" autoBack></u-navbar>
			</view>
			<view class="flex wrap-goods-container">
				<view class="flex wrap-goods">
					<view class="flex wrap-goods__base">
						<image class="wrap-goods__base__cover" :src="detail.category && detail.category.cover_image_text" mode="aspectFill"></image>
						<view class="flex wrap-goods__base__right">
							<text class="wrap-goods__base__right__name line1">{{ detail.category && detail.category.nickname }}</text>
							<text class="wrap-goods__base__right__desc line1">{{ detail.category && detail.category.intro }}</text>
							<view class="flex wrap-goods__base__right__fee">
								<rich-text
									class="wrap-goods__base__right__fee__price"
									:nodes="formattedPrice({ price: detail.category && detail.category.price, unit: detail.category && detail.category.unit, highlightSize: '16px' })"
								></rich-text>
								<text class="wrap-goods__base__right__fee__count">x{{ detail.number }}</text>
							</view>
						</view>
					</view>
					<u-line color="#F0F0F0"></u-line>
					<view class="flex wrap-goods__worker" v-if="!$u.test.isEmpty(detail.detail && detail.detail.user)">
						<image class="wrap-goods__worker__avatar" :src="detail.detail.user.avatar" mode="aspectFill"></image>
						<view class="flex wrap-goods__worker__right">
							<text class="wrap-goods__worker__right__name">{{ detail.detail.user.nickname }}</text>
							<text class="wrap-goods__worker__right__mobile">{{ detail.detail.user.mobile }}</text>
						</view>
						<view class="flex wrap-goods__worker__contact" @click="onCall">联系师傅</view>
					</view>
				</view>
			</view>
			<view class="flex other">
				<view class="flex wrap-reason">
					<text class="wrap-reason__title">反馈原因</text>
					<view class="flex wrap-reason__tags">
						<view class="flex wrap-reason__tags__item" :class="[tagIndexs.includes(index) ? 'wrap-reason__tags__item--active' : '']" v-for="(item, index) in tagList" :key="index" @click="onTagClick(index)">
							{{ item }}
						</view>
					</view>
				</view>
				<view class="flex wrap-explain">
					<text class="wrap-explain__title">反馈说明</text>
					<view class="flex wrap-explain__input">
						<u--textarea customStyle="background: transparent;" v-model="explain" border="none" placeholder="描述具体情况，有助于更快处理"></u--textarea>
					</view>
				</view>
				<view class="flex wrap-media">
					<view class="wrap-media__title">
						<rich-text :nodes="`上传凭证<span style='font-weight:400;font-size:12px;'>（图片）</span>`"></rich-text>
					</view>
					<view class="flex wrap-media__files">
						<x-upload
							:fileList="files"
							accept="image"
							imageMode="aspectFill"
							:sizeType="['original']"
							multiple
							:maxCount="4"
							width="152rpx"
							height="152rpx"
							@beforeChoose="beforeChoose"
							@afterRead="afterRead"
							@delete="deletePic"
						>
							<view class="flex-col wrap-media__files__add" :style="{ width: '152rpx', height: '152rpx' }">
								<image src="/static/image/ic_camera.png"></image>
								<text>点击上传</text>
							</view>
						</x-upload>
					</view>
				</view>
			</view>
			<view slot="bottom">
				<view class="flex wrap-bottom">
					<view class="flex wrap-bottom__confirm" @click="onPrepareSubmit">提交</view>
				</view>
				<u-safe-bottom :customStyle="{ background: '#FFFFFF' }"></u-safe-bottom>
			</view>
		</z-paging>
		<!-- 通用操作弹窗 -->
		<u-popup :show="promptPopup.show" bgColor="transparent" mode="center" :close-on-click-overlay="false" @close="promptPopup.show = false">
			<popup-prompt
				:ident="promptPopup.ident"
				:icon="promptPopup.icon"
				:iconStyle="promptPopup.iconStyle"
				:title="promptPopup.title"
				:message="promptPopup.message"
				:actions="promptPopup.actions"
				:tipText="promptPopup.tipText"
				:buttonBoxJustifyContent="promptPopup.buttonBoxJustifyContent"
				:customStyle="promptPopup.customStyle"
				:showClose="promptPopup.showClose"
				@cancel="promptPopup.show = false"
			></popup-prompt>
		</u-popup>
	</view>
</template>

<script>
import SysPermission from '@/common/sysPermission.js';
import xUpload from '@/components/x-upload/x-upload.vue';
import { callTel } from '@/common/utils.js';
export default {
	components: {
		xUpload
	},
	data() {
		return {
			pageParams: {},
			// 订单明细
			detail: {},
			// 反馈标签
			tagList: [],
			tagIndexs: [],
			explain: '',
			// 资源文件
			files: [],
			// 媒体资源是否上传中
			mediaUploading: false,
			// 通用操作弹窗
			promptPopup: {
				show: false,
				ident: 'prompt',
				showClose: false
			}
		};
	},
	onLoad(options) {
		this.pageParams = options;
		this.getPageData();
	},
	methods: {
		onTagClick(index) {
			if (this.tagIndexs.includes(index)) {
				this.tagIndexs = this.tagIndexs.filter((value) => value !== index);
			} else {
				this.tagIndexs.push(index);
			}
		},
		// 联系师傅
		onCall() {
			let mobile = uni.$u.getProperty(this.detail, 'detail.user.mobile');
			callTel({ tel: mobile });
		},
		// 【媒体资源选择器】->删除媒体资源
		deletePic(event) {
			this.files.splice(event.index, 1);
		},
		// 【媒体资源选择器】-> 选择之前
		async beforeChoose(e) {
			const _that = this;
			var options =
				e.type === 'album'
					? {
							permission: 'album',
							permissionTitle: this.cons.kPermission_Tip_Camera_Title,
							permissionSubtitle: this.cons.kPermission_Tip_Camera_Text
					  }
					: {
							permission: 'camera',
							permissionTitle: this.cons.kPermission_Tip_Camera_Title,
							permissionSubtitle: this.cons.kPermission_Tip_Camera_Text
					  };
			SysPermission.judgePermission(options.permission, _that.cons.kPermission_Tip_Camera_Title, _that.cons.kPermission_Tip_Camera_Text, (result) => {
				if (result == 1) {
					// 已授权
					e.resolve({
						next: true
					});
				} else {
					e.resolve({
						next: false
					});
				}
			});
		},
		// 【媒体资源选择器】-> 新增file
		async afterRead(event) {
			this.mediaUploading = true;
			// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
			let lists = [].concat(event.file);
			let fileListLen = this.files.length;
			lists.map((item) => {
				this.files.push({
					...item,
					status: 'uploading',
					message: '上传中'
				});
			});
			for (let i = 0; i < lists.length; i++) {
				let item = this.files[fileListLen];
				const result = await this.uplodaImage(lists[i].url, (progress) => {
					// 展示上传进度
					// this.files[fileListLen].progress !== 100 (此判断ios中progress为100的时候会回调两次导致进入为100的时候会再多出一条数据)
					// if (this.files[fileListLen].progress !== 100) {
					// 	this.files.splice(fileListLen, 1, Object.assign(item, {
					// 		...item,
					// 		status: 'uploading',
					// 		message: '上传中',
					// 		progress: progress
					// 	}))
					// }
				});
				console.log('上传结果', result);
				this.files.splice(
					fileListLen,
					1,
					Object.assign(item, {
						status: !uni.$u.test.isEmpty(result) ? 'success' : 'failed',
						message: !uni.$u.test.isEmpty(result) ? '' : '上传失败',
						url: result
					})
				);
				fileListLen++;
			}
			this.mediaUploading = false;
			console.log('等待上传结果------', this.files);
		},
		// 上传媒体资源
		uplodaImage(path, callback) {
			return new Promise((resolve, reject) => {
				uni.$u.http
					.upload('/common/upload', {
						filePath: path,
						name: 'file',
						params: { model: 'order' },
						custom: {
							load: false
						},
						getTask: (task, options) => {
							task.onProgressUpdate((res) => {
								console.log('上传进度' + res.progress);
								// console.log('已经上传的数据长度' + res.totalBytesSent);
								// console.log('预期需要上传的数据总长度' + res.totalBytesExpectedToSend);
								callback && callback(res.progress);
							});
						}
					})
					.then((res) => {
						resolve(res.file_path);
					})
					.catch((err) => {
						uni.$u.toast('文件上传失败,请重新上传');
						resolve(null);
					});
			});
		},
		onPrepareSubmit() {
			if (uni.$u.test.isEmpty(this.tagIndexs)) {
				uni.$u.toast('请选择反馈原因');
				return;
			}
			if (uni.$u.test.isEmpty(this.explain)) {
				uni.$u.toast('请填写反馈说明');
				return;
			}
			// 检测媒体资源上传状态的递归函数
			const checkMediaUploadStatus = () => {
				if (this.mediaUploading) {
					setTimeout(checkMediaUploadStatus, 100); // 避免不必要的多次操作
					return;
				}
				this.onSubmit(); // 媒体上传完成后，直接调用提交申请函数
			};

			// 初始调用检测函数
			uni.showLoading({ title: '提交中', mask: true });
			checkMediaUploadStatus();
		},
		// 提交
		onSubmit() {
			let fileUrls = this.files.map((item, index) => item.url);
			const tags = this.tagIndexs.map((index) => this.tagList[index]);

			uni.$u.http
				.post('/feedback/order', {
					product_detail_id: this.detail?.detail?.id || '',
					reason: encodeURIComponent(tags.join(',')),
					content: encodeURIComponent(this.explain),
					images: encodeURIComponent(fileUrls)
				})
				.then((res) => {
					uni.hideLoading();
					this.promptPopup = {
						show: true,
						ident: 'prompt',
						icon: '/static/image/ic_alert_success.png',
						title: '提交成功',
						message: '您的反馈已提交 ',
						customStyle: {
							backgroundColor: '#FFFFFF'
						},
						actions: [
							{
								type: 'ok',
								title: '知道了',
								customStyle: 'width:240rpx;height:90rpx;border-radius: 45rpx;font-size:30rpx;background: #444BF1;color:#FFFFFF;',
								action: () => {
									this.onBack();
								}
							}
						],
						showClose: false
					};
				})
				.catch((err) => {
					uni.hideLoading();
				});
		},
		// 查询页面数据
		getPageData() {
			// 订单明细
			uni.$u.http
				.get('/product/getInfo', { params: { product_id: this.pageParams.id } })
				.then((res) => {
					this.detail = res;
				})
				.catch((err) => {});
			// 反馈标签
			uni.$u.http
				.get('/common/getCommonWordList')
				.then((res) => {
					this.tagList = res.feedback_entry || [];
				})
				.catch((err) => {});
		}
	}
};
</script>

<style lang="scss" scoped>
.wrap-goods-container {
	width: 100%;
	background: linear-gradient(to bottom, #444BF1 0%, #444BF1 50%, #f7f7f7 50%, #f7f7f7 100%);
}
.wrap-goods {
	width: 100%;
	margin: 20rpx 30rpx 0 30rpx;
	background: #ffffff;
	border-radius: 20rpx;
	flex-direction: column;
	align-items: flex-start;

	&__base {
		width: 100%;
		padding: 30rpx;
		&__cover {
			width: 160rpx;
			min-width: 160rpx;
			height: 160rpx;
			border-radius: 15rpx;
			background-color: #f7f7f7;
		}

		&__right {
			flex: 1;
			height: 160rpx;
			margin-left: 15rpx;
			flex-direction: column;
			align-items: flex-start;

			&__name {
				font-weight: bold;
				font-size: 30rpx;
				color: #333333;
			}

			&__desc {
				margin-top: 10rpx;
				font-size: 24rpx;
				color: #999999;
			}

			&__fee {
				width: 100%;
				margin-top: auto;
				justify-content: space-between;
				&__price {
					font-size: 28rpx;
					color: #f95986;
				}
				&__count {
					font-weight: 500;
					font-size: 24rpx;
					color: #4d4d4d;
				}
			}
		}
	}

	&__worker {
		width: 100%;
		padding: 30rpx;
		&__avatar {
			width: 80rpx;
			height: 80rpx;
			background: #f7f7f7;
			border-radius: 40rpx;
		}
		&__right {
			margin-left: 15rpx;
			flex-direction: column;
			align-items: flex-start;
			&__name {
				font-weight: 600;
				font-size: 28rpx;
				color: #333333;
			}
			&__mobile {
				margin-top: 5rpx;
				font-weight: 500;
				font-size: 28rpx;
				color: #333333;
			}
		}
		&__contact {
			width: 160rpx;
			height: 60rpx;
			margin-left: auto;
			background: #444BF1;
			border-radius: 30rpx;
			justify-content: center;
			font-weight: bold;
			font-size: 24rpx;
			color: #ffffff;
		}
	}
}

.other {
	flex: 1;
	margin: 20rpx 30rpx 0 30rpx;
	padding: 30rpx;
	border-radius: 20rpx;
	background: #ffffff;
	flex-direction: column;
	align-items: flex-start;
}

.wrap-reason {
	width: 100%;
	flex-direction: column;
	align-items: flex-start;
	&__title {
		font-weight: 600;
		font-size: 34rpx;
		color: #333333;
		&:before {
			content: '*';
			color: #ff0000;
			font-weight: normal;
		}
	}
	&__tags {
		width: 100%;
		margin-top: 20rpx;
		flex-wrap: wrap;
		justify-content: space-between;

		&__item {
			width: 200rpx;
			height: 60rpx;
			margin: 10rpx 0;
			background: #f7f7f7;
			border-radius: 8rpx;
			border: 1px solid #f7f7f7;
			font-size: 30rpx;
			color: #333333;
			justify-content: center;
			&--active {
				background: #f6f5ff;
				border: 0.8px solid #444BF1;
				color: #444BF1;
			}
		}
	}
}

.wrap-explain {
	width: 100%;
	margin-top: 50rpx;
	flex-direction: column;
	align-items: flex-start;
	&__title {
		font-weight: 600;
		font-size: 34rpx;
		color: #333333;
		&:before {
			content: '*';
			color: #ff0000;
			font-weight: normal;
		}
	}
	&__input {
		width: 100%;
		margin-top: 30rpx;
		background: #f7f7f7;
		border-radius: 15rpx;
	}
}

.wrap-media {
	width: 100%;
	margin-top: 50rpx;
	flex-direction: column;
	align-items: flex-start;
	&__title {
		font-weight: 600;
		font-size: 34rpx;
		color: #333333;
	}
	&__files {
		margin-top: 30rpx;
		&__add {
			margin-right: 8rpx;
			align-items: center;
			justify-content: center;
			background: #f3f3f3;
			border-radius: 15rpx;

			image {
				width: 36rpx;
				height: 33rpx;
			}

			text {
				margin-top: 15rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
				text-align: center;
			}
		}
	}
}

.wrap-bottom {
	width: 100%;
	padding: 30rpx;

	&__confirm {
		width: 100%;
		height: 100rpx;
		background: #444BF1;
		border-radius: 30rpx;
		justify-content: center;
		font-weight: 500;
		font-size: 34rpx;
		color: #ffffff;
	}
}
</style>
