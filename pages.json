{
	"pages": [{
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTextStyle": "white",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/category/index",
			"style": {
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/customer/index",
			"style": {
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/mine/index",
			"style": {
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/login/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/login/edit-password",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/home/<USER>",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		},
		 {
			"path": "pages/search/search",
			"style": {
				"navigationBarTitleText": ""
			}
		}, {
			"path": "components/dialog/dialog",
			"style": {
				"navigationStyle": "custom",
				// #ifdef APP-PLUS
				"backgroundColor": "transparent",
				"backgroundColorTop": "transparent",
				"backgroundColorBottom": "transparent",
				// #endif
				"app-plus": {
					"animationType": "fade-in",
					"background": "transparent",
					"popGesture": "none"
				}
			}

		}, {
			"path": "pages/welcome/ios-privacy",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/welcome/guide-page",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/other/web-view",
			"style": {
				"navigationStyle": "default",
				"navigationBarTitleText": ""
			}
		}
	],
	//进入主包pages下的mine页面时，加载分包important
	"preloadRule": {
		"pages/mine/mine": {
			"network": "all",
			"packages": ["pagesMine"]
		}
	},
	"subPackages": [{
			"root": "pagesHome",
			"pages": [{
					"path": "product/detail",
					"style": {
						"navigationBarTitleText": "详情"
					}
				}, {
					"path": "provider/detail",
					"style": {
						"navigationBarTitleText": "名片"
					}
				},
				{
					"path": "list/index",
					"style": {
						"navigationBarTitleText": "",
						"navigationBarTextStyle": "white"
					}
				}
			]
		},
		{
			"root": "pagesCategory",
			"pages": []
		},
		{
			"root": "pagesChat",
			"pages": [{
					"path": "chat/index",
					"style": {
						"navigationBarTextStyle": "black",
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "conversation/index",
					"style": {
						"navigationBarTextStyle": "black",
						"navigationBarTitleText": "会话列表"
					}
				}
			]
		},
		{
			"root": "pagesCustomer",
			"pages": [{
					"path": "customer/detail",
					"style": {
						"navigationBarTextStyle": "black",
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "goods/feedback",
					"style": {
						"navigationBarTextStyle": "white",
						"navigationBarTitleText": ""
					}
				}
			]
		}, {
			"root": "pagesMine",
			"pages": [{
					"path": "setting/setting",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "cert/index",
					"style": {
						"navigationBarTitleText": "商家入驻"
					}
				},
				{
					"path": "customer/index",
					"style": {
						"navigationBarTitleText": "我的客源"
					}
				},
				{
					"path": "provider/profile",
					"style": {
						"navigationBarTitleText": "名片信息"
					}
				},
				{
					"path": "publish/product/list",
					"style": {
						"navigationBarTitleText": "我的发布"
					}
				},
				{
					"path": "publish/product/publish",
					"style": {
						"navigationBarTitleText": "发布产品"
					}
				},
				{
					"path": "pin/index",
					"style": {
						"navigationBarTitleText": "置顶"
					}
				},
				{
					"path": "helper/index",
					"style": {
						"navigationBarTitleText": "使用教程"
					}
				},
				{
					"path": "helper/detail",
					"style": {
						"navigationBarTitleText": "教程详情"
					}
				},
				{
					"path": "vip/index",
					"style": {
						"navigationBarTitleText": "会员中心"
					}
				},
				{
					"path": "setting/feedback",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "setting/cancel-account",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "privacy/privacy",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "privacy/agreement",
					"style": {
						"navigationStyle": "default",
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "privacy/signed-agreements",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "message/message",
					"style": {
						"navigationBarTitleText": ""
					}
				}
			]
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#FFFFFF",
		"navigationStyle": "custom",
		"app-plus": {
			"bounce": "none"
		},
		"pageOrientation": "auto"
	},
	"tabBar": {
		"list": [{
				"pagePath": "pages/home/<USER>"
			},
			{
				"pagePath": "pages/category/index"
			},
			{
				"pagePath": "pages/customer/index"
			},
			{
				"pagePath": "pages/mine/index"
			}
		]
	},
	"condition": { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [{
			"name": "", //模式名称
			"path": "", //启动页面，必选
			"query": "" //启动参数，在页面的onLoad函数里面得到
		}]
	}
}