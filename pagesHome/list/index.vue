<template>
	<view>
		<public-module></public-module>
		<z-paging ref="paging" bgColor="#F7F7F7" v-model="dataList" @query="queryList">
			<template #top>
				<view class="flex nav-wrap">
					<view class="flex nav-wrap__content"
						:style="{ height: `${navBarHeight}px`, marginTop: `${statusBarHeight}px` }">
						<view class="flex nav-wrap__content__left" @click.stop="onBack">
							<u-icon name="arrow-left" color="#FFFFFF" bold></u-icon>
						</view>
						<view class="flex nav-wrap__content__center">
							<u-tabs :current="tabIndex" :list="tabs" lineWidth="50rpx" lineHeight="10rpx"
								lineColor="url(/static/image/img_home_tab_indicator.png) 100% 100%"
								:activeStyle="{ color: '#FFFFFF', fontSize: '36rpx', fontWeight: '800', transform: 'scale(1.05)' }"
								:inactiveStyle="{ color: '#BEC0F7', fontSize: '36rpx', ransform: 'scale(1)' }"
								itemStyle="padding:0 30rpx;height:80rpx;text-align:center;"
								@click="onTabClick"></u-tabs>
						</view>
						<view class="flex nav-wrap__content__right"></view>
					</view>
				</view>
				<!-- 横向滚动容器 -->
				<view class="flex category-wrap">
					<scroll-view class="category-wrap__scroll" scroll-x>
						<view v-for="(item, index) in categoryTabs" :key="index" class="category-wrap__scroll__item"
							:class="{ active: categoryTabIndex === index }" @click="onCategoryTabClick(index)">
							<text>{{ item.name }}</text>
						</view>
					</scroll-view>
				</view>
			</template>

			<custom-empty slot="empty" message="暂无评价"></custom-empty>
			<!-- 产品列表 -->
			<view class="flex wrap-list" v-if="tabs[tabIndex].id == 1">
				<list-product-item class="wrap-list__item" :item="item" v-for="(item, index) in dataList" :key="index"
					@click="onProductListItemClick"></list-product-item>
			</view>
			<!-- 服务商列表 -->
			<view class="flex wrap-list" v-if="tabs[tabIndex].id == 2">
				<list-provider-item class="wrap-list__item" :item="item" v-for="(item, index) in dataList" :key="index"
					@click="onProviderListItemClick"></list-provider-item>
			</view>
		</z-paging>
	</view>
</template>

<script>
	export default {
		components: {},
		data() {
			return {
				pageParams: {},
				// 小程序胶囊
				mpMenuRect: {},
				// 状态栏高度
				statusBarHeight: 0,
				// 导航栏高度
				navBarHeight: 44,
				// Tabs分类
				tabs: [{
						id: 1,
						name: '本地业务',
						url: '/product/spu/page'
					},
					{
						id: 2,
						name: '本地商家',
						url: '/product/spu/page'
					}
				],
				tabIndex: 0,
				//  分类tabs
				categoryTabs: [{
					name: '注册公司'
				}, {
					name: '注册公司'
				}, {
					name: '注册公司'
				}, {
					name: '注册公司'
				}, {
					name: '注册公司'
				}, {
					name: '注册公司'
				}, {
					name: '注册公司'
				}, {
					name: '注册公司'
				}],
				categoryTabIndex: 0,
				// 数据列表
				dataList: []
			};
		},
		onLoad(options) {
			this.pageParams = options;
			uni.hideTabBar();
			const sys = uni.getSystemInfoSync();
			this.statusBarHeight = sys.statusBarHeight;
			// #ifdef MP
			this.mpMenuRect = uni.getMenuButtonBoundingClientRect();
			// #endif

			this.queryCategory();
		},
		methods: {
			// 切换分类
			onTabClick(tab) {
				this.tabIndex = tab.index;
				this.$refs.paging.reload(false);
			},
			// 切换分类
			onCategoryTabClick(index) {
				this.categoryTabIndex = index;
				this.$refs.paging.reload(false);
			},
			// 产品列表条目点击
			onProductListItemClick(item) {
				this.onJump({
					url: '/pagesHome/product/detail',
					params: {
						id: item.detail.id
					}
				});
			},
			// 服务商列表条目点击
			onProviderListItemClick(item) {
				this.onJump({
					url: '/pagesHome/product/detail',
					params: {
						id: item.detail.id
					}
				});
			},
			// 查询分类
			queryCategory() {
				uni.$u.http
					.get('/category/common/list-by-type-and-parent', {
						params: {
							type: this.pageParams.ownerType,
							parentId: this.pageParams.parentOwnerId
						}
					})
					.then((res) => {
						this.categoryTabs = res;
						// 默认选中到对应的位置
						const index = this.categoryTabs.findIndex(e => e.id == this.pageParams?.ownerId)
						if (index != -1) {
							this.categoryTabIndex = index;
						}
					})
			},
			queryList(pageNo, pageSize) {
				const reqUrl = this.tabs[this.tabIndex || 0].url;
				const serviceCategoryId = uni.$u.test.isEmpty(this.categoryTabs) ? this.pageParams.ownerId : this
					.categoryTabs[this.categoryTabIndex].id;
				const reqParams = {
					pageNo: pageNo,
					pageSize: pageSize,
					serviceCategoryId: serviceCategoryId,
					cityCode: this.selectedAddress?.cityId || ''
				};
				uni.$u.http
					.get(reqUrl, {
						params: reqParams
					})
					.then((res) => {
						this.loadingStatus = 'SUCCESS';
						this.$refs.paging.complete(res.list);
					})
					.catch((err) => {
						this.loadingStatus = 'FAIL';
						this.$refs.paging.complete(false);
					});
			}
		}
	};
</script>

<style lang="scss" scoped>
	/* 导航栏 */
	.nav-wrap {
		z-index: 999;
		width: 750rpx;
		background-color: #444bf1;

		&__content {
			flex: 1;
			justify-content: space-between;

			&__left,
			&__right {
				width: 100rpx;
				height: 80rpx;
				padding: 0 26rpx;
			}

			&__center {
				flex: 1;
				height: 80rpx;
				justify-content: center;
			}
		}
	}

	.category-wrap {
		padding: 10px 0;
		background-color: #444bf1;

		&__scroll {
			display: flex;
			white-space: nowrap;

			&__item {
				display: inline-block;
				padding: 0 15px;
				height: 60rpx;
				line-height: 60rpx;
				text-align: center;
				color: #bec0f7;
				font-size: 28rpx;
				white-space: nowrap;

				&.active {
					background-color: #ffffff;
					color: #111111;
					font-weight: 600;
					border-radius: 15rpx 15rpx 0 0;
					z-index: 2;
				}
			}
		}
	}

	/* 列表 */
	.wrap-list {
		padding: 0 30rpx;
		background-color: #f7f7f7;
		flex-direction: column;

		&__item {
			width: 100%;
			margin-top: 20rpx;
		}
	}
</style>