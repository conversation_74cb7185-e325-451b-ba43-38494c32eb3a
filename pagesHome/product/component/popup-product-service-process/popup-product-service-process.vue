<template>
	<view class="popup-product-service-process">
		<view class="flex-col wrap">
			<text class="wrap__title">服务流程</text>
			<view class="wrap__content">
				<rich-text :nodes="richTextContent"></rich-text>
			</view>
		</view>
	</view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import { openMiniWx, iWxMiniProgram } from '@/common/utils.js';
export default {
	name: 'popup-product-service-process',
	props: {
		processNames: {
			type: Array,
			default: () => []
		}
	},
	computed: {
		...mapState(['sysConf']),
		// 展示内容
		richTextContent() {
			const template = this.sysConf['content.product.serivice_process'] || '';
			// 将数组转换为带序号的字符串（如："1、步骤1；2、步骤2"）
			const processList = this.processNames.map((item, index) => `${index + 1}.${item}`).join('<br>'); // 使用分号和换行符分隔

			return template.replace(/\{\{\s*BIZ_TEXT\s*\}\}/g, processList);
		}
	},
	data() {
		return {};
	},
	methods: {}
};
</script>

<style lang="scss" scoped>
.row {
	display: flex;
	flex-direction: row;
}

.col {
	display: flex;
	flex-direction: column;
}

.popup-product-service-process {
	width: 750rpx !important;
	justify-content: center;
	align-items: center;

	.wrap {
		width: 100%;
		padding: 30rpx;
		background: #ffffff;
		border-radius: 30rpx 30rpx 0 0;
		align-items: center;

		&__title {
			font-weight: bold;
			font-size: 42rpx;
			color: #1e2134;
			line-height: 70rpx;
		}

		&__content {
			width: 100%;
			margin-top: 20rpx;
			margin-bottom: 30rpx;
		}
	}
}
</style>
