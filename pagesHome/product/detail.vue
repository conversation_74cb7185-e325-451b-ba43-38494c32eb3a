<template>
	<view>
		<public-module></public-module>
		<view class="flex nav-wraper">
			<view class="flex nav-wraper__content" :style="{ height: `${navBarHeight}px`, marginTop: `${statusBarHeight}px` }">
				<view class="flex nav-wraper__content__left" @click.stop="onBack">
					<image :src="iImage('ic_nav_circle_back.png')" mode="aspectFit"></image>
				</view>
				<view class="flex nav-wraper__content__right">
					<x-share :class="[isDisabled ? 'disable' : '']" @share="onShare">
						<image :src="iImage('ic_nav_circle_share.png')" mode="aspectFill"></image>
					</x-share>
				</view>
			</view>
		</view>
		<z-paging ref="paging">
			<!-- Banner -->
			<view id="banner-wraper" class="banner-wraper">
				<swiper class="banner-wraper__swiper" :current="currentBannerIndex" :autoplay="false" @change="onBannerChange">
					<swiper-item v-for="(url, idx) in detail.sliderPicUrls" :key="idx">
						<image class="banner-wraper__swiper__image" :src="url" mode="aspectFill"></image>
					</swiper-item>
				</swiper>
				<view class="flex banner-wraper__number" v-if="detail.sliderPicUrls">{{ `${currentBannerIndex + 1}/${detail.sliderPicUrls.length}` }}</view>
			</view>
			<!-- 产品信息 -->
			<view class="flex-col product-wraper">
				<view class="flex product-wraper__cost">
					<image class="product-wraper__cost__bg" :src="iImage('img_product_detail_cost_bg.png', 'pagesHome')" mode="scaleToFill"></image>
					<view class="flex-col product-wraper__cost__left">
						<rich-text class="product-wraper__cost__left__price" :nodes="formatPrice"></rich-text>
						<text class="product-wraper__cost__left__market">行业指导价￥{{ detail.marketPrice | fen2yuanSimple }}</text>
					</view>
					<view class="flex-col product-wraper__cost__right">
						<text class="product-wraper__cost__right__sales-count">{{ detail.salesCount }}</text>
						<text class="product-wraper__cost__right__sales-name">已服务</text>
					</view>
				</view>
				<view class="felx-col product-wraper__base">
					<text class="product-wraper__base__title">{{ detail.name }}</text>
					<view class="flex product-wraper__base__advantage">
						<text v-for="(str, idx) in detail.advantageTags" :key="idx">{{ str }}</text>
					</view>
					<view class="product-wraper__base__intro">{{ detail.introduction }}</view>

					<view class="flex product-wraper__base__guarantee">
						<image class="product-wraper__base__guarantee__title-img" :src="iImage('img_detail_guarantee_text.png', 'pagesHome')"></image>
						<view class="flex product-wraper__base__guarantee__tag">
							<view class="flex product-wraper__base__guarantee__tag__item" v-for="(item, index) in guaList" :key="index">
								<image class="product-wraper__base__guarantee__tag__item__ic" :src="iImage(item.icon, 'pagesHome')"></image>
								<text class="product-wraper__base__guarantee__tag__item__txt">{{ item.name }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 服务内容 -->
			<view class="flex-col service-wraper" v-if="detail.serviceProcessNames">
				<view class="flex service-wraper__item">
					<text class="service-wraper__item__name">服务区域：</text>
					<text class="service-wraper__item__value">{{ detail.serviceAreaName }}</text>
				</view>
				<view class="flex service-wraper__item">
					<text class="service-wraper__item__name">服务内容：</text>
					<text class="service-wraper__item__value">{{ detail.serviceProcessNames.map((item, index) => `${index + 1}.${item}`).join('；') }}</text>
				</view>
				<view class="flex service-wraper__item" @click="onOpenServiceProcessPopup">
					<text class="service-wraper__item__name">业务流程：</text>
					<text class="service-wraper__item__value" style="color: #444bf1">查看业务流程</text>
					<image class="service-wraper__item__arrow" :src="iImage('ic_small_arrow_right.png')"></image>
				</view>
			</view>
			<!-- 问卷调研 -->
			<view class="flex survey-wraper">
				<image :src="iImage('img_detail_survey.png')"></image>
			</view>
			<!-- 说明内容 -->
			<view class="flex-col explain-wraper">
				<view class="flex explain-wraper__header">
					<text class="explain-wraper__header__title">企服顾问虚假承诺不诚信？</text>
					<text class="explain-wraper__header__tel">举报电话：xxxx</text>
				</view>
				<view class="explain-wraper__content">
					<text>
						{{ sysConf['content.product.warn_tip'] }}
					</text>
				</view>
			</view>
			<!-- 推荐 -->
			<view class="flex-col recommend-wraper">
				<view class="flex recommend-wraper__tab">
					<view class="flex recommend-wraper__tab__item" :class="{ active: recommendTabIndex == index }" v-for="(item, index) in recommendTabs" :key="index" @click="onRecommendTabClick(index)">
						<image class="recommend-wraper__tab__item__indicator" :src="iImage(item.tabImg, 'pagesHome')" v-show="recommendTabIndex == index"></image>
						<text>{{ item.name }}</text>
					</view>
				</view>
				<!-- 产品列表 -->
				<view class="flex-col recommend-wraper__list" v-if="recommendTabs[recommendTabIndex].id == 1">
					<list-product-item class="recommend-wraper__list__item" style-type="flat" :item="item" v-for="(item, index) in recommendList" :key="index" @click="onProductListItemClick"></list-product-item>
				</view>
				<!-- 服务商列表 -->
				<view class="flex-col recommend-wraper__list" v-if="recommendTabs[recommendTabIndex].id == 2">
					<list-provider-item class="recommend-wraper__list__item" style-type="flat" :item="item" v-for="(item, index) in recommendList" :key="index" @click="onProviderListItemClick"></list-provider-item>
				</view>
			</view>

			<u-gap height="10rpx"></u-gap>

			<!-- 底部操作条 -->
			<view slot="bottom">
				<view class="flex bottom-wraper">
					<view class="flex bottom-wraper__provider" @click="onProviderUserClick(detail.providerUser.id)">
						<image class="flex bottom-wraper__provider__avatar" :src="providerLogo" mode="aspectFill"></image>
						<view class="flex-col bottom-wraper__provider__right">
							<text class="bottom-wraper__provider__right__name line1" v-if="!$u.test.isEmpty(detail.providerUser)">{{ providerName }}</text>
							<view>
								<view class="flex bottom-wraper__provider__right__sf" :style="{ borderColor: providerIdent.color }" v-if="!$u.test.isEmpty(providerIdent)">
									<text :style="{ backgroundColor: providerIdent.color }">{{ providerIdent.name }}</text>
									<text>顾问</text>
								</view>
							</view>
						</view>
					</view>
					<view class="flex bottom-wraper__contact">
						<view class="flex bottom-wraper__contact__weixin" :class="[isDisabled || isMy ? 'disable' : '']" @click="onBottomBarItemClick(cons.DEMAND_CONSULT_CHANNEL_WECHAT)">
							<image :src="iImage('ic_detail_contact_wexin.png')"></image>
						</view>
						<view class="flex bottom-wraper__contact__tel" :class="[isDisabled || isMy ? 'disable' : '']" @click="onBottomBarItemClick(cons.DEMAND_CONSULT_CHANNEL_PHONE)">
							<image :src="iImage('ic_detail_contact_tel.png')"></image>
							<text>电话聊</text>
						</view>
						<view class="flex bottom-wraper__contact__im" :class="[isDisabled || isMy ? 'disable' : '']" @click="onBottomBarItemClick(cons.DEMAND_CONSULT_CHANNEL_ONLINE)">
							<image :src="iImage('ic_detail_contact_im.png')"></image>
							<text>在线聊</text>
						</view>
					</view>
				</view>
				<u-safe-bottom :customStyle="{ background: '#FFF' }"></u-safe-bottom>
			</view>
		</z-paging>
		<!-- 服务流程 -->
		<u-popup :show="productServiceProcessPopup.show" bgColor="transparent" mode="bottom" closeable @close="productServiceProcessPopup.show = false">
			<popup-product-service-process :processNames="detail.serviceProcessNames"></popup-product-service-process>
		</u-popup>
		<!-- 通用操作弹窗 -->
		<u-popup :show="promptPopup.show" bgColor="transparent" mode="center" @close="promptPopup.show = false">
			<popup-prompt
				:ident="promptPopup.ident"
				:icon="promptPopup.icon"
				:title="promptPopup.title"
				:message="promptPopup.message"
				:actions="promptPopup.actions"
				:tipText="promptPopup.tipText"
				:customStyle="promptPopup.customStyle"
				:showClose="promptPopup.showClose"
				@cancel="promptPopup.show = false"
			></popup-prompt>
		</u-popup>
		<!-- 客服 -->
		<u-popup :show="kfPopup.show" bgColor="transparent" mode="center" :close-on-click-overlay="false" @close="kfPopup.show = false">
			<popup-kf @close="kfPopup.show = false"></popup-kf>
		</u-popup>
	</view>
</template>

<script>
import { mapState } from 'vuex';
import { generateConversationNo } from '@/pagesChat/utils/tool.js';
import chatCons from '@/pagesChat/utils/constants.js';
import { iShare } from '@/common/utils.js';
import PopupProductServiceProcess from './component/popup-product-service-process/popup-product-service-process.vue';
export default {
	components: {
		PopupProductServiceProcess
	},
	data() {
		return {
			pageParams: {},
			// 状态栏高度
			statusBarHeight: 0,
			// 导航栏高度
			navBarHeight: 44,
			currentBannerIndex: 0,
			// 详情占位图
			placeholderImg: '/static/image/img_default.png',
			// 保障
			guaList: [
				{
					icon: 'ic_detail_guarantee.png',
					name: '财产损失'
				},
				{
					icon: 'ic_detail_guarantee.png',
					name: '服务加价'
				},
				{
					icon: 'ic_detail_guarantee.png',
					name: '未履约损失'
				}
			],
			// 服务明细
			detail: {},
			// 推荐
			recommendTabs: [
				{
					id: 1,
					name: '本地业务',
					tabImg: 'img_detail_tab_left.png',
					url: '/product/spu/recommend-list'
				},
				{
					id: 2,
					name: '本地商家',
					tabImg: 'img_detail_tab_right.png',
					url: '/provider/user/recommend-list'
				}
			],
			recommendTabIndex: 0,
			recommendList: [],
			// 产品服务流程弹窗
			productServiceProcessPopup: {
				show: false
			},
			// 通用操作弹窗
			promptPopup: {
				show: false,
				ident: 'prompt',
				showClose: false
			},
			// 客服弹窗
			kfPopup: {
				show: false
			}
		};
	},
	computed: {
		...mapState(['selectedAddress', 'userInfo', 'sysConf']),
		// 格式化价格
		formatPrice() {
			const yuan = this.$options.filters.fen2yuanSimple(this.detail?.price || 0);
			return this.$options.filters.formattedPrice({
				price: yuan,
				highlightSize: '24px'
			});
		},
		providerName() {
			if (uni.$u.test.isEmpty(this.detail?.providerUser)) {
				return '';
			}
			const map = {
				[this.cons.PROVIDER_TYPE_PERSONAL]: this.detail?.providerUser?.realName,
				[this.cons.PROVIDER_TYPE_COMPANY]: this.detail?.providerUser?.corpName
			};
			return map[String(this.detail?.providerUser?.providerType)];
		},
		providerLogo() {
			if (uni.$u.test.isEmpty(this.detail?.providerUser)) {
				return '';
			}
			const map = {
				[this.cons.PROVIDER_TYPE_PERSONAL]: this.detail?.providerUser?.avatarUrl,
				[this.cons.PROVIDER_TYPE_COMPANY]: this.detail?.providerUser?.brandLogoUrl
			};
			return map[this.detail?.providerUser?.providerType];
		},
		providerIdent() {
			if (uni.$u.test.isEmpty(this.detail?.providerUser)) {
				return {};
			}
			const map = {
				[this.cons.PROVIDER_TYPE_PERSONAL]: {
					name: '个',
					color: '#2CBDB9'
				},
				[this.cons.PROVIDER_TYPE_COMPANY]: {
					name: '企',
					color: '#4177FF'
				}
			};
			return map[this.detail?.providerUser?.providerType];
		},
		// 是否是自己的产品
		isMy() {
			if (!uni.$u.test.isEmpty(this.detail?.providerUserId) && !uni.$u.test.isEmpty(this.userInfo?.id)) {
				return this.detail?.providerUserId == this.userInfo?.id;
			}
			return false;
		},
		/**
		 * 相关的所有操作按钮是否禁用
		 */
		isDisabled() {
			// if (uni.$u.test.isEmpty(this.detail)) {
			// 	return true;
			// }
			return false;
		},
		// 分享参数
		shareParams() {
			if (!uni.$u.test.isEmpty(this.detail)) {
				return {
					itemId: this.detail.id,
					itemScene: this.cons.SHARE_SCENE_PRODUCT_DETAIL,
					path: uni.$u.page()
				};
			}
			return {};
		}
	},
	onLoad(options) {
		this.pageParams = options;
		const sys = uni.getSystemInfoSync();
		this.statusBarHeight = sys.statusBarHeight;

		this.queryDetail();
		this.queryRecommendList();
	},
	onHide() {},
	// 分享
	async onShareAppMessage(res) {
		return await iShare(this.shareParams);
	},
	onShareTimeline() {},
	methods: {
		// App分享
		onShare() {
			iShare(this.shareParams);
		},
		// 轮播切换事件
		onBannerChange(e) {
			const { current } = e.detail;
			this.currentBannerIndex = current;
		},
		// 打开服务流程弹窗
		onOpenServiceProcessPopup() {
			this.productServiceProcessPopup = { show: true };
		},
		// 推荐tab点击
		onRecommendTabClick(index) {
			this.recommendTabIndex = index;
			this.queryRecommendList();
		},
		// 产品列表条目点击
		onProductListItemClick(item) {
			this.onJump({
				url: '/pagesHome/product/detail',
				params: {
					id: item.detail.id
				}
			});
		},
		// 服务商列表条目点击
		onProviderListItemClick(item) {
			this.onJump({
				url: '/pagesHome/provider/detail',
				params: {
					id: item.detail.id
				}
			});
		},
		onProviderUserClick(id) {
			this.onJump({
				url: '/pagesHome/provider/detail',
				params: {
					id: id
				}
			});
		},
		// 底部Bar按钮点击
		onBottomBarItemClick(type) {
			// 微信咨询
			if (type == this.cons.DEMAND_CONSULT_CHANNEL_WECHAT) {
				this.consultProduct(type);
				return;
			}

			// 电话咨询
			if (type == this.cons.DEMAND_CONSULT_CHANNEL_PHONE) {
				this.consultProduct(type);
				return;
			}

			// 在线咨询
			if (type == this.cons.DEMAND_CONSULT_CHANNEL_ONLINE) {
				const conversationNo = generateConversationNo(this.userInfo.id, this.detail?.providerUserId, chatCons.CONVERSATION_TYPE.SINGLE);
				this.onJump({
					url: '/pagesChat/chat/index',
					params: {
						conversationNo: conversationNo,
						faqSceneType: '10', // FAQ
						faqSceneBizId: this.detail.serviceCategoryId, // FAQ
						enterSource: '2', // 进入来源
						targetId: this.detail.providerUserId,
						conversationType: chatCons.CONVERSATION_TYPE.SINGLE
					},
					login: true
				});
				return;
			}
		},
		// 咨询
		consultProduct(channel) {
			uni.$u.http
				.post(
					'/demand/user/quick-consult',
					{
						originType: this.cons.DEMAND_ORIGIN_TYPE_CONSULT_PRODUCT,
						consultChannel: channel,
						providerUserId: this.detail?.providerUserId,
						productSpuId: this.detail.id,
						provinceCode: this.selectedAddress.provinceId,
						cityCode: this.selectedAddress.cityId,
						districtCode: 111
					},
					{ custom: { showLoading: true, loadingMsg: '提交中..' } }
				)
				.then((res) => {})
				.catch((err) => {});
		},
		// 查询服务明细
		queryDetail() {
			uni.$u.http
				.get('/product/spu/get-detail', {
					params: {
						id: this.pageParams.id
					}
				})
				.then((res) => {
					this.detail = res;
				})
				.catch((err) => {});
		},
		// 查询推荐列表
		queryRecommendList() {
			const reqUrl = this.recommendTabs[this.recommendTabIndex || 0].url;
			uni.$u.http
				.get(reqUrl, {
					params: {
						id: this.pageParams.id,
						cityCode: this.selectedAddress?.cityId || ''
					}
				})
				.then((res) => {
					this.recommendList = res;
				})
				.catch((err) => {});
		}
	}
};
</script>

<style lang="scss" scoped>
/* 导航栏 */
.nav-wraper {
	z-index: 999;
	width: 750rpx;
	background: transparent;
	position: fixed;
	top: 0;
	left: 0;

	&__content {
		flex: 1;
		justify-content: space-between;

		&__left {
			width: 120rpx;
			height: 100%;
			padding: 0 30rpx;

			image {
				width: 55rpx;
				height: 55rpx;
			}
		}

		&__mid {
			flex: 1;
			height: 80rpx;
			justify-content: center;
		}

		&__right {
			width: 120rpx;
			height: 100%;
			padding: 0 30rpx;
			justify-content: flex-end;

			image {
				width: 55rpx;
				height: 55rpx;
			}
		}
	}
}

/* Banner */
.banner-wraper {
	position: relative;

	&__swiper {
		width: 100%;
		height: 562rpx;

		&__image {
			width: 100%;
			height: 562rpx;
		}
	}

	&__number {
		width: 80rpx;
		height: 42rpx;
		background-color: rgba(33, 33, 33, 0.49);
		font-size: 24rpx;
		color: #fefefe;
		border-radius: 40rpx;
		align-items: center;
		justify-content: center;
		position: absolute;
		bottom: 20rpx;
		right: 30rpx;
	}
}

/* 产品内容 */
.product-wraper {
	margin: 20rpx 20rpx 0 20rpx;
	position: relative;

	&__cost {
		z-index: 5;
		width: 100%;
		height: 136rpx;
		padding-left: 30rpx;
		padding-right: 30rpx;
		padding-bottom: 20rpx;
		justify-content: space-between;
		position: relative;

		&__bg {
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0;
			left: 0;
		}

		&__left {
			color: #ffffff;

			&__price {
				font-weight: bold;
				font-size: 40rpx;
			}

			&__market {
				font-size: 24rpx;
				line-height: 36rpx;
				opacity: 0.7;
			}
		}

		&__right {
			z-index: 5;
			align-items: flex-end;
			font-size: 24rpx;
			color: #ffffff;

			&__sales-count {
				color: #ffffff;
				line-height: 30rpx;
			}
		}
	}

	&__base {
		flex: 1;
		z-index: 10;
		margin-top: -20rpx;
		padding: 30rpx 30rpx 10rpx 30rpx;
		background: #ffffff;
		border-radius: 20rpx;
		overflow: hidden;

		&__title {
			font-weight: bold;
			font-size: 42rpx;
			color: #1e2134;
			line-height: 34rpx;
		}

		&__advantage {
			margin-top: 15rpx;
			flex-wrap: wrap;

			text {
				margin-right: 10rpx;
				padding: 5rpx 10rpx;
				background: #f0f1ff;
				border-radius: 5rpx;
				border: 0.8px solid #444bf1;
				font-size: 28rpx;
				color: #444bf1;
			}
		}

		&__intro {
			margin-top: 15rpx;
			font-size: 28rpx;
			color: #909099;
			line-height: 36rpx;
		}

		&__guarantee {
			height: 56rpx;
			margin-left: -20rpx;
			margin-right: -20rpx;
			margin-top: 30rpx;
			padding: 0 20rpx;
			background: #1e2134;
			border-radius: 0rpx 0rpx 20rpx 20rpx;

			&__title-img {
				width: 110rpx;
				height: 22rpx;
			}

			&__tag {
				flex: 1;
				margin-left: 20rpx;
				flex-wrap: wrap;
				align-items: flex-start;

				&__item {
					margin: 5rpx 20rpx 5rpx 0;

					&:last-child {
						margin-right: 0;
					}

					&__ic {
						width: 28rpx;
						height: 28rpx;
					}

					&__txt {
						margin-left: 5rpx;
						font-size: 24rpx;
						color: #ffffff;
					}
				}
			}
		}
	}
}

/* 服务内容 */
.service-wraper {
	margin: 20rpx 20rpx 0 20rpx;
	padding: 15rpx 30rpx;
	background: #ffffff;
	border-radius: 20rpx;

	&__item {
		padding: 15rpx 0;
		align-items: flex-start;

		&__name {
			color: #909099;
			font-weight: 500;
			font-size: 28rpx;
			white-space: nowrap;
		}

		&__value {
			font-weight: 600;
			font-size: 30rpx;
			color: #333333;
		}

		&__arrow {
			width: 10rpx;
			height: 17rpx;
			margin: auto 0 auto auto;
		}
	}
}

/* 问卷调研 */
.survey-wraper {
	margin: 20rpx 20rpx 0 20rpx;

	image {
		width: 100%;
		height: 140rpx;
	}
}

/* 说明内容 */
.explain-wraper {
	margin: 20rpx 20rpx 0 20rpx;
	padding: 15rpx 30rpx;
	background: #ffffff;
	border-radius: 20rpx;

	&__header {
		justify-content: space-between;

		&__title {
			font-weight: 600;
			font-size: 30rpx;
			color: #1e2134;
			line-height: 70rpx;
		}

		&__tel {
			font-weight: 400;
			font-size: 24rpx;
			color: #444bf1;
			line-height: 70rpx;
		}
	}

	&__content {
		font-size: 24rpx;
		color: #909099;
		line-height: 40rpx;
		text-align: justify;
	}
}

/* 推荐 */
.recommend-wraper {
	margin: 20rpx 20rpx 0 20rpx;
	background: #ffffff;
	border-radius: 20rpx;

	&__tab {
		background-color: #e8e9ef;
		justify-content: space-between;

		&__item {
			width: 50%;
			height: 80rpx;
			justify-content: center;
			font-size: 36rpx;
			color: #909099;
			position: relative;

			&__indicator {
				width: 100%;
				height: 100%;
				z-index: 0;
				position: absolute;
				left: 0;
				top: 0;
			}

			text {
				z-index: 5;
			}

			&.active {
				color: #1e2134;
				font-weight: bold;
			}
		}
	}

	&__list {
		&__item {
			width: 100%;
		}
	}
}

/* 底部操作条 */
.bottom-wraper {
	flex: 1;
	height: 120rpx;
	padding: 0 30rpx;
	background: #ffffff;
	box-shadow: 0rpx 2rpx 10rpx 0rpx rgba(174, 174, 174, 0.53);
	justify-content: space-between;

	&__provider {
		&__avatar {
			width: 80rpx;
			min-width: 80rpx;
			height: 80rpx;
			background: #f3f4f6;
			border-radius: 15rpx;
			border: 0.8px solid #e0e0e0;
		}

		&__right {
			height: 80rpx;
			margin-left: 15rpx;
			justify-content: space-around;

			&__name {
				font-weight: 600;
				font-size: 30rpx;
				color: #1e2134;
			}

			&__sf {
				height: 30rpx;
				display: inline-block;
				background-color: #ffffff;
				border-radius: 5rpx;
				border: 2rpx solid #444bf1;
				font-weight: 400;
				font-size: 22rpx;
				color: #1e2134;

				text:nth-child(1) {
					padding: 0 5rpx;
					background-color: #444bf1;
					color: #ffffff;
					font-weight: bold;
				}

				text:nth-child(2) {
					padding: 0 5rpx;
				}
			}
		}
	}

	&__contact {
		&__weixin {
			width: 80rpx;
			height: 80rpx;
			background: #2abd79;
			border-radius: 10rpx;
			justify-content: center;

			image {
				width: 44rpx;
				height: 44rpx;
			}
		}

		&__tel,
		&__im {
			width: 180rpx;
			height: 80rpx;
			background: #444bf1;
			border-radius: 10rpx;
			justify-content: center;

			image {
				width: 30rpx;
				height: 30rpx;
			}

			text {
				margin-left: 10rpx;
				font-weight: 600;
				font-size: 30rpx;
				color: #ffffff;
				line-height: 36rpx;
			}
		}

		&__im {
			background: #f43a55;
		}
	}

	&__contact > view:not(:last-child) {
		margin-right: 10rpx;
	}
}
</style>
