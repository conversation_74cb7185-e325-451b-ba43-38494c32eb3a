<template>
	<view>
		<public-module></public-module>
		<z-paging ref="paging">
			<!-- 顶部导航 -->
			<template #top>
				<u-navbar bgColor="#FFFFFF" title="顾问名片" :statusBar="true" :placeholder="true" :autoBack="true">
					<view slot="right" class="flex nav-share">
						<x-share :class="[isDisabled ? 'disable' : '']" @share="onShare">
							<image style="" :src="iImage('ic_nav_share.png', 'pagesHome')" mode="aspectFill"></image>
						</x-share>
					</view>
				</u-navbar>
			</template>
			<!-- 服务商基本信息 -->
			<view class="flex-col provider-wrap">
				<view class="flex provider-wrap__base">
					<image class="flex provider-wrap__base__avatar" :src="logo" mode="aspectFill"></image>
					<view class="flex-col provider-wrap__base__right">
						<text class="provider-wrap__base__right__name">{{ title }}</text>
						<view class="flex provider-wrap__base__right__box" v-if="!$u.test.isEmpty(ident)">
							<view class="flex provider-wrap__base__right__box__sf" :style="{ borderColor: ident.color }">
								<text :style="{ backgroundColor: ident.color }">{{ ident.name }}</text>
								<text>顾问</text>
							</view>
							<text class="provider-wrap__base__right__box__info">{{ detail.experienceDesc }} | 126人已咨询</text>
						</view>
					</view>
				</view>
				<view class="flex provider-wrap__address">
					<image class="provider-wrap__address__ic" :src="iImage('ic_detail_address.png', 'pagesHome')" mode="aspectFit"></image>
					<text class="provider-wrap__address__name">{{ addressName }}</text>
					<view class="flex provider-wrap__address__contact">咨询</view>
				</view>
				<scroll-view scroll-x class="flex provider-wrap__promotion" v-if="!$u.test.isEmpty(detail.promotionPicUrls)">
					<view class="provider-wrap__promotion__item" v-for="(url, idx) in detail.promotionPicUrls" :key="idx">
						<image :src="url" mode="aspectFill"></image>
					</view>
				</scroll-view>
				<view class="flex provider-wrap__guarantee">
					<image class="provider-wrap__guarantee__title-img" :src="iImage('img_detail_guarantee_text.png', 'pagesHome')"></image>
					<view class="flex provider-wrap__guarantee__tag">
						<view class="flex provider-wrap__guarantee__tag__item" v-for="(item, index) in guaList" :key="index">
							<image class="provider-wrap__guarantee__tag__item__ic" :src="iImage(item.icon, 'pagesHome')"></image>
							<text class="provider-wrap__guarantee__tag__item__txt">{{ item.name }}</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 服务内容 -->
			<view class="flex-col service-wraper">
				<view class="flex service-wraper__item" v-if="!$u.test.isEmpty(detail.serviceCategoryNames)">
					<text class="service-wraper__item__name">主营业务：</text>
					<text class="service-wraper__item__value">{{ detail.serviceCategoryNames.join('、') }}</text>
				</view>
				<view class="flex service-wraper__item" v-if="!$u.test.isEmpty(detail.serviceRegionNames)">
					<text class="service-wraper__item__name">服务区域：</text>
					<text class="service-wraper__item__value">{{ detail.serviceRegionNames.join('、') }}</text>
				</view>
				<view class="flex service-wraper__item" v-if="!$u.test.isEmpty(detail.description)">
					<text class="service-wraper__item__name">服务介绍：</text>
					<text class="service-wraper__item__value">{{ detail.description }}</text>
				</view>
			</view>
			<!-- 问卷调研 -->
			<view class="flex survey-wraper">
				<image :src="iImage('img_detail_survey.png')"></image>
			</view>
			<!-- 说明内容 -->
			<view class="flex-col explain-wraper">
				<view class="flex explain-wraper__header">
					<text class="explain-wraper__header__title">企服顾问虚假承诺不诚信？</text>
					<text class="explain-wraper__header__tel">举报电话：954310</text>
				</view>
				<view class="explain-wraper__content">
					<rich-text :nodes="`涉及到以下行为均可联系我们投诉或举报<br/> 1、企服顾问虚假承诺，欺骗客户;<br/> 2、私下通过微信、现金等方式收取款项;<br/> 3、线下介绍非`"></rich-text>
				</view>
			</view>
			<!-- 推荐 -->
			<view class="flex-col recommend-wraper">
				<view class="flex recommend-wraper__tab">
					<view class="flex recommend-wraper__tab__item" :class="{ active: recommendTabIndex == index }" v-for="(item, index) in recommendTabs" :key="index" @click="onRecommendTabClick(index)">
						<image class="recommend-wraper__tab__item__indicator" :src="iImage(item.tabImg, 'pagesHome')" v-show="recommendTabIndex == index"></image>
						<text>{{ item.name }}</text>
					</view>
				</view>
				<!-- 产品列表 -->
				<view class="flex-col recommend-wraper__list" v-if="recommendTabs[recommendTabIndex].id == 1">
					<list-product-item class="recommend-wraper__list__item" style-type="flat" :item="item" v-for="(item, index) in recommendDataList" :key="index" @click="onProductListItemClick"></list-product-item>
				</view>
				<!-- 服务商列表 -->
				<view class="flex-col recommend-wraper__list" v-if="recommendTabs[recommendTabIndex].id == 2">
					<list-provider-item class="recommend-wraper__list__item" style-type="flat" :item="item" v-for="(item, index) in recommendDataList" :key="index" @click="onProviderListItemClick"></list-provider-item>
				</view>
			</view>

			<u-gap height="40rpx"></u-gap>

			<!-- 底部操作条 -->
			<template #bottom>
				<view class="flex bottom-wraper">
					<view id="bottom-bar-weixin" class="flex bottom-wraper__weixin" :class="[isDisabled ? 'disable' : '']" @click="onBottomBarItemClick('weixin')">
						<image :src="iImage('ic_detail_contact_wexin.png')"></image>
						<text>微信聊</text>
					</view>
					<view id="bottom-bar-tel" class="flex bottom-wraper__tel" :class="[isDisabled ? 'disable' : '']" @click="onBottomBarItemClick('tel')">
						<image :src="iImage('ic_detail_contact_tel.png')"></image>
						<text>电话聊</text>
					</view>
					<view id="bottom-bar-im" class="flex bottom-wraper__im" :class="[isDisabled ? 'disable' : '']" @click="onBottomBarItemClick('im')">
						<image :src="iImage('ic_detail_contact_im.png')"></image>
						<text>在线聊</text>
					</view>
				</view>
				<u-safe-bottom :customStyle="{ background: '#FFF' }"></u-safe-bottom>
			</template>
		</z-paging>
		<!-- 通用操作弹窗 -->
		<u-popup :show="promptPopup.show" bgColor="transparent" mode="center" @close="promptPopup.show = false">
			<popup-prompt
				:ident="promptPopup.ident"
				:icon="promptPopup.icon"
				:title="promptPopup.title"
				:message="promptPopup.message"
				:actions="promptPopup.actions"
				:tipText="promptPopup.tipText"
				:customStyle="promptPopup.customStyle"
				:showClose="promptPopup.showClose"
				@cancel="promptPopup.show = false"
			></popup-prompt>
		</u-popup>
		<!-- 客服 -->
		<u-popup :show="kfPopup.show" bgColor="transparent" mode="center" :close-on-click-overlay="false" @close="kfPopup.show = false">
			<popup-kf @close="kfPopup.show = false"></popup-kf>
		</u-popup>

		<x-bubble :target="bubblePopover.target" :visible="bubblePopover.show" popoverWidth="400" popoverHeight="100" @close="bubblePopover.show = false">
			<text>这是一个弹窗内容</text>
		</x-bubble>
	</view>
</template>

<script>
import { mapState } from 'vuex';
import { generateConversationNo } from '@/pagesChat/utils/tool.js';
import chatCons from '@/pagesChat/utils/constants.js';
import { iShare } from '@/common/utils.js';
export default {
	components: {},
	data() {
		return {
			pageParams: {},
			// 状态栏高度
			statusBarHeight: 0,
			// 导航栏高度
			navBarHeight: 44,
			// 顶部轮播
			banners: [],
			currentBannerIndex: 0,
			// 详情占位图
			placeholderImg: '/static/image/img_default.png',
			// 保障
			guaList: [
				{
					icon: 'ic_detail_guarantee.png',
					name: '财产损失'
				},
				{
					icon: 'ic_detail_guarantee.png',
					name: '服务加价'
				},
				{
					icon: 'ic_detail_guarantee.png',
					name: '未履约损失'
				}
			],
			// 服务明细
			detail: {},
			// 推荐
			recommendTabs: [
				{
					id: 1,
					name: '本地业务',
					tabImg: 'img_detail_tab_left.png',
					url: '/product/spu/page'
				},
				{
					id: 2,
					name: '本地商家',
					tabImg: 'img_detail_tab_right.png',
					url: '/product/spu/page'
				}
			],
			recommendTabIndex: 0,
			recommendDataList: [{}, {}, {}],
			// 通用操作弹窗
			promptPopup: {
				show: false,
				ident: 'prompt',
				showClose: false
			},
			// 客服弹窗
			kfPopup: {
				show: false
			},
			bubblePopover: {
				show: false,
				target: ''
			}
		};
	},
	computed: {
		...mapState(['userInfo', 'sysConf']),
		title() {
			const map = {
				[this.cons.PROVIDER_TYPE_PERSONAL]: this.detail?.realName,
				[this.cons.PROVIDER_TYPE_COMPANY]: this.detail?.corpName
			};
			return map[String(this.detail?.providerType)];
		},
		logo() {
			const map = {
				[this.cons.PROVIDER_TYPE_PERSONAL]: this.detail?.avatarUrl,
				[this.cons.PROVIDER_TYPE_COMPANY]: this.detail?.brandLogoUrl
			};
			return map[this.detail?.providerType];
		},
		ident() {
			const map = {
				[this.cons.PROVIDER_TYPE_PERSONAL]: { name: '个', color: '#2CBDB9' },
				[this.cons.PROVIDER_TYPE_COMPANY]: { name: '企', color: '#4177FF' }
			};
			return map[this.detail?.providerType];
		},
		addressName() {
			const map = {
				[this.cons.PROVIDER_TYPE_PERSONAL]: [this.detail?.provinceName, this.detail?.cityName, this.detail?.districtName].join(''),
				[this.cons.PROVIDER_TYPE_COMPANY]: [this.detail?.provinceName, this.detail?.cityName, this.detail?.districtName, this.detail?.addressDetail].join('')
			};
			return map[this.detail?.providerType];
		},
		/**
		 * 相关的所有操作按钮是否禁用
		 */
		isDisabled() {
			if (uni.$u.test.isEmpty(this.detail)) {
				return true;
			}
			return false;
		},
		// 分享参数
		shareParams() {
			if (!uni.$u.test.isEmpty(this.detail)) {
				return {
					itemId: this.detail.id,
					itemScene: this.cons.SHARE_SCENE_PRODUCT_DETAIL,
					path: uni.$u.page()
				};
			}
			return {};
		}
	},
	onLoad(options) {
		this.pageParams = options;
		const sys = uni.getSystemInfoSync();
		this.statusBarHeight = sys.statusBarHeight;

		this.queryDetail();
	},
	onHide() {},
	// 分享
	async onShareAppMessage(res) {
		return await iShare(this.shareParams);
	},
	onShareTimeline() {},
	methods: {
		// App分享
		onShare() {
			iShare(this.shareParams);
		},
		// 轮播切换事件
		onBannerChange(e) {
			const { current } = e.detail;
			this.currentBannerIndex = current;
		},
		// 推荐tab点击
		onRecommendTabClick(index) {
			this.recommendTabIndex = index;
		},
		// 产品列表条目点击
		onProductListItemClick(e) {},
		// 服务商列表条目点击
		onProviderListItemClick(e) {},
		// 底部Bar按钮点击
		onBottomBarItemClick(type) {
			// 微信咨询
			if (type == 'weixin') {
				this.bubblePopover = {
					target: '.bottom-wraper__weixin',
					show: true
				};
				return;
			}

			// 电话咨询
			if (type == 'tel') {
				this.bubblePopover = {
					target: '#bottom-bar-tel',
					show: true
				};
				return;
			}

			// 在线咨询
			if (type == 'im') {
				this.bubblePopover = {
					target: '#bottom-bar-im',
					show: true
				};
				return;
				// const conversationNo = generateConversationNo(this.userInfo.id, this.detail.providerUserId, chatCons.CONVERSATION_TYPE.SINGLE);
				// this.onJump({
				// 	url: '/pagesChat/chat/index',
				// 	params: {
				// 		conversationNo: conversationNo,
				// 		targetId: this.detail.providerUserId,
				// 		conversationType: chatCons.CONVERSATION_TYPE.SINGLE
				// 	},
				// 	login: true
				// });
				return;
			}
		},
		// 查询服务明细
		queryDetail() {
			uni.$u.http
				.get('/provider/user/get-detail', {
					params: {
						id: this.pageParams.id
					}
				})
				.then((res) => {
					this.detail = res;
				})
				.catch((err) => {});
		}
	}
};
</script>

<style lang="scss" scoped>
/* NavBar */
.nav-share {
	width: 120rpx;
	height: 100%;
	padding: 0 30rpx;
	justify-content: flex-end;

	image {
		width: 33rpx;
		height: 33rpx;
	}
}

/* 服务商基本信息 */
.provider-wrap {
	margin: 20rpx 20rpx 0 20rpx;
	padding: 0 20rpx 10rpx 20rpx;
	background: #ffffff;
	border-radius: 20px;

	&__base {
		padding: 30rpx 0;
		align-items: flex-start;
		border-bottom: 0.8px solid #e6e6e6;

		&__avatar {
			width: 120rpx;
			height: 120rpx;
			flex-shrink: 0;
			background: #f3f4f6;
			border-radius: 20rpx;
			border: 0.8px solid #e0e0e0;
		}

		&__right {
			min-height: 120rpx;
			margin-left: 20rpx;
			justify-content: center;

			&__name {
				font-weight: 800;
				font-size: 42rpx;
				color: #333333;
			}

			&__box {
				margin-top: 20rpx;

				&__sf {
					height: 30rpx;
					background-color: #ffffff;
					border-radius: 5rpx;
					border: 2rpx solid #444bf1;
					font-weight: 400;
					font-size: 22rpx;
					color: #1e2134;
					text-align: center;
					white-space: nowrap;

					text:nth-child(1) {
						flex: 1;
						padding: 0 5rpx;
						background-color: #444bf1;
						color: #ffffff;
						font-weight: bold;
					}

					text:nth-child(2) {
						flex: 1;
						padding: 0 5rpx;
					}
				}

				&__info {
					margin-left: 20rpx;
					font-size: 24rpx;
					color: #1e2134;
					white-space: nowrap;
				}
			}
		}
	}

	&__address {
		margin-top: 25rpx;
		align-items: flex-start;

		&__ic {
			width: 24rpx;
			height: 60rpx;
			flex-shrink: 0;
		}

		&__name {
			margin-left: 10rpx;
			font-size: 28rpx;
			color: #1e2134;
			line-height: 60rpx;
		}

		&__contact {
			width: 120rpx;
			height: 60rpx;
			margin-right: -20rpx;
			flex-shrink: 0;
			margin-left: auto;
			background: #f43a55;
			color: #ffffff;
			font-size: 30rpx;
			border-radius: 30rpx 0 0 30rpx;
			font-weight: bold;
			align-items: center;
			justify-content: center;
		}
	}

	&__promotion {
		margin-top: 20rpx;
		white-space: nowrap;

		&__item {
			width: 240rpx;
			height: 180rpx;
			margin-right: 20rpx;
			background: #f6f5f7;
			border-radius: 15rpx;
			overflow: hidden;
			display: inline-block;

			&:last-child {
				margin-right: 0;
			}

			image {
				width: 100%;
				height: 100%;
			}
		}
	}

	&__guarantee {
		height: 56rpx;
		margin-top: 20rpx;
		margin-left: -10rpx;
		margin-right: -10rpx;
		padding: 0 20rpx;
		background: #1e2134;
		border-radius: 0rpx 0rpx 20rpx 20rpx;

		&__title-img {
			width: 110rpx;
			height: 22rpx;
		}

		&__tag {
			flex: 1;
			margin-left: 20rpx;
			flex-wrap: wrap;
			align-items: flex-start;

			&__item {
				margin: 5rpx 20rpx 5rpx 0;

				&:last-child {
					margin-right: 0;
				}

				&__ic {
					width: 28rpx;
					height: 28rpx;
				}

				&__txt {
					margin-left: 5rpx;
					font-size: 24rpx;
					color: #ffffff;
				}
			}
		}
	}
}

/* 产品内容 */
.product-wraper {
	margin: 20rpx 20rpx 0 20rpx;
	position: relative;

	&__cost {
		z-index: 5;
		width: 100%;
		height: 136rpx;
		padding-left: 30rpx;
		padding-right: 30rpx;
		padding-bottom: 20rpx;
		justify-content: space-between;
		position: relative;

		&__bg {
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0;
			left: 0;
		}

		&__left {
			color: #ffffff;

			&__price {
				font-weight: bold;
				font-size: 40rpx;
			}

			&__market {
				font-size: 24rpx;
				line-height: 36rpx;
				opacity: 0.7;
			}
		}

		&__right {
			font-size: 24rpx;
			font-weight: 600;
			color: #ffffff;

			&__sales-count {
				color: #ffffff;
				line-height: 30rpx;
			}
		}
	}

	&__base {
		flex: 1;
		z-index: 10;
		margin-top: -20rpx;
		padding: 30rpx 30rpx 10rpx 30rpx;
		background: #ffffff;
		border-radius: 20rpx;
		overflow: hidden;

		&__title {
			font-weight: bold;
			font-size: 42rpx;
			color: #1e2134;
			line-height: 34rpx;
		}

		&__advantage {
			margin-top: 15rpx;
			flex-wrap: wrap;

			text {
				margin-right: 10rpx;
				padding: 5rpx 10rpx;
				background: #f0f1ff;
				border-radius: 5rpx;
				border: 0.8px solid #444bf1;
				font-size: 28rpx;
				color: #444bf1;
			}
		}

		&__intro {
			margin-top: 15rpx;
			font-size: 28rpx;
			color: #909099;
			line-height: 36rpx;
		}

		&__guarantee {
			height: 56rpx;
			margin-left: -20rpx;
			margin-right: -20rpx;
			margin-top: 30rpx;
			padding: 0 20rpx;
			background: #1e2134;
			border-radius: 0rpx 0rpx 20rpx 20rpx;

			&__title-img {
				width: 110rpx;
				height: 22rpx;
			}

			&__tag {
				flex: 1;
				margin-left: 20rpx;
				flex-wrap: wrap;
				align-items: flex-start;

				&__item {
					margin: 5rpx 20rpx 5rpx 0;

					&:last-child {
						margin-right: 0;
					}

					&__ic {
						width: 28rpx;
						height: 28rpx;
					}

					&__txt {
						margin-left: 5rpx;
						font-size: 24rpx;
						color: #ffffff;
					}
				}
			}
		}
	}
}

/* 服务内容 */
.service-wraper {
	margin: 20rpx 20rpx 0 20rpx;
	padding: 15rpx 30rpx;
	background: #ffffff;
	border-radius: 20rpx;

	&__item {
		padding: 15rpx 0;
		align-items: flex-start;

		&__name {
			color: #909099;
			font-weight: 500;
			font-size: 28rpx;
			white-space: nowrap;
		}

		&__value {
			font-weight: 600;
			font-size: 30rpx;
			color: #333333;
		}

		&__arrow {
			width: 10rpx;
			height: 17rpx;
			margin: auto 0 auto auto;
		}
	}
}

/* 问卷调研 */
.survey-wraper {
	margin: 20rpx 20rpx 0 20rpx;

	image {
		width: 100%;
		height: 140rpx;
	}
}

/* 说明内容 */
.explain-wraper {
	margin: 20rpx 20rpx 0 20rpx;
	padding: 15rpx 30rpx;
	background: #ffffff;
	border-radius: 20rpx;

	&__header {
		justify-content: space-between;

		&__title {
			font-weight: 600;
			font-size: 30rpx;
			color: #1e2134;
			line-height: 70rpx;
		}

		&__tel {
			font-weight: 400;
			font-size: 24rpx;
			color: #444bf1;
			line-height: 70rpx;
		}
	}

	&__content {
		font-size: 24rpx;
		color: #909099;
		line-height: 40rpx;
		text-align: justify;
	}
}

/* 推荐 */
.recommend-wraper {
	margin: 20rpx 20rpx 0 20rpx;
	background: #ffffff;
	border-radius: 20rpx;

	&__tab {
		background-color: #e8e9ef;
		justify-content: space-between;

		&__item {
			width: 50%;
			height: 80rpx;
			justify-content: center;
			font-size: 36rpx;
			color: #909099;
			position: relative;

			&__indicator {
				width: 100%;
				height: 100%;
				z-index: 0;
				position: absolute;
				left: 0;
				top: 0;
			}

			text {
				z-index: 5;
			}

			&.active {
				color: #1e2134;
				font-weight: bold;
			}
		}
	}

	&__list {
		&__item {
			width: 100%;
		}
	}
}

/* 底部操作条 */
.bottom-wraper {
	flex: 1;
	height: 120rpx;
	padding: 0 30rpx;
	background: #ffffff;
	box-shadow: 0rpx 2rpx 10rpx 0rpx rgba(174, 174, 174, 0.53);
	justify-content: space-between;

	&__weixin,
	&__tel,
	&__im {
		width: 180rpx;
		height: 80rpx;
		background: #444bf1;
		border-radius: 10rpx;
		justify-content: center;

		image {
			width: 30rpx;
			height: 30rpx;
		}

		text {
			margin-left: 10rpx;
			font-weight: 600;
			font-size: 30rpx;
			color: #ffffff;
			line-height: 36rpx;
		}
	}

	&__weixin {
		background: #2abd79;
	}

	&__im {
		flex: 1;
		width: 100%;
		background: #f43a55;
	}
}

.bottom-wraper > view:not(:last-child) {
	margin-right: 10rpx;
}
</style>
