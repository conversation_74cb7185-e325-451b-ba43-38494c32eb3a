<template>
	<view class="flex-col wraper" @click.stop="click" v-if="!$u.test.isEmpty(item)">
		<view class="flex wraper__type" v-if="!$u.test.isEmpty(demandTypeName)">
			<image class="wraper__type__bg" :src="iImage('img_demand_type_bg.png')"></image>
			<text>{{demandTypeName}}</text>
		</view>
		<view class="flex wraper__top">
			<view class="flex wraper__top__short-title" v-if="!$u.test.isEmpty(demandTitleObj.shortTitle)">{{demandTitleObj.shortTitle}}</view>
			<text class="wraper__top__title line1">{{demandTitleObj.title}}</text>
			<image class="wraper__top__new animate__animated animate__bounceIn animate__infinite"
				:src="iImage('img_customer_new.png')"></image>
		</view>
		<view class="flex wraper__tag-content">
			<view class="flex wraper__tag-content__tag" v-for="(t, i) in tagFields" :key="i">
				<image :src="iImage(t.ic)" mode="aspectFit"></image>
				<text>{{ t.value }}</text>
			</view>
		</view>
		<text class="wraper__remark line1" v-if="!$u.test.isEmpty(item.remark)">{{item.remark}}</text>
		<view class="flex wraper__bottom">
			<view class="flex">
				<image class="wraper__bottom__user-avatar" :src="iImage('img_demand_user_avatar.png')"></image>
				<text class="wraper__bottom__user-title">{{contactTitle}}</text>
			</view>
			<view class="flex wraper__bottom__btn">去App接单</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'list-hall-demand-item',
		props: {
			// 列表条目数据
			item: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {};
		},
		computed: {
			/**
			 * 需求标题信息（包含主标题 title 和简称 shortTitle）
			 * - 发布来源（originType=3）根据子类型判断展示哪个分类名称
			 * - 预约产品 / 商家，统一展示服务分类名称
			 */
			demandTitleObj() {
				const subtypeMap = {
					[this.cons.DEMAND_ORIGIN_SUBTYPE_NORMAL]: {
						title: this.item.serviceCategoryName,
						shortTitle: this.item.serviceCategoryShortName
					},
					[this.cons.DEMAND_ORIGIN_SUBTYPE_QUALIFICATION]: {
						title: this.item.qualificationCategoryName,
						shortTitle: this.item.qualificationCategoryShortName
					},
					[this.cons.DEMAND_ORIGIN_SUBTYPE_DECLARATION]: {
						title: this.item.declarationCategoryName,
						shortTitle: this.item.declarationCategoryShortName
					}
				};

				// 主动发布的需求（根据子类型选择标题）
				if (this.item.originType === this.cons.DEMAND_ORIGIN_TYPE_PUBLISH) {
					return subtypeMap[this.item.originSubtype] || {
						title: '',
						shortTitle: ''
					};
				}

				// 预约产品/服务商的需求（统一用服务分类）
				if (
					this.item.originType === this.cons.DEMAND_ORIGIN_TYPE_APPOINT_PRODUCT ||
					this.item.originType === this.cons.DEMAND_ORIGIN_TYPE_APPOINT_MERCHANT
				) {
					const title = this.item.serviceCategoryName || '';
					const shortTitle = this.item.serviceCategoryShortName || '';
					return {
						title,
						shortTitle
					};
				}

				// 默认空值
				return {
					title: '',
					shortTitle: ''
				};
			},
			// 称呼
			contactTitle() {
				const title = this.item?.contactTitle || ''
				const suffixMap = {
					[this.cons.SEX_MALE]: '先生',
					[this.cons.SEX_FEMALE]: '女士'
				}
				const suffix = suffixMap[this.item?.sex] || ''
				return title + suffix
			},
			// 需求类型
			demandTypeName() {
				const typeMap = {
					[this.cons.DEMAND_TYPE_CLUE]: '线索单',
					[this.cons.DEMAND_TYPE_APPOINT]: '预约单',
				}
				return typeMap[this.item.demandType]
			},
			//
			tagFields() {
				return [{
						ic: 'ic_hall_demand_city.png',
						value: this.item.areaName
					},
					{
						ic: 'ic_hall_demand_tel.png',
						value: this.item.contactMobile
					}
				];
			}
		},
		methods: {
			click() {
				this.$emit('click', {
					detail: this.item
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.wraper {
		margin: 0 30rpx;
		padding: 25rpx 20rpx;
		background-color: #FFFFFF;
		box-shadow: 0rpx 0rpx 18rpx 0rpx rgba(126, 129, 137, 0.16);
		border-radius: 20rpx;
		position: relative;
		overflow: hidden;

		&__type {
			width: 132rpx;
			height: 54rpx;
			font-weight: 500;
			font-size: 24rpx;
			color: #ff512d;
			position: absolute;
			top: 0;
			right: 0;

			&__bg {
				z-index: 0;
				width: 100%;
				height: 100%;
				position: absolute;
				top: 0;
				right: 0;
			}

			text {
				z-index: 5;
				margin-left: 50rpx;
			}
		}

		&__top {
			align-items: center;

			&__short-title {
				width: 68rpx;
				height: 36rpx;
				margin-right: 15rpx;
				background-color: #444bf1;
				justify-content: center;
				border-radius: 0rpx 18rpx 18rpx 0rpx;
				font-size: 24rpx;
				color: #ffffff;
			}

			&__title {
				max-width: 70%;
				font-weight: bold;
				font-size: 34rpx;
				color: #1e2134;
				line-height: 36rpx;
			}

			&__new {
				width: 66rpx;
				height: 40rpx;
				margin-left: 15rpx;
			}
		}

		&__tag-content {
			padding: 10rpx 20rpx;
			margin-top: 20rpx;
			background-color: #F5F5F7;
			border-radius: 10rpx;
			flex-wrap: wrap;

			&__tag {
				height: 55rpx;
				margin-right: 30rpx;

				image {
					width: 30rpx;
					height: 30rpx;
				}

				text {
					margin-left: 5rpx;
					font-weight: 400;
					font-size: 28rpx;
					color: #1e2134;
				}
			}
		}

		&__remark {
			margin-top: 20rpx;
			font-weight: 400;
			font-size: 28rpx;
			color: #5f6069;
		}

		&__bottom {
			margin-top: 20rpx;
			justify-content: space-between;

			&__user-avatar {
				width: 46rpx;
				height: 46rpx;
				background: #f3f4f6;
				border-radius: 50%;
			}

			&__user-title {
				margin-left: 10rpx;
				font-weight: 400;
				font-size: 28rpx;
				color: #1e2134;
			}

			&__btn {
				width: 180rpx;
				height: 60rpx;
				background: #444bf1;
				color: #ffffff;
				font-size: 30rpx;
				border-radius: 30rpx;
				font-weight: 600;
				justify-content: center;
			}
		}
	}
</style>