<template>
	<view>
		<u-popup :show="noticeSettingPopupShow" bgColor="transparent" mode="bottom" zIndex="999998">
			<view class="col popup-content">
				<view class="col container">
					<image class="bgimg" :src="iImage('img_notification_setting.png')" mode="widthFix"></image>
					<view class="row open-btn" @click="onOpen()">
						<text class="open-btn-text">去开启</text>
					</view>
					<view class="row no-btn" @click="onClose()">
						<text class="no-btn-text">暂不开启</text>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from 'vuex';
	// #ifdef APP-PLUS
	import aliyunPushHelper, {
		androidNoticeChannelId
	} from '@/common/aliPush.js';
	// #endif
	export default {
		data() {
			return {
				// 弹窗标识
				FLAG: 'NoticeSettingTip',
				// 弹窗关闭记录的过期时间为2个小时，转换为毫秒【弹窗关闭之后2小时内不在弹出】
				expireTime: 2 * 60 * 60 * 1000,
				// 定时器
				timer: null,
			}
		},
		computed: {
			...mapState(['sysPermissionTip', 'noticeSettingPopupShow']),
		},
		watch: {
			// 监听弹窗状态
			// 首次进入【APP的系统权限使用说明sysPermissionTip】展示完成之后在进行通知的弹窗提示
			// sysPermissionTip: {
			// 	immediate: true,
			// 	handler(newVal, oldVal) {
			// 		if (!newVal) {
			// 			this.timer = setTimeout(() => {
			// 				this.judgeOpen()
			// 			}, 1000)
			// 		}
			// 	}
			// },
		},
		mounted() {
			// 已同意【APP的系统权限使用说明sysPermissionTip】之后再弹出通知设置弹窗
			// if (!this.sysPermissionTip) {
			this.timer = setTimeout(() => {
				this.judgeOpen()
			}, 1000)
			// }
		},
		beforeDestroy() {
			console.log("销毁通知设置弹窗")
			if (this.timer) {
				clearTimeout(this.timer)
				this.timer = null
			}
		},
		methods: {
			...mapMutations(['SET_NOTICE_SETTING_POPUP_SHOW']),
			// 打开 弹窗
			// 1、已打开通知的状态下不执行弹窗。 2、距离上次关闭弹窗的时间不足2小时时不执行弹窗
			judgeOpen() {
				// 检查关闭弹窗的时间是否已过期
				const expiration = uni.getStorageSync(this.FLAG);
				if (expiration && Date.now() < expiration) {
					// 数据未过期，不需要弹窗
					return
				}
				let isAuthorized = this.checkNoticePermission()
				if (!isAuthorized) {
					this.SET_NOTICE_SETTING_POPUP_SHOW(true)
				} else {
					this.SET_NOTICE_SETTING_POPUP_SHOW(false)
				}
			},
			// 开启
			onOpen() {
				// #ifdef APP-PLUS
				if (plus.os.name == 'Android') {
					let noticeEnabled = aliyunPushHelper.isNotificationEnabled()
					if (!noticeEnabled) {
						// 通知总开关
						aliyunPushHelper.goNotificationSettings()
					} else {
						// 通知渠道开关
						let noticeChannelEnabled = aliyunPushHelper.isNotificationEnabled(androidNoticeChannelId)
						if (!noticeChannelEnabled) {
							aliyunPushHelper.goNotificationSettings(androidNoticeChannelId)
						}
					}
				} else if (plus.os.name == 'iOS') {
					var app = plus.ios.invoke('UIApplication', 'sharedApplication');
					var setting = plus.ios.invoke('NSURL', 'URLWithString:', 'app-settings:');
					plus.ios.invoke(app, 'openURL:', setting);
					plus.ios.deleteObject(setting);
					plus.ios.deleteObject(app);
				}
				// #endif

				this.onClose()
			},
			// 关闭
			onClose() {
				this.setNoticeSettingPopupShow(false)

				// 缓存关闭弹窗的时间
				uni.setStorage({
					key: this.FLAG,
					data: Date.now() + this.expireTime
				});
			},

			// 检查通知权限
			// true-已授权  false-未授权
			checkNoticePermission() {
				// #ifdef APP-PLUS  
				if (plus.os.name == 'Android') { // 判断是Android
					let noticeEnabled = aliyunPushHelper.isNotificationEnabled()
					let noticeChannelEnabled = aliyunPushHelper.isNotificationEnabled(androidNoticeChannelId)
					if (!noticeEnabled || !noticeChannelEnabled) {
						return false
					}
					return true
				} else if (plus.os.name == 'iOS') { // 判断是IOS
					var isOn = undefined;
					var types = 0;
					var app = plus.ios.invoke('UIApplication', 'sharedApplication');
					var settings = plus.ios.invoke(app, 'currentUserNotificationSettings');
					if (settings) {
						types = settings.plusGetAttribute('types');
						plus.ios.deleteObject(settings);
					} else {
						types = plus.ios.invoke(app, 'enabledRemoteNotificationTypes');
					}
					plus.ios.deleteObject(app);
					isOn = (0 != types);
					return isOn
				}
				// #endif  
			}
		}
	}
</script>

<style>
	.row {
		display: flex;
		flex-direction: row;
	}

	.col {
		display: flex;
		flex-direction: column;
	}

	.popup-content {
		width: 750rpx;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.container {
		width: 560rpx;
		height: 710rpx;
		margin-bottom: 50rpx;
		justify-content: flex-end;
		align-items: center;
		position: relative;
	}

	.bgimg {
		width: 560rpx;
		height: 710rpx;
		position: absolute;
		top: 0;
		left: 0;
	}

	.open-btn {
		z-index: 5;
		width: 480rpx;
		height: 80rpx;
		background: #6C68F0;
		border-radius: 10rpx;
		align-items: center;
		justify-content: center;
	}

	.open-btn-text {
		font-size: 30rpx;
		font-weight: bold;
		color: #FFFFFF;
	}

	.no-btn {
		z-index: 5;
		width: 480rpx;
		height: 80rpx;
		margin-top: 20rpx;
		margin-bottom: 40rpx;
		background: #FFFFFF;
		border-radius: 6px;
		align-items: center;
		justify-content: center;
	}

	.no-btn-text {
		font-size: 30rpx;
		font-weight: bold;
		color: #6C68F0;
	}
</style>