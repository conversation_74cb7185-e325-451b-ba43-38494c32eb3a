<template>
	<view class="flex-col wrap-time">
		<text class="flex-row wrap-time__title">选择上门时间</text>
		<view class="flex-row wrap-time__content">
			<scroll-view scroll-y class="flex-col wrap-time__content__left-list">
				<view
					class="flex-row wrap-time__content__left-list__cell"
					:class="[index == dateIndex ? 'wrap-time__content__left-list__cell--active animate__animated animate__bounceIn animate__faster' : '']"
					v-for="(item, index) in dates"
					:key="index"
					@click="onClickDate(index, item)"
				>
					{{ item.value }}
				</view>
			</scroll-view>
			<scroll-view scroll-y class="flex-col wrap-time__content__right-list">
				<view
					class="flex-row wrap-time__content__right-list__cell"
					:class="[index == timeIndex ? 'wrap-time__content__right-list__cell--active animate__animated animate__bounceIn animate__faster' : '', item.disabled ? 'disable' : '']"
					v-for="(item, index) in times"
					:key="index"
					@click="onClickTime(index, item)"
				>
					{{ item.value }}
				</view>
			</scroll-view>
		</view>
		<view class="flex-row wrap-time__bottom">
			<view class="flex-row wrap-time__bottom__cancel" @click="onCancel">取消</view>
			<view class="flex-row wrap-time__bottom__confirm" @click="onConfirm">确定</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'popup-time',
	props: {
		show: {
			type: Boolean,
			default: false
		},
		value: {
			type: Object,
			default() {
				return {
					provinceId: '',
					cityId: '',
					areaId: ''
				};
			}
		}
	},
	watch: {
		show: {
			handler(newVal, oldVal) {
				// #ifdef MP
				if (newVal) {
					this.init();
				}
				// #endif
			},
			immediate: true
		}
	},
	data() {
		return {
			dates: [],// 展示的日期
			timesRaw:[],// 原始时间数据
			times: [],// 展示的是假
			dateIndex: 0,
			timeIndex: 0
		};
	},
	created() {
		this.init();
	},
	methods: {
		init() {
			uni.$u.http
				.get('/common/getCommonWordList')
				.then((res) => {
					this.dates = res.day_arr;
					this.timesRaw = res.day_time;
					this.checkTimeAvailability(this.dates?.[0].key);
				})
				.catch((err) => {});
		},
		// 检查每个时间段是否可用
		checkTimeAvailability(timestamp) {
			const checkDate = new Date(timestamp * 1000); // 将时间戳转换为 Date 对象
			const checkDateOnly = new Date(checkDate.getFullYear(), checkDate.getMonth(), checkDate.getDate()); // 只保留日期部分
			const currentDate = new Date();
			const currentDateOnly = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate()); // 只保留当前日期部分

			const currentHour = currentDate.getHours();
			const currentMinute = currentDate.getMinutes();
			const currentTotalMinutes = currentHour * 60 + currentMinute; // 当前时间的总分钟数

			let minTime = Infinity;
			let maxTime = -Infinity;

			// 遍历 times 数组，计算最早和最晚时间，并设置各时间段的可用性
			this.timesRaw.forEach((time) => {
				if (time.value !== '全天均可') {
					// 分割时间段并转换为分钟
					const [start, end] = time.value.split('-').map((t) => {
						const [hour, minute] = t.split(':').map(Number);
						return hour * 60 + minute;
					});

					// 更新最早和最晚的时间
					if (start < minTime) minTime = start;
					if (end > maxTime) maxTime = end;

					// 根据日期比较结果设置时间可用性
					if (checkDateOnly > currentDateOnly) {
						// 如果日期大于今天，所有时间可用
						time.disabled = false;
					} else if (checkDateOnly < currentDateOnly) {
						// 如果日期小于今天，所有时间不可用
						time.disabled = true;
					} else {
						// 如果日期等于今天，判断时间段是否可用
						time.disabled = !(currentTotalMinutes < end);
					}
				}
			});

			// 判断全天的可用性
			const isWithinDaytime = currentTotalMinutes >= minTime && currentTotalMinutes < maxTime;
			this.timesRaw.forEach((time) => {
				if (time.value === '全天均可') {
					// 如果当前时间不在最早和最晚时间段之间，全天不可用
					time.disabled = !isWithinDaytime;
				}
			});
			this.times = this.timesRaw.filter(e=>!e.disabled)
		},
		onClickDate(index, item) {
			this.dateIndex = index;
			this.timeIndex = 0;
			this.checkTimeAvailability(item.key);
		},
		onClickTime(index, item) {
			this.timeIndex = index;
		},
		onCancel() {
			this.$emit('cancel');
		},
		onConfirm() {
			let date = this.dates[this.dateIndex];
			let time = this.times[this.timeIndex];
			this.$emit('confirm', { date: { key: date.key, value: date.value }, time: { key: time.key, value: time.value } });
		}
	}
};
</script>

<style lang="scss" scoped>
.wrap-time {
	width: 750rpx;
	background-color: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
	align-items: center;
	justify-content: center;
	&__title {
		height: 110rpx;
		line-height: 110rpx;
		font-weight: bold;
		font-size: 36rpx;
		color: #343434;
		text-align: center;
	}

	&__content {
		width: 100%;
		height: 700rpx;
		border-top: 5px solid #f8f8f8;
		&__left-list {
			width: 50%;
			background-color: #f8f8f8;
			&__cell {
				flex: 1;
				height: 110rpx;
				font-size: 30rpx;
				color: #333333;
				align-items: center;
				justify-content: center;
				&--active {
					font-weight: 600;
					color: #444BF1;
					background: #ffffff;
				}
			}
		}
		&__right-list {
			width: 50%;
			&:last-child {
				margin-bottom: 30rpx;
			}
			&__cell {
				flex: 1;
				height: 66rpx;
				margin: 30rpx 30rpx 0 30rpx;
				border-radius: 10rpx;
				border: 1px solid #e6e6e6;
				font-weight: 500;
				font-size: 30rpx;
				color: #333333;
				align-items: center;
				justify-content: center;

				&--active {
					color: #444BF1;
					background: #f6f5ff;
					border: 0.8px solid #444BF1;
				}
			}
		}
	}
	&__bottom {
		width: 100%;
		height: 120rpx;
		padding: 0 30rpx;
		background: #ffffff;
		box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
		align-items: center;
		justify-content: space-between;
		&__cancel {
			width: 330rpx;
			height: 90rpx;
			border-radius: 45rpx;
			border: 0.8px solid #cccccc;
			font-weight: bold;
			font-size: 30rpx;
			color: #999999;
			align-items: center;
			justify-content: center;
		}
		&__confirm {
			width: 330rpx;
			height: 90rpx;
			background: #444BF1;
			border-radius: 45rpx;
			font-weight: bold;
			font-size: 30rpx;
			color: #ffffff;
			align-items: center;
			justify-content: center;
		}
	}
}
</style>
