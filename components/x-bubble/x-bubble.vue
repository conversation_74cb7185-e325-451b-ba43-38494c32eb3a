<template>
	<view>
		<!-- 遮罩层 -->
		<view v-if="visible && showMask" class="popover-mask" :style="{ backgroundColor: `rgba(0,0,0,${maskOpacity})` }" @click="close"></view>

		<!-- 弹窗本体 -->
		<view v-if="visible" class="popover-content" :style="contentStyle" ref="popoverContent">
			<slot></slot>
			<view class="popover-arrow" :style="arrowStyle"></view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'AccuratePopover',
	props: {
		target: {
			type: String,
			required: true
		},
		visible: Boolean,
		placement: {
			type: String,
			default: 'auto',
			validator: (v) => ['auto', 'top', 'bottom', 'left', 'right'].includes(v)
		},
		align: {
			type: String,
			default: 'center',
			validator: (v) => ['start', 'center', 'end'].includes(v)
		},
		popoverWidth: [Number, String],
		popoverHeight: [Number, String],
		maskOpacity: {
			type: Number,
			default: 0.3
		},
		showMask: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			contentStyle: {
				left: '-9999px',
				top: '0px',
				opacity: 0
			},
			arrowStyle: {}
		};
	},
	watch: {
		visible(val) {
			if (val) {
				this.$nextTick(() => {
					setTimeout(() => this.calculatePosition(), 30);
				});
			}
		}
	},
	methods: {
		close() {
			this.$emit('close');
		},

		async calculatePosition() {
			try {
				const [targetRect, rawContentRect] = await Promise.all([this.getBoundingRect(this.target), this.getBoundingRect('.popover-content', !(this.popoverWidth && this.popoverHeight))]);

				if (!targetRect || !rawContentRect) return;

				const contentRect = { ...rawContentRect };
				if (this.popoverWidth) contentRect.width = parseFloat(this.popoverWidth);
				if (this.popoverHeight) contentRect.height = parseFloat(this.popoverHeight);

				const systemInfo = uni.getSystemInfoSync();
				const placement = this.determinePlacement(targetRect, contentRect, systemInfo);

				const { position, arrowPosition } = this.calculateStyles(targetRect, contentRect, placement, systemInfo);

				this.contentStyle = {
					...position,
					width: this.popoverWidth ? this.popoverWidth + this.unit(this.popoverWidth) : 'auto',
					height: this.popoverHeight ? this.popoverHeight + this.unit(this.popoverHeight) : 'auto',
					opacity: 1
				};
				this.arrowStyle = arrowPosition;
			} catch (e) {
				console.error('Popover position error:', e);
			}
		},

		determinePlacement(targetRect, contentRect, systemInfo) {
			if (this.placement !== 'auto') return this.placement;

			const spaceTop = targetRect.top - contentRect.height - 20;
			const spaceBottom = systemInfo.windowHeight - targetRect.bottom - contentRect.height - 20;
			const spaceLeft = targetRect.left - contentRect.width - 20;
			const spaceRight = systemInfo.windowWidth - targetRect.right - contentRect.width - 20;

			const best = [
				{ dir: 'top', space: spaceTop },
				{ dir: 'bottom', space: spaceBottom },
				{ dir: 'left', space: spaceLeft },
				{ dir: 'right', space: spaceRight }
			].reduce((a, b) => (b.space > a.space ? b : a));

			return best.space > 0 ? best.dir : 'bottom';
		},

		calculateStyles(targetRect, contentRect, placement, systemInfo) {
			const spacing = 10;
			const arrowSize = 8;
			const position = {};
			const arrowPosition = {
				position: 'absolute',
				width: 0,
				height: 0,
				border: `${arrowSize}px solid transparent`
			};

			const centerX = targetRect.left + targetRect.width / 2;
			const centerY = targetRect.top + targetRect.height / 2;

			switch (placement) {
				case 'top':
				case 'bottom': {
					let left;
					const maxLeft = systemInfo.windowWidth - contentRect.width - 10;
					if (this.align === 'start') {
						left = Math.max(10, Math.min(targetRect.left, maxLeft));
					} else if (this.align === 'end') {
						left = Math.max(10, Math.min(targetRect.right - contentRect.width, maxLeft));
					} else {
						left = centerX - contentRect.width / 2;
						left = Math.max(10, Math.min(left, maxLeft));
					}
					position.left = `${left}px`;

					let arrowLeft = centerX - left;
					arrowLeft = Math.min(Math.max(arrowLeft, arrowSize), contentRect.width - arrowSize);

					if (placement === 'top') {
						position.top = `${targetRect.top - contentRect.height - spacing}px`;
						arrowPosition.bottom = `-${arrowSize * 2}px`;
						arrowPosition.left = `${arrowLeft}px`;
						arrowPosition['border-top-color'] = '#333';
					} else {
						position.top = `${targetRect.bottom + spacing}px`;
						arrowPosition.top = `-${arrowSize * 2}px`;
						arrowPosition.left = `${arrowLeft}px`;
						arrowPosition['border-bottom-color'] = '#333';
					}
					break;
				}
				case 'left':
				case 'right': {
					let top = centerY - contentRect.height / 2;
					top = Math.max(10, Math.min(top, systemInfo.windowHeight - contentRect.height - 10));
					position.top = `${top}px`;

					let arrowTop = centerY - top;
					arrowTop = Math.min(Math.max(arrowTop, arrowSize), contentRect.height - arrowSize);

					if (placement === 'left') {
						position.left = `${targetRect.left - contentRect.width - spacing}px`;
						arrowPosition.right = `-${arrowSize * 2}px`;
						arrowPosition.top = `${arrowTop}px`;
						arrowPosition['border-left-color'] = '#333';
					} else {
						position.left = `${targetRect.right + spacing}px`;
						arrowPosition.left = `-${arrowSize * 2}px`;
						arrowPosition.top = `${arrowTop}px`;
						arrowPosition['border-right-color'] = '#333';
					}
					break;
				}
			}

			position.left = `${Math.max(10, Math.min(parseFloat(position.left), systemInfo.windowWidth - contentRect.width - 10))}px`;
			position.top = `${Math.max(10, Math.min(parseFloat(position.top), systemInfo.windowHeight - contentRect.height - 10))}px`;

			return { position, arrowPosition };
		},

		getBoundingRect(selector, useDefault = false) {
			return new Promise((resolve) => {
				const query = uni.createSelectorQuery().in(this.$root);
				query
					.select(selector)
					.boundingClientRect((res) => {
						if (!res && useDefault) {
							resolve({ width: 200, height: 100 });
						} else {
							resolve(res);
						}
					})
					.exec();
			});
		},

		unit(val) {
			return typeof val === 'string' && val.includes('rpx') ? '' : 'px';
		}
	}
};
</script>

<style scoped>
.popover-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.3);
	z-index: 999;
}
.popover-content {
	position: fixed;
	background: #333;
	color: white;
	padding: 12px;
	border-radius: 6px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
	z-index: 1000;
	min-width: 120px;
	max-width: 80vw;
	opacity: 0;
	transition: opacity 0.2s;
}
.popover-arrow {
	position: absolute;
}
</style>
