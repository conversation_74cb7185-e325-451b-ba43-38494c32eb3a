<template>
	<view class="flex-col wraper" :class="[`wraper--${styleType}`]" @click.stop="click" v-if="!$u.test.isEmpty(item)">
		<image class="wraper__topping" :src="iImage('img_product_pin.png')" v-if="item.isPin"></image>
		<view class="flex wraper__top">
			<image class="flex wraper__top__avatar" :src="logo" mode="aspectFill"></image>
			<view class="flex-col wraper__top__right">
				<text class="wraper__top__right__name line1">{{ title }}</text>
				<view class="flex wraper__top__right__box">
					<view class="flex wraper__top__right__box__sf" :style="{ borderColor: ident.color }" v-if="!$u.test.isEmpty(ident)">
						<text :style="{ backgroundColor: ident.color }">{{ ident.name }}</text>
						<text>顾问</text>
					</view>
					<text class="wraper__top__right__box__info">{{ item.experienceDesc }} | 126人已咨询</text>
				</view>
			</view>
		</view>
		<view class="flex-col wraper__content">
			<text class="wraper__content__field1 line1">{{ serviceCategoryName }}</text>
			<text class="wraper__content__field2 line1">{{ serviceAreaName }}</text>
		</view>
		<view class="flex wraper__bottom">
			<image class="wraper__bottom__address-icon" :src="iImage('ic_provider_list_address.png')"></image>
			<text class="wraper__bottom__address-name line1">{{ addressName }}</text>
			<view class="flex wraper__bottom__btn">咨询</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'list-provider-item',
	props: {
		// 列表条目数据
		item: {
			type: Object,
			default: () => {}
		},
		// 样式类型控制，支持 flat / card / rounded
		styleType: {
			type: String,
			default: 'rounded',
			validator: (val) => ['flat', 'card', 'rounded'].includes(val)
		}
	},
	data() {
		return {};
	},
	computed: {
		title() {
			const map = {
				[this.cons.PROVIDER_TYPE_PERSONAL]: this.item.realName,
				[this.cons.PROVIDER_TYPE_COMPANY]: this.item.corpName
			};
			return map[this.item.providerType];
		},
		logo() {
			const map = {
				[this.cons.PROVIDER_TYPE_PERSONAL]: this.item.avatarUrl,
				[this.cons.PROVIDER_TYPE_COMPANY]: this.item.brandLogoUrl
			};
			return map[this.item.providerType];
		},
		ident() {
			const map = {
				[this.cons.PROVIDER_TYPE_PERSONAL]: { name: '个', color: '#2CBDB9' },
				[this.cons.PROVIDER_TYPE_COMPANY]: { name: '企', color: '#4177FF' }
			};
			return map[this.item.providerType];
		},
		serviceCategoryName() {
			if (uni.$u.test.isEmpty(this.item.serviceCategoryNames)){
				return ''
			}
			return this.item.serviceCategoryNames.join('、');
		},
		serviceAreaName() {
			if (uni.$u.test.isEmpty(this.item.serviceRegionNames)){
				return ''
			}
			return this.item.serviceRegionNames.join('、');
		},
		addressName() {
			const map = {
				[this.cons.PROVIDER_TYPE_PERSONA]: [this.item.provinceName, this.item.cityName, this.item.districtName].join(''),
				[this.cons.PROVIDER_TYPE_COMPANY]: [this.item.provinceName, this.item.cityName, this.item.districtName, this.item.addressDetail].join('')
			};
			return map[this.item.providerType];
		}
	},
	methods: {
		click() {
			this.$emit('click', { detail: this.item });
		}
	}
};
</script>

<style lang="scss" scoped>
.wraper {
	margin: 0 30rpx;
	padding: 25rpx 20rpx;
	background: #ffffff;
	position: relative;

	// 样式类型：flat（无圆角无阴影）、card（阴影）、rounded（阴影 + 圆角）
	&--flat {
		// 不加任何装饰
	}
	&--card {
		box-shadow: 0px 3rpx 24rpx 0px rgba(222, 222, 222, 0.38);
	}
	&--rounded {
		box-shadow: 0px 3rpx 24rpx 0px rgba(222, 222, 222, 0.38);
		border-radius: 10rpx;
	}

	&__topping {
		width: 80rpx;
		height: 80rpx;
		z-index: 10;
		position: absolute;
		left: 0rpx;
		top: 0rpx;
	}

	&__top {
		align-items: center;

		&__avatar {
			width: 90rpx;
			min-width: 90rpx;
			height: 90rpx;
			background: #f3f4f6;
			border-radius: 20rpx;
			border: 0.8px solid #e0e0e0;
		}

		&__right {
			margin-left: 20rpx;
			align-items: flex-start;

			&__name {
				font-weight: 800;
				font-size: 34rpx;
				color: #333333;
			}

			&__box {
				margin-top: 10rpx;

				&__sf {
					height: 30rpx;
					background-color: #ffffff;
					border-radius: 5rpx;
					border: 2rpx solid #444bf1;
					font-weight: 400;
					font-size: 22rpx;
					color: #1e2134;
					text-align: center;
					white-space: nowrap;

					text:nth-child(1) {
						flex: 1;
						padding: 0 5rpx;
						background-color: #444bf1;
						color: #ffffff;
						font-weight: bold;
					}
					text:nth-child(2) {
						flex: 1;
						padding: 0 5rpx;
					}
				}

				&__info {
					margin-left: 20rpx;
					font-size: 24rpx;
					color: #1e2134;
					white-space: nowrap;
				}
			}
		}
	}

	&__content {
		margin-top: 20rpx;
		padding: 10rpx 20rpx;
		background: #f5f7fb;
		border-radius: 10rpx;
		border: 1rpx dashed #949bae;

		&__field1,
		&__field2 {
			font-size: 28rpx;
			color: #333333;
			line-height: 50rpx;
		}

		&__field1:before {
			content: '主营业务：';
			color: #909099;
		}
		&__field2:before {
			content: '服务地区：';
			color: #909099;
		}
	}

	&__bottom {
		margin-top: 25rpx;

		&__address-icon {
			width: 24rpx;
			height: 29rpx;
			flex-shrink: 0;
		}

		&__address-name {
			margin-left: 10rpx;
			font-weight: 400;
			font-size: 28rpx;
			color: #1e2134;
		}

		&__btn {
			width: 120rpx;
			min-width: 120rpx;
			height: 60rpx;
			margin-left: auto;
			background: #444bf1;
			color: #ffffff;
			font-size: 30rpx;
			border-radius: 30rpx;
			font-weight: bold;
			align-items: center;
			justify-content: center;
		}
	}
}
</style>
