<template>
	<view class="col popup-content">
		<view class="col wrapper">
			<view class="row wrapper__title-view">
				<text class="wrapper__title-view__title">分享好友</text>
			</view>
			<view class="row wrapper__item-wrapper">
				<view-share class="wrapper__item-wrapper__item" @share="onShare($event,'Session')">
					<image class="wrapper__item-wrapper__item__icon"
						:src="iImage('ic_pop_share_wxsession.png')" />
					<text class="wrapper__item-wrapper__item__text">微信好友</text>
				</view-share>
				<!-- <view-share class="wrapper__item-wrapper__item" @share="onShare($event,'Timeline')">
					<image class="wrapper__item-wrapper__item__icon"
						:src="iImage('ic_pop_share_wxtimeline.png')" />
					<text class="wrapper__item-wrapper__item__text">朋友圈</text>
				</view-share> -->
				<view class="wrapper__item-wrapper__item" @click="onCopyLink()">
					<image class="wrapper__item-wrapper__item__icon" :src="iImage('ic_pop_share_link.png')" />
					<text class="wrapper__item-wrapper__item__text">复制链接</text>
				</view>
				<view class="wrapper__item-wrapper__item" @click="onLike()">
					<image class="wrapper__item-wrapper__item__icon"
						:src="data.is_praise ? '/static/image/ic_pop_share_like_high.png' : '/static/image/ic_pop_share_like.png'" />
					<text class="wrapper__item-wrapper__item__text">点赞</text>
				</view>
				<view class="wrapper__item-wrapper__item" @click="onCollect()">
					<image class="wrapper__item-wrapper__item__icon"
						:src="data.is_collection ? '/static/image/ic_pop_share_collect_high.png' : '/static/image/ic_pop_share_collect.png'" />
					<text class="wrapper__item-wrapper__item__text">收藏</text>
				</view>
			</view>
		</view>
		<view class="row close" @click="onCancel">取消</view>
	</view>
</template>

<script>
	// #ifdef APP-NVUE
	import GlobalMixin from '@/mixin/mixin.js';
	// #endif
	import {
		appShare
	} from '@/common/utils.js'
	export default {
		name: "popup-share",
		// #ifdef APP-NVUE
		mixins: [GlobalMixin],
		// #endif
		props: {
			// 需要分享的内容
			shareData: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {
				// 界面数据
				data: this.shareData
			};
		},
		created() {

		},
		methods: {
			/**
			 * @param {Object} share 分享平台 【APP、小程序】
			 * @param {Object} scene
			 */
			onShare(share, scene) {
				if (share.type == 'AppShare') {
					// APP分享
					appShare({
						shareScene: scene == 'Session' ? 'WXSceneSession' : 'WXSceneTimeline',
						itemType: this.data.type,
						itemId: this.data.id,
						path: uni.$u.page()
					}, (res) => {})
				}
			},
			// 复制链接
			onCopyLink() {
				console.warn(this.data)
				uni.showLoading({
					title: '获取中...',
					mask: true
				})
				uni.$u.http.post('/wechat/getShareLink', {
					from_scene: '', // 分享场景 参考【constant.kShare_Scene_xx】
					from_id: this.data.id, // 分享的项目编号
					task_type: this.data.type, // 分享的项目类型
				}).then(res => {
					uni.hideLoading()
					console.warn(res)
					uni.setClipboardData({
						data: res.data.link,
						success: function() {
							uni.showToast({
								title: "链接复制成功",
								icon: 'none'
							})
						}
					})
				}).catch(err => {
					uni.hideLoading()
				})
			},
			// 点赞
			onLike() {
				uni.$u.http.post('/user/setPraise', {
					type: this.data.type,
					id: this.data.id
				}).then(res => {
					if (res.data.is_praise) {
						this.data.is_praise = 1
						this.data.praise_num += 1
					} else {
						this.data.is_praise = 0
						this.data.praise_num -= 1
					}
					let element = this.items.find(e => e.type == 'Like')
					if (element) {
						element.isHigh = this.data.is_praise
					}
				}).catch(err => {})
			},
			// 收藏
			onCollect() {
				uni.$u.http.post('/user/setCollection', {
					type: this.data.type,
					id: this.data.id
				}).then(res => {
					if (res.data.is_collect) {
						this.data.is_collection = 1
						this.data.collection_num += 1
					} else {
						this.data.is_collection = 0
						this.data.collection_num -= 1
					}
					let element = this.items.find(e => e.type == 'Collect')
					if (element) {
						element.isHigh = this.data.is_praise
					}
				}).catch(err => {})
			},
			// 取消
			onCancel() {
				this.$emit('cancel');
			}
		}
	}
</script>

<style lang="scss">
	.row {
		display: flex;
		flex-direction: row;
	}

	.col {
		display: flex;
		flex-direction: column;
	}

	.popup-content {
		width: 750rpx !important;
		align-items: center;
		flex-direction: column;
	}

	.wrapper {
		width: 690rpx;
		padding: 30rpx;
		background: #FFFFFF;
		border-radius: 15rpx;

		&__title-view {
			&__title {
				font-size: 34rpx;
				font-weight: 800;
				color: #333333;
			}
		}

		&__item-wrapper {
			flex: 1;
			margin-top: 30rpx;
			align-items: center;
			justify-content: space-between;


			&__item {
				align-items: center;
				justify-content: center;

				&__icon {
					width: 90rpx;
					height: 90rpx;
				}

				&__text {
					margin-top: 20rpx;
					font-size: 28rpx;
					font-weight: 500;
					color: #333333;
				}
			}
		}

	}

	.close {
		width: 690rpx;
		height: 90rpx;
		margin: 20rpx 0 30rpx 0;
		background: #FFFFFF;
		border-radius: 15rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #333333;
		align-items: center;
		justify-content: center;
	}
</style>