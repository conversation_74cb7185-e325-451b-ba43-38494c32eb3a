<template>
	<u-popup :show="publishPopupShow" mode="bottom" bgColor="transparent" zIndex="999998" :overlay-opacity="0.7" @close="onClose">
		<view class="flex-col publish-wrap" @click="onClose">
			<view class="flex publish-wrap__content">
				<view class="flex-col publish-wrap__content__item" v-for="(item, index) in list" :key="index" @click="onItemClick(item)">
					<image class="publish-wrap__content__item__ic" :src="iImage(item.icon)"></image>
					<text class="publish-wrap__content__item__title">{{ item.title }}</text>
					<text class="publish-wrap__content__item__subtitle">{{ item.subtitle }}</text>
					<view class="flex publish-wrap__content__item__btn">去发布</view>
				</view>
			</view>
			<image class="publish-wrap__close" :src="iImage('ic_close_w.png')" mode="aspectFit" />
		</view>
	</u-popup>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
export default {
	props: {
		// 弹窗所在的页面
		inPage: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			list: []
		};
	},
	computed: {
		...mapState(['publishPopupShow'])
	},
	watch: {
		// 监听弹窗的显示状态
		publishPopupShow: {
			handler(newVal) {
				if (newVal && uni.$u.page() == this.inPage) {
					this.show = true;
				} else {
					this.show = false;
				}
			},
			immediate: true
		}
	},
	created() {
		this.list = [
			{ type: this.cons.DEMAND_ORIGIN_SUBTYPE_NORMAL, icon: 'ic_publish_demand.png', title: '发布需求', subtitle: '急速匹配办理' },
			{ type: this.cons.DEMAND_ORIGIN_SUBTYPE_QUALIFICATION, icon: 'ic_publish_credential.png', title: '资质服务', subtitle: '资质求购/转让' },
			{ type: this.cons.DEMAND_ORIGIN_SUBTYPE_DECLARATION, icon: 'ic_publish_policy.png', title: '政策申报', subtitle: '便捷 省事 省力' }
		];
	},
	methods: {
		...mapMutations(['SET_PUBLISH_POPUP_SHOW', 'SET_PUBLISH_FORM_POPUP']),

		// 模块点击
		onItemClick(e) {
			this.onClose();
			this.SET_PUBLISH_FORM_POPUP({ show: true, type: e.type }); 
		},
		onClose() {
			this.SET_PUBLISH_POPUP_SHOW(false);
		}
	}
};
</script>

<style lang="scss">
.publish-wrap {
	width: 750rpx;
	padding: 40rpx 0 160rpx 0;
	background: transparent;
	justify-content: center;
	align-items: center;

	&__content {
		margin-bottom: 180rpx;
		justify-content: center;

		&__item {
			width: 211rpx;
			height: 277rpx;
			background: linear-gradient(0deg, #e8e9ff 0%, #ffffff 100%);
			border-radius: 20rpx;
			border: 0.8px solid #8286f0;
			justify-content: center;
			align-items: center;

			&__ic {
				width: 66rpx;
				height: 66rpx;
			}

			&__title {
				margin-top: 20rpx;
				font-weight: 600;
				font-size: 30rpx;
				color: #1e2134;
			}

			&__subtitle {
				font-weight: 400;
				font-size: 24rpx;
				color: #5f6069;
				line-height: 36rpx;
			}

			&__btn {
				width: 160rpx;
				height: 46rpx;
				margin-top: 20rpx;
				background: linear-gradient(180deg, #f24593 0%, #5551f6 100%);
				border-radius: 23px;
				justify-content: center;
				font-weight: 500;
				font-size: 24rpx;
				color: #ffffff;
			}
		}
	}

	&__content > view:not(:last-child) {
		margin-right: 20rpx;
	}

	&__close {
		width: 68rpx;
		height: 68rpx;
	}
}
</style>
