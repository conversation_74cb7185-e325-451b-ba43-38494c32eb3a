<template>
	<view>
		<!-- 使用 x-form-select 替换原有的 input 标签 -->

		<!-- 服务信息 -->
		<view class="flex-col section-wrap">
			<text class="section-wrap__title">服务信息</text>

			<!-- 主营业务 - 原来的写法 -->
			<!-- <view class="flex section-wrap__normal-cell" @click="onShowSelectPopup('service')">
				<text>主营业务</text>
				<input :value="serviceCategoryValue" placeholder="请选择主营业务(多选)" disabled />
				<image class="section-wrap__normal-cell__arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
			</view> -->

			<!-- 主营业务 - 使用 x-form-select 替换 input（文本模式） -->
			<view class="flex section-wrap__normal-cell"
				:class="{ verify: formVerify && $u.test.isEmpty(serviceCategoryValue) }"
				@click="onShowSelectPopup('service')">
				<text>主营业务</text>
				<x-form-select
					:value="form.serviceCategory"
					:options="serviceCategoryOptions"
					placeholder="请选择主营业务(多选)"
					display-mode="text"
					:show-full-path="false"
					:props="{ value: 'id', label: 'name', children: 'children' }"
				/>
				<image class="section-wrap__normal-cell__arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
			</view>

			<!-- 主营业务 - 标签模式示例 -->
			<view class="flex section-wrap__normal-cell"
				:class="{ verify: formVerify && $u.test.isEmpty(serviceCategoryValue) }"
				@click="onShowSelectPopup('service')">
				<text>主营业务</text>
				<x-form-select
					:value="form.serviceCategory"
					:options="serviceCategoryOptions"
					placeholder="请选择主营业务"
					display-mode="tag"
					:max-count="3"
					:show-full-path="false"
					:props="{ value: 'id', label: 'name', children: 'children' }"
					@delete="handleServiceCategoryDelete"
				/>
				<image class="section-wrap__normal-cell__arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
			</view>

			<!-- 服务地区 - 原来的写法 -->
			<!-- <view class="flex section-wrap__normal-cell" @click="onShowSelectPopup('serviceRegion')">
				<text>服务地区</text>
				<input :value="serviceRegionValue" placeholder="请选择服务地区" disabled />
				<image class="section-wrap__normal-cell__arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
			</view> -->

			<!-- 服务地区 - 使用 x-form-select 替换 input（标签模式） -->
			<view class="flex section-wrap__normal-cell"
				:class="{ verify: formVerify && $u.test.isEmpty(serviceRegionValue) }"
				@click="onShowSelectPopup('serviceRegion')">
				<text>服务地区</text>
				<x-form-select
					:value="form.serviceRegions"
					placeholder="请选择服务地区"
					display-mode="tag"
					:max-count="6"
					:allow-delete="true"
					@delete="handleRegionDelete"
				/>
				<image class="section-wrap__normal-cell__arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
			</view>

			<!-- 所在城市 - 原来的写法 -->
			<!-- <view class="flex section-wrap__normal-cell" @click="onShowSelectPopup('city')">
				<text>所在城市</text>
				<input :value="addressValue" placeholder="请选择所在城市" disabled />
				<image class="section-wrap__normal-cell__arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
			</view> -->

			<!-- 所在城市 - 使用 x-form-select 替换 input -->
			<view class="flex section-wrap__normal-cell"
				:class="{ verify: formVerify && $u.test.isEmpty(addressValue) }"
				@click="onShowSelectPopup('city')">
				<text>所在城市</text>
				<x-form-select
					:value="form.address"
					placeholder="请选择所在城市"
					type="city"
				/>
				<image class="section-wrap__normal-cell__arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
			</view>
		</view>
	</view>
</template>

<script>
import { handleTree } from '@/common/tool.js';

export default {
	name: 'ProfileUsageExample',
	data() {
		return {
			// 表单数据
			form: {
				// 服务类目 - 支持包含完整路径信息的对象格式
				serviceCategory: {
					"ident": "cascader",
					"value": [
						{
							"id": 123,
							"name": "债权债务纠纷处理",
							"path": [33, 123],
							"pathLabels": ["法律顾问", "债权债务纠纷处理"],
							"level": 1
						},
						{
							"id": 124,
							"name": "知识产权法律保护",
							"path": [33, 124],
							"pathLabels": ["法律顾问", "知识产权法律保护"],
							"level": 1
						}
					],
					"paths": [[33, 123], [33, 124]]
				},
				// 也支持直接传入末级ID数组
				// serviceCategory: [75, 77],
				// 或者传入完整路径数组（二维数组）
				// serviceCategory: [[32, 116], [32, 114]],
				// 或者传入完整的对象数组
				// serviceCategory: [
				//   { id: 75, name: 'UI设计', path: [1, 75], pathLabels: ['设计服务', 'UI设计'] },
				//   { id: 77, name: '前端开发', path: [2, 77], pathLabels: ['开发服务', '前端开发'] }
				// ],
				
				// 服务区域
				serviceRegions: [
					{
						id: 1,
						name: '北京市',
						child: [
							{ id: 11, name: '朝阳区' },
							{ id: 12, name: '海淀区' }
						]
					}
				],
				
				// 所在地址 - 也可以使用统一的级联数据格式
				address: {
					"ident": "cascader",
					"value": [
						{
							"id": 110101,
							"name": "东城区",
							"path": [110000, 110100, 110101],
							"pathLabels": ["北京市", "北京市", "东城区"],
							"level": 2
						}
					],
					"paths": [[110000, 110100, 110101]]
				}
			},
			
			// 服务类目选项数据
			serviceCategoryOptions: [],
			
			// 表单验证
			formVerify: false
		};
	},
	
	computed: {
		// 地址显示文本（用于表单验证）
		addressValue() {
			if (!this.form.address) return '';
			// 现在使用统一的级联数据格式，可以直接检查是否有值
			if (typeof this.form.address === 'object' && this.form.address.value && this.form.address.value.length > 0) {
				return this.form.address.value[0].name || '';
			}
			return '';
		},

		// 服务区域显示文本（保留原有的computed，用于验证）
		serviceRegionValue() {
			if (!Array.isArray(this.form.serviceRegions)) return '';
			const list = this.form.serviceRegions.flatMap(e => e.child || []);
			return list.map(e => e.name).join('、');
		},

		// 服务类型显示文本（保留原有的computed，用于验证）
		serviceCategoryValue() {
			if (!Array.isArray(this.form.serviceCategory)) return '';
			// 如果是ID数组，需要从选项中查找对应的名称
			if (this.form.serviceCategory.length > 0 && typeof this.form.serviceCategory[0] === 'number') {
				return this.getServiceCategoryNames(this.form.serviceCategory).join('、');
			}
			// 如果是对象数组，直接取名称
			return this.form.serviceCategory.map(item => item.name || item.label).join('、');
		}
	},
	
	created() {
		this.loadServiceCategories();
	},
	
	methods: {
		// 加载服务类目数据
		loadServiceCategories() {
			uni.$u.http.get('/category/common/list-by-type', {
				params: {
					type: this.cons.CATEGORY_TYPE_SERVICE
				}
			}).then((res) => {
				this.serviceCategoryOptions = handleTree(res);
			});
		},

		// 显示选择器弹窗（保持原有的方法）
		onShowSelectPopup(type) {
			switch (type) {
				case 'service':
					// 显示服务类目选择器
					this.showServiceCategorySelector();
					break;
				case 'serviceRegion':
					// 显示服务地区选择器
					this.showServiceRegionSelector();
					break;
				case 'city':
					// 显示城市选择器
					this.showCitySelector();
					break;
			}
		},

		// 显示服务类目选择器
		showServiceCategorySelector() {
			// 这里调用你原有的级联选择器逻辑
			this.cascaderPopup = {
				show: true,
				level: 2,
				maxNum: 3,
				list: this.serviceCategoryOptions,
				value: this.form.serviceCategory,
				complete: (res) => {
					this.form.serviceCategory = res;
				}
			};
		},

		// 显示服务地区选择器
		showServiceRegionSelector() {
			// 这里调用你原有的多选城市选择器逻辑
			this.multipleCityPopup = {
				show: true,
				maxNum: 6,
				value: this.form.serviceRegions,
				complete: (res) => {
					this.form.serviceRegions = res.detail;
				}
			};
		},

		// 显示城市选择器
		showCitySelector() {
			// 这里调用你原有的城市选择器逻辑
			this.cityPopup = {
				show: true,
				level: 3,
				value: this.form.address,
				complete: (res) => {
					this.form.address = res;
				}
			};
		},

		// 处理服务类目删除
		handleServiceCategoryDelete(result) {
			console.log('删除服务类目:', result);
			// result.value 是删除后的新值，可以直接赋值
			this.form.serviceCategory = result.value;

			// 如果需要调用接口更新数据，可以在这里处理
			// this.updateServiceCategory(result.value);
		},

		// 处理服务地区删除
		handleRegionDelete(result) {
			console.log('删除服务地区:', result);
			// result.value 是删除后的新值，与popup-cascader的回调格式一致
			this.form.serviceRegions = result.value;

			// 如果需要调用接口更新数据，可以在这里处理
			// this.updateServiceRegion(result.value);
		},
		
		// 根据ID数组获取服务类目名称
		getServiceCategoryNames(ids) {
			const names = [];
			const findNames = (options, targetIds) => {
				for (const option of options) {
					if (targetIds.includes(option.id)) {
						names.push(option.name);
					}
					if (option.children && option.children.length > 0) {
						findNames(option.children, targetIds);
					}
				}
			};
			findNames(this.serviceCategoryOptions, ids);
			return names;
		},
		
		// 表单提交前的数据处理
		prepareSubmitData() {
			// 处理服务类目数据，转换为后端需要的格式
			let serviceCategoryIds = [];
			if (Array.isArray(this.form.serviceCategory)) {
				if (this.form.serviceCategory.length > 0) {
					if (typeof this.form.serviceCategory[0] === 'number') {
						// 如果是ID数组，直接使用
						serviceCategoryIds = this.form.serviceCategory;
					} else if (typeof this.form.serviceCategory[0] === 'object') {
						// 如果是对象数组，提取ID
						serviceCategoryIds = this.form.serviceCategory.map(item => item.id || item.value);
					}
				}
			}
			
			// 处理服务地区数据
			const serviceRegionCodes = this.form.serviceRegions.flatMap(region => 
				(region.child || []).map(city => city.id)
			);
			
			// 返回提交数据
			return {
				// 服务类目ID数组
				serviceCategoryIds,
				// 服务地区代码数组
				serviceRegionCodes,
				// 省市区代码
				provinceCode: this.form.address.provinceId,
				cityCode: this.form.address.cityId,
				districtCode: this.form.address.districtId,
				// 其他表单数据...
			};
		},
		
		// 数据回显处理（从后端获取数据后）
		handleDataEcho(profileData) {
			// 处理服务类目回显
			if (profileData.serviceCategoryList) {
				// 方式1: 直接使用末级ID数组（推荐）
				const categoryTree = handleTree(profileData.serviceCategoryList);
				this.form.serviceCategory = categoryTree.flatMap(first => 
					(first.child || []).map(second => second.id)
				);
				
				// 方式2: 使用完整对象数组
				// this.form.serviceCategory = categoryTree.flatMap(first =>
				//   (first.child || []).map(second => ({
				//     id: second.id,
				//     name: second.name,
				//     path: [first.id, second.id],
				//     pathLabels: [first.name, second.name]
				//   }))
				// );
			}
			
			// 处理服务地区回显
			if (profileData.serviceRegionList) {
				this.form.serviceRegions = handleTree(profileData.serviceRegionList, 'id', 'parentId', 'child', 1);
			}
			
			// 处理所在地址回显
			this.form.address = {
				provinceId: profileData.provinceCode,
				provinceName: profileData.provinceName,
				cityId: profileData.cityCode,
				cityName: profileData.cityName,
				districtId: profileData.districtCode,
				districtName: profileData.districtName
			};
		}
	}
};
</script>

<style lang="scss" scoped>
.section-wrap {
	padding: 0 30rpx;
	margin-top: 10rpx;
	background-color: #ffffff;

	&__title {
		margin-top: 30rpx;
		font-weight: bold;
		font-size: 36rpx;
		color: #1e2134;
	}

	&__normal-cell {
		padding: 30rpx 0;
		align-items: flex-start;

		> text {
			font-weight: 500;
			font-size: 30rpx;
			color: #1e2134;
			white-space: nowrap;
			margin-top: 20rpx; // 与选择器对齐

			&:before {
				content: '*';
				color: red;
			}
		}
	}
}

.form-select-wrapper {
	flex: 1;
	margin-left: 45rpx;
}

/* 校验错误状态 */
.verify {
	animation: shake 0.2s ease-in-out 1;

	> text:first-child {
		font-weight: bold;
		font-size: 30rpx;
		color: red;
	}
}

@keyframes shake {
	0%, 100% {
		transform: translateX(0);
	}
	25% {
		transform: translateX(-8px);
	}
	50% {
		transform: translateX(8px);
	}
	75% {
		transform: translateX(-8px);
	}
}

/* 自定义 x-form-select 样式示例 */
.custom-height-select {
	.x-form-select__input {
		border: 2rpx solid #007aff;
		border-radius: 12rpx;
		background-color: #f0f8ff;
	}

	.x-form-select__input-field {
		font-size: 32rpx;
		font-weight: 500;
	}
}

.custom-form-select {
	.x-form-select__tag {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		border: none;
		border-radius: 25rpx;
		font-weight: 500;
	}

	.x-form-select__tag-delete {
		background-color: rgba(255, 255, 255, 0.3);

		&:hover {
			background-color: rgba(255, 255, 255, 0.5);
		}

		.x-form-select__tag-delete-line {
			background-color: white;
		}
	}
}
</style>
