<template>
	<view class="x-form-select" :class="customClass">
		<!-- 文本显示模式 -->
		<view
			v-if="displayMode === 'text'"
			class="x-form-select__input"
			:style="{ height: inputHeight }"
			@click="handleClick"
		>
			<input
				:value="displayText"
				:placeholder="dynamicPlaceholder"
				disabled
				class="x-form-select__input-field"
				:style="{ height: inputHeight, lineHeight: inputHeight }"
			/>
		</view>

		<!-- 标签显示模式 -->
		<view
			v-else-if="displayMode === 'tag'"
			class="x-form-select__tag-container"
			:style="{ minHeight: minHeight, gap: tagSpacing }"
			@click="handleClick"
		>
			<!-- 占位符 -->
			<text v-if="!displayItems || displayItems.length === 0" class="x-form-select__placeholder">
				{{ dynamicPlaceholder }}
			</text>

			<!-- 标签列表 -->
			<view v-else class="x-form-select__tag-content" :style="{ gap: tagSpacing }">
				<view
					class="x-form-select__tag"
					v-for="(item, index) in displayItems"
					:key="getItemKey(item, index)"
					:style="{ height: tagHeight, lineHeight: tagHeight }"
				>
					<text class="x-form-select__tag-name">{{ getItemLabel(item) }}</text>
					<view
						v-if="allowDelete"
						class="x-form-select__tag-delete"
						@click.stop="handleDeleteTag(item, index)"
					>
						<view class="x-form-select__tag-delete-line"></view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
/**
 * x-form-select 表单选择器显示组件
 * @description 用于替换表单中的input标签，专门用于显示选择器的回显文本
 * @version 1.0.0
 */
export default {
	name: 'XFormSelect',
	props: {
		// 占位符文本
		placeholder: {
			type: String,
			default: '请选择'
		},
		// 选中的值（支持多种格式）
		value: {
			type: [String, Number, Array, Object],
			default: null
		},
		// 数据源（用于回显）
		options: {
			type: Array,
			default: () => []
		},
		// 字段映射配置
		props: {
			type: Object,
			default: () => ({
				value: 'id',
				label: 'name',
				children: 'children'
			})
		},
		// 是否禁用
		disabled: {
			type: Boolean,
			default: true 
		},
		// 多选时的分隔符
		separator: {
			type: String,
			default: '、'
		},
		// 级联选择时的分隔符
		cascaderSeparator: {
			type: String,
			default: '/'
		},
		// 是否显示全路径（true: 显示完整路径，false: 只显示末级）
		showFullPath: {
			type: Boolean,
			default: false
		},
		// 显示模式：text(文本模式) | tag(标签模式)
		displayMode: {
			type: String,
			default: 'tag',
			validator: (value) => ['text', 'tag'].includes(value)
		},
		// 是否允许删除标签（仅标签模式有效）
		allowDelete: {
			type: Boolean,
			default: true
		},

		// 最大显示数量提示
		maxCount: {
			type: Number,
			default: 0,
			validator(value) {
				return value >= 0;
			}
		},
		// 自定义样式类名
		customClass: {
			type: String,
			default: ''
		},
		// 输入框高度（文本模式）
		inputHeight: {
			type: String,
			default: '60rpx'
		},
		// 标签容器最小高度（标签模式）
		minHeight: {
			type: String,
			default: '60rpx'
		},
		// 标签间距
		tagSpacing: {
			type: String,
			default: '8rpx'
		},
		// 标签高度
		tagHeight: {
			type: String,
			default: '44rpx'
		}
	},
	data() {
		return {
			// 显示文本（文本模式使用）
			displayText: '',
			// 显示项目列表（标签模式使用）
			displayItems: [],
			// 路径查找缓存
			pathCache: new Map()
		};
	},
	computed: {
		// 动态占位符（支持最大数量提示）
		dynamicPlaceholder() {
			if (this.maxCount > 0) {
				return `${this.placeholder}(最多${this.maxCount}个)`;
			}
			return this.placeholder;
		}
	},
	watch: {
		value: {
			handler() {
				this.updateDisplay();
			},
			immediate: true,
			deep: false // 改为浅监听，提高性能
		},
		options: {
			handler() {
				this.clearPathCache(); // 清除缓存
				this.updateDisplay();
			},
			deep: false // 改为浅监听，只监听数组引用变化
		},
		displayMode: {
			handler() {
				this.updateDisplay();
			}
		},
		showFullPath: {
			handler() {
				this.updateDisplay();
			}
		}
	},
	methods: {
		// 处理点击事件
		handleClick() {
			if (this.disabled) return;
			this.$emit('click');
		},

		// 处理删除标签
		handleDeleteTag(item, index) {
			if (!this.allowDelete) return;

			// 统一处理删除逻辑
			const newValue = this.handleDeleteItem(item, index);

			// 触发删除事件，保持与popup-cascader一致的回调格式
			this.$emit('delete', {
				value: newValue,
				deletedItem: item,
				deletedIndex: index
			});

			// 触发change事件
			this.$emit('change', newValue);
		},

		// 统一处理删除项目
		handleDeleteItem(item, index) {
			if (!this.value) return null;

			// 处理包含完整路径信息的对象（如级联选择器返回的数据）
			if (typeof this.value === 'object' && !Array.isArray(this.value) &&
				this.value.ident && this.value.value && Array.isArray(this.value.value)) {

				const newValue = JSON.parse(JSON.stringify(this.value)); // 深拷贝

				// 从 value 数组中删除对应项目
				const itemIndex = newValue.value.findIndex(valueItem => valueItem.id === item.id);
				if (itemIndex !== -1) {
					newValue.value.splice(itemIndex, 1);

					// 同时更新 paths 数组
					if (newValue.paths && Array.isArray(newValue.paths)) {
						newValue.paths.splice(itemIndex, 1);
					}
				}

				// 如果删除后没有项目了，返回 null
				return newValue.value.length === 0 ? null : newValue;
			}

			// 处理完整路径数组（二维数组）
			if (Array.isArray(this.value) && this.value.length > 0 &&
				Array.isArray(this.value[0]) && typeof this.value[0][0] === 'number') {
				const newValue = this.value.filter(pathArray => {
					const endId = pathArray[pathArray.length - 1];
					return endId !== item.id;
				});

				return newValue.length === 0 ? null : newValue;
			}

			// 处理多选分组数据（包含child子项的数据结构）
			if (Array.isArray(this.value) && this.value.length > 0 &&
				this.value[0] && typeof this.value[0] === 'object' && this.value[0].child) {

				const newValue = JSON.parse(JSON.stringify(this.value)); // 深拷贝

				for (let i = 0; i < newValue.length; i++) {
					const group = newValue[i];
					if (group.child && Array.isArray(group.child)) {
						const itemIndex = group.child.findIndex(childItem =>
							childItem.id === item.id
						);

						if (itemIndex !== -1) {
							group.child.splice(itemIndex, 1);
							// 如果该分组下没有子项了，删除整个分组
							if (group.child.length === 0) {
								newValue.splice(i, 1);
							}
							break;
						}
					}
				}

				return newValue;
			}

			// 处理数组类型（级联选择器、普通多选）
			if (Array.isArray(this.value)) {
				const newValue = [...this.value];

				// 如果是ID数组，根据item的ID删除
				if (typeof this.value[0] === 'number' && item.id !== undefined) {
					const deleteIndex = newValue.findIndex(val => val === item.id);
					if (deleteIndex !== -1) {
						newValue.splice(deleteIndex, 1);
					}
				} else {
					// 其他情况，根据索引删除
					newValue.splice(index, 1);
				}

				return newValue;
			}

			// 单个值删除后为空
			return null;
		},

		// 获取项目的唯一标识
		getItemKey(item, index = 0) {
			if (typeof item === 'object' && item !== null) {
				return item.id || item[this.props.value] || item.name || index;
			}
			return item || index;
		},

		// 获取项目的显示标签
		getItemLabel(item) {
			if (typeof item === 'object' && item !== null) {
				return item.name || item[this.props.label] || item.label || String(item);
			}
			return String(item);
		},

		// 更新显示内容（文本模式和标签模式）
		updateDisplay() {
			// 简单的防抖处理
			if (this.updateTimer) {
				clearTimeout(this.updateTimer);
			}
			this.updateTimer = setTimeout(() => {
				if (this.displayMode === 'text') {
					this.updateDisplayText();
				} else if (this.displayMode === 'tag') {
					this.updateDisplayItems();
				}
			}, 10);
		},

		// 更新显示文本（文本模式）
		updateDisplayText() {
			if (!this.value) {
				this.displayText = '';
				return;
			}

			// 统一处理所有数据类型
			const items = this.parseValueToItems();

			if (items.length === 0) {
				// 如果没有解析到项目，显示空文本
				this.displayText = '';
				return;
			}

			const labels = items.map(item => this.getDisplayLabel(item));
			this.displayText = labels.join(this.separator);
		},

		// 更新显示项目（标签模式）
		updateDisplayItems() {
			if (!this.value) {
				this.displayItems = [];
				return;
			}

			// 统一处理所有数据类型
			this.displayItems = this.parseValueToItems();
		},

		// 解析value为统一的项目数组
		parseValueToItems() {
			if (!this.value) return [];

			try {
				// 处理包含完整路径信息的对象（如级联选择器返回的数据）
				if (typeof this.value === 'object' && !Array.isArray(this.value) &&
					this.value.ident && this.value.value && Array.isArray(this.value.value)) {
					return this.value.value.map(item => {
						if (!item || typeof item !== 'object') {
							console.warn('x-form-select: Invalid item in value.value array:', item);
							return { id: null, name: '', fullInfo: item };
						}
						return {
							id: item.id,
							name: this.showFullPath ?
								(item.pathLabels && Array.isArray(item.pathLabels) ? item.pathLabels.join(this.cascaderSeparator) : item.name) :
								item.name,
							fullInfo: item
						};
					}).filter(item => item.id !== null);
				}
			} catch (error) {
				console.error('x-form-select: Error parsing cascader object:', error);
				return [];
			}



			try {
				// 处理完整路径数组（二维数组）
				if (Array.isArray(this.value) && this.value.length > 0 &&
					Array.isArray(this.value[0]) && typeof this.value[0][0] === 'number') {
					return this.value.map(pathArray => {
						if (!Array.isArray(pathArray) || pathArray.length === 0) {
							console.warn('x-form-select: Invalid path array:', pathArray);
							return null;
						}

						const endId = pathArray[pathArray.length - 1]; // 取末级ID

						// 如果有options数据源，尝试查找路径
						if (this.options.length > 0) {
							const path = this.findPathById(endId, this.options);
							const label = this.showFullPath ?
								path.join(this.cascaderSeparator) :
								(path.length > 0 ? path[path.length - 1] : String(endId));

							return {
								id: endId,
								name: label,
								path: path,
								fullPath: pathArray,
								[this.props.value]: endId,
								[this.props.label]: label
							};
						} else {
							// 没有options数据源时，直接显示末级ID
							return {
								id: endId,
								name: String(endId),
								fullPath: pathArray,
								[this.props.value]: endId,
								[this.props.label]: String(endId)
							};
						}
					}).filter(item => item !== null);
				}
			} catch (error) {
				console.error('x-form-select: Error parsing path arrays:', error);
				return [];
			}

			// 处理多选分组数据（包含child子项的数据结构）
			if (Array.isArray(this.value) && this.value.length > 0 &&
				this.value[0] && typeof this.value[0] === 'object' && this.value[0].child) {
				return this.value.flatMap(group =>
					(group.child || []).map(item => ({
						id: item.id,
						name: item.name,
						groupName: group.name,
						groupId: group.id
					}))
				);
			}

			// 处理级联数据（ID数组）
			if (Array.isArray(this.value) && this.value.length > 0 &&
				typeof this.value[0] === 'number' && this.options.length > 0) {
				return this.value.map(id => {
					const path = this.findPathById(id, this.options);
					const label = this.showFullPath ?
						path.join(this.cascaderSeparator) :
						(path.length > 0 ? path[path.length - 1] : String(id));

					return {
						id: id,
						name: label,
						path: path,
						[this.props.value]: id,
						[this.props.label]: label
					};
				}).filter(item => item.name);
			}

			// 处理普通选择器数据
			if (Array.isArray(this.value)) {
				return this.value.map(val => {
					const option = this.options.find(opt => opt[this.props.value] === val);
					return option || { [this.props.value]: val, [this.props.label]: val };
				});
			}

			// 处理单个值
			if (this.value !== null && this.value !== undefined) {
				if (typeof this.value === 'number' && this.options.length > 0) {
					// 级联单选
					const path = this.findPathById(this.value, this.options);
					const label = this.showFullPath ?
						path.join(this.cascaderSeparator) :
						(path.length > 0 ? path[path.length - 1] : String(this.value));

					return [{
						id: this.value,
						name: label,
						path: path,
						[this.props.value]: this.value,
						[this.props.label]: label
					}];
				} else {
					// 普通单选
					const option = this.options.find(opt => opt[this.props.value] === this.value);
					return [option || { [this.props.value]: this.value, [this.props.label]: this.value }];
				}
			}

			return [];
		},

		// 获取显示标签
		getDisplayLabel(item) {
			if (typeof item === 'object' && item !== null) {
				return item.name || item[this.props.label] || item.label || String(item);
			}
			return String(item);
		},



		// 根据ID查找标签
		findLabelById(id, options) {
			for (const option of options) {
				if (option[this.props.value] === id) {
					return option[this.props.label];
				}
				if (option[this.props.children] && option[this.props.children].length > 0) {
					const childLabel = this.findLabelById(id, option[this.props.children]);
					if (childLabel) return childLabel;
				}
			}
			return null;
		},

		// 根据ID查找完整路径（带缓存）
		findPathById(id, options, currentPath = []) {
			if (!options || !Array.isArray(options) || options.length === 0) {
				return [];
			}

			try {
				// 检查缓存
				const cacheKey = `${id}_${JSON.stringify(currentPath)}`;
				if (this.pathCache && this.pathCache.has(cacheKey)) {
					return this.pathCache.get(cacheKey);
				}

				for (const option of options) {
					if (!option || typeof option !== 'object') continue;

					const newPath = [...currentPath, option[this.props.label]];
					if (option[this.props.value] === id) {
						// 缓存结果
						if (this.pathCache) {
							this.pathCache.set(cacheKey, newPath);
						}
						return newPath;
					}
					if (option[this.props.children] && Array.isArray(option[this.props.children]) && option[this.props.children].length > 0) {
						const result = this.findPathById(id, option[this.props.children], newPath);
						if (result.length > 0) {
							// 缓存结果
							if (this.pathCache) {
								this.pathCache.set(cacheKey, result);
							}
							return result;
						}
					}
				}

				// 缓存空结果
				if (this.pathCache) {
					this.pathCache.set(cacheKey, []);
				}
				return [];
			} catch (error) {
				console.error('x-form-select: Error in findPathById:', error);
				return [];
			}
		},

		// 清除路径缓存
		clearPathCache() {
			try {
				if (this.pathCache && typeof this.pathCache.clear === 'function') {
					this.pathCache.clear();
				}
			} catch (error) {
				console.error('x-form-select: Error clearing cache:', error);
				// 重新初始化缓存
				this.pathCache = new Map();
			}
		}
	},
	beforeDestroy() {
		// 清理定时器和缓存
		try {
			if (this.updateTimer) {
				clearTimeout(this.updateTimer);
				this.updateTimer = null;
			}
			this.clearPathCache();
		} catch (error) {
			console.error('x-form-select: Error in beforeDestroy:', error);
		}
	},
	// uni-app 兼容性：添加 beforeUnmount 生命周期
	beforeUnmount() {
		this.beforeDestroy();
	}
};
</script>

<style lang="scss" scoped>
.x-form-select {
	flex: 1;

	// 文本显示模式
	&__input {
		width: 100%;
		display: flex;
		align-items: center;
		padding: 0 20rpx;
		box-sizing: border-box;
		border: 1rpx solid #e5e5e5;
		border-radius: 8rpx;
		background-color: #f8f8f8;
		/* height 通过 props 动态设置 */
	}

	&__input-field {
		flex: 1;
		font-size: 30rpx;
		color: #333333;
		background: transparent;
		border: none;
		outline: none;
		/* height 和 line-height 通过 props 动态设置 */

		&::placeholder {
			color: #c0c4cc;
		}

		&:disabled {
			color: #333333;
			background: transparent;
		}
	}

	// 标签显示模式
	&__tag-container {
		display: flex;
		align-items: flex-start;
		width: 100%;
		padding: 15rpx 0;
		/* min-height 和 gap 通过 props 动态设置 */

		.x-form-select__placeholder {
			line-height: 60rpx;
			font-size: 30rpx;
			color: #909099;
		}
	}

	&__tag-content {
		flex: 1;
		display: flex;
		flex-wrap: wrap;
		/* gap 通过 props 动态设置，移除固定的 margin */
	}

	&__tag {
		display: flex;
		align-items: center;
		padding: 0 10rpx;
		background: #f3f3f5;
		border-radius: 4rpx;
		border: 1px solid #d8d8db;
		/* height、line-height 和 margin 通过 props 动态设置 */
	}

	&__tag-name {
		font-weight: 400;
		font-size: 26rpx;
		color: #1e2134;
		line-height: 1;
	}

	&__tag-delete {
		width: 24rpx;
		height: 24rpx;
		margin-left: 8rpx;
		border-radius: 50%;
		background-color: rgba(0, 0, 0, 0.3);
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		flex-shrink: 0;
		transition: background-color 0.2s ease;

		&:hover {
			background-color: rgba(0, 0, 0, 0.5);
		}

		&-line {
			width: 12rpx;
			height: 2rpx;
			background-color: white;
			border-radius: 1rpx;
		}
	}
}
</style>
