# x-form-select 表单选择器显示组件

## 概述

`x-form-select` 是一个专门用于表单中显示选择器回显文本的组件，用来替换表单中的 `<input>` 标签。它统一处理各种数据格式，支持文本和标签两种显示模式。

## 特性

- 🎯 **专注显示**: 专门用于显示选择器的回显文本
- 🔄 **统一处理**: 统一处理各种数据格式，无需区分数据类型
- 🎨 **无缝替换**: 直接替换表单中的input标签
- 🏷️ **双显示模式**: 支持文本模式和标签模式
- 🗑️ **标签删除**: 标签模式支持删除功能，回调格式与 popup-cascader 一致
- 📍 **路径控制**: 支持显示完整路径或仅显示末级文本
- ⚡ **轻量高效**: 纯显示组件，性能优异
- 🛠️ **易集成**: 最小化的API设计，易于集成

## 显示模式

- `text`: 文本模式，使用禁用的 input 显示文本
- `tag`: 标签模式，以标签形式展示选中项，支持删除操作

## 基本用法

### 替换原有的input标签

**原来的代码：**

```vue
<input :value="selectedDataValue" placeholder="请选择数据" disabled />
```

**使用 x-form-select 替换：**

```vue
<!-- 文本模式 -->
<x-form-select
  :value="form.selectedData"
  placeholder="请选择数据"
  display-mode="text"
  @click="onShowSelectPopup('data')"
/>

<!-- 标签模式 -->
<x-form-select
  :value="form.selectedData"
  placeholder="请选择数据"
  display-mode="tag"
  :allow-delete="true"
  @delete="handleDelete"
  @click="onShowSelectPopup('data')"
/>
```

### 普通选择器显示

```vue
<template>
  <view class="form-item">
    <text>服务类型</text>
    <x-form-select
      :value="form.selectedService"
      :options="serviceOptions"
      placeholder="请选择服务类型"
      type="select"
      @click="showServiceSelector"
    />
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {
        selectedService: 2 // 回显数据：会显示"开发服务"
      },
      serviceOptions: [
        { value: 1, label: '设计服务' },
        { value: 2, label: '开发服务' },
        { value: 3, label: '咨询服务' }
      ]
    };
  },
  methods: {
    showServiceSelector() {
      // 显示选择器弹窗
    }
  }
};
</script>
```

### 多选显示

```vue
<x-form-select
  :value="[1, 3]"
  :options="skillOptions"
  placeholder="请选择技能"
  type="select"
  separator="、"
/>
<!-- 显示：Vue.js、Angular -->
```

### 级联选择器

```vue
<template>
  <x-form-select
    type="cascader"
    title="选择服务类别"
    placeholder="请选择服务类别"
    :options="categoryOptions"
    v-model="selectedCategory"
    :level="2"
    :props="{ value: 'id', label: 'name', children: 'children' }"
    @change="handleCategoryChange"
  />
</template>

<script>
export default {
  data() {
    return {
      selectedCategory: 75, // 回显末级ID
      categoryOptions: [
        {
          id: 1,
          name: '设计服务',
          children: [
            { id: 11, name: 'UI设计' },
            { id: 12, name: '平面设计' }
          ]
        },
        {
          id: 2,
          name: '开发服务',
          children: [
            { id: 21, name: '前端开发' },
            { id: 22, name: '后端开发' }
          ]
        }
      ]
    };
  }
};
</script>
```

### 城市选择器

```vue
<template>
  <x-form-select
    type="city"
    title="选择所在城市"
    placeholder="请选择所在城市"
    v-model="selectedCity"
    :level="3"
    @change="handleCityChange"
  />
</template>

<script>
export default {
  data() {
    return {
      selectedCity: {
        provinceId: '110000',
        provinceName: '北京市',
        cityId: '110100',
        cityName: '北京市',
        districtId: '110101',
        districtName: '东城区'
      }
    };
  }
};
</script>
```

### 多选城市选择器

```vue
<template>
  <x-form-select
    type="multiple-city"
    title="选择服务地区"
    placeholder="请选择服务地区"
    v-model="selectedRegions"
    :max="6"
    @change="handleRegionChange"
  />
</template>

<script>
export default {
  data() {
    return {
      selectedRegions: [
        {
          id: 1,
          name: '北京市',
          child: [
            { id: 11, name: '朝阳区' },
            { id: 12, name: '海淀区' }
          ]
        }
      ]
    };
  }
};
</script>
```

## Props 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | String/Number/Array/Object | null | 选中的值 |
| placeholder | String | '请选择' | 占位符文本 |
| options | Array | [] | 选项数据源（用于数据回显） |
| displayMode | String | 'text' | 显示模式：text/tag |
| showFullPath | Boolean | true | 是否显示完整路径（false时只显示末级） |
| separator | String | '、' | 多选时的分隔符 |
| cascaderSeparator | String | '/' | 级联选择时的分隔符 |
| maxCount | Number | 0 | 最大数量提示（0表示不限制） |
| allowDelete | Boolean | true | 标签模式是否允许删除 |
| props | Object | {value:'value',label:'label',children:'children'} | 字段映射配置 |
| disabled | Boolean | true | 是否禁用（默认禁用，因为是显示组件） |
| customClass | String | '' | 自定义样式类名 |
| inputHeight | String | '60rpx' | 输入框高度（文本模式） |
| minHeight | String | '60rpx' | 标签容器最小高度（标签模式） |
| tagSpacing | String | '8rpx' | 标签间距 |
| tagHeight | String | '44rpx' | 标签高度 |

## Events 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| click | 点击组件时触发 | - |
| delete | 删除标签时触发（标签模式） | {value, deletedItem, deletedIndex} |
| change | 删除标签后触发 | (newValue) |

## 数据回显说明

### 普通选择器回显

- 单选：传入对应的 value 值
- 多选：传入 value 值的数组

### 级联选择器回显

- 支持传入末级 ID，组件会自动查找完整路径
- 支持传入完整路径数组（二维数组）
- 支持传入包含完整信息的对象
- 支持传入包含完整路径信息的对象格式（如 popup-cascader 返回的数据）

**完整路径数组格式示例：**
```javascript
// 二维数组，每个子数组表示一个完整路径
[
  [32, 116],  // 第一个选项的完整路径
  [32, 114]   // 第二个选项的完整路径
]
```

**包含完整路径信息的对象格式示例：**
```javascript
{
  "ident": "cascader",
  "value": [
    {
      "id": 123,
      "name": "债权债务纠纷处理",
      "path": [33, 123],
      "pathLabels": ["法律顾问", "债权债务纠纷处理"],
      "level": 1
    }
  ],
  "paths": [[33, 123]]
}
```

### 多选分组数据回显
- 传入分组数组，每个元素包含 child 子项

## 注意事项

1. 确保引入了相关的依赖组件（popup-select、popup-cascader 等）
2. 图标路径需要根据项目实际情况调整
3. 数据回显时，确保数据格式与组件期望的格式一致
4. 级联选择器的数据源需要符合树形结构

## 更新日志

### v1.0.0
- 初始版本发布
- 支持四种选择器类型
- 完善的数据回显功能
- 统一的API设计
