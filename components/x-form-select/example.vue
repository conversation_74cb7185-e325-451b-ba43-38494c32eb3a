<template>
	<view class="example-page">
		<u-navbar title="x-form-select 示例" :statusBar="true" :placeholder="true" :autoBack="true" />
		
		<scroll-view scroll-y class="example-content">
			<!-- 普通单选选择器 -->
			<view class="example-section">
				<text class="example-title">普通单选选择器</text>
				<x-form-select
					type="select"
					title="选择服务类型"
					placeholder="请选择服务类型"
					:options="serviceOptions"
					v-model="form.selectedService"
					@change="handleServiceChange"
				/>
				<text class="example-result">选中值: {{ form.selectedService }}</text>
			</view>
			
			<!-- 普通多选选择器 -->
			<view class="example-section">
				<text class="example-title">普通多选选择器</text>
				<x-form-select
					type="select"
					title="选择技能"
					placeholder="请选择技能（最多3个）"
					:options="skillOptions"
					v-model="form.selectedSkills"
					:multiple="true"
					:max="3"
					display-mode="grid"
					@change="handleSkillChange"
				/>
				<text class="example-result">选中值: {{ JSON.stringify(form.selectedSkills) }}</text>
			</view>
			
			<!-- 级联选择器 -->
			<view class="example-section">
				<text class="example-title">级联选择器</text>
				<x-form-select
					type="cascader"
					title="选择服务类别"
					placeholder="请选择服务类别"
					:options="categoryOptions"
					v-model="form.selectedCategory"
					:level="2"
					:props="{ value: 'id', label: 'name', children: 'children' }"
					@change="handleCategoryChange"
				/>
				<text class="example-result">选中值: {{ form.selectedCategory }}</text>
			</view>
			
			<!-- 级联多选选择器 -->
			<view class="example-section">
				<text class="example-title">级联多选选择器</text>
				<x-form-select
					type="cascader"
					title="选择多个服务类别"
					placeholder="请选择多个服务类别"
					:options="categoryOptions"
					v-model="form.selectedCategories"
					:level="2"
					:multiple="true"
					:max="5"
					:props="{ value: 'id', label: 'name', children: 'children' }"
					@change="handleCategoriesChange"
				/>
				<text class="example-result">选中值: {{ JSON.stringify(form.selectedCategories) }}</text>
			</view>
			
			<!-- 城市选择器 -->
			<view class="example-section">
				<text class="example-title">城市选择器</text>
				<x-form-select
					type="city"
					title="选择所在城市"
					placeholder="请选择所在城市"
					v-model="form.selectedCity"
					:level="3"
					@change="handleCityChange"
				/>
				<text class="example-result">选中值: {{ JSON.stringify(form.selectedCity) }}</text>
			</view>
			
			<!-- 多选城市选择器 -->
			<view class="example-section">
				<text class="example-title">多选城市选择器</text>
				<x-form-select
					type="multiple-city"
					title="选择服务地区"
					placeholder="请选择服务地区（最多6个）"
					v-model="form.selectedRegions"
					:max="6"
					@change="handleRegionChange"
				/>
				<text class="example-result">选中值: {{ JSON.stringify(form.selectedRegions) }}</text>
			</view>
			
			<!-- 错误状态示例 -->
			<view class="example-section">
				<text class="example-title">错误状态示例</text>
				<x-form-select
					type="select"
					title="必填选择器"
					placeholder="请选择（必填）"
					:options="serviceOptions"
					v-model="form.requiredField"
					:error="!form.requiredField"
					error-message="此字段为必填项"
					@change="handleRequiredChange"
				/>
			</view>
			
			<!-- 禁用状态示例 -->
			<view class="example-section">
				<text class="example-title">禁用状态示例</text>
				<x-form-select
					type="select"
					title="禁用选择器"
					placeholder="已禁用"
					:options="serviceOptions"
					v-model="form.disabledField"
					:disabled="true"
				/>
			</view>
			
			<!-- 操作按钮 -->
			<view class="example-actions">
				<button class="example-btn" @click="resetForm">重置表单</button>
				<button class="example-btn primary" @click="submitForm">提交表单</button>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	name: 'XFormSelectExample',
	data() {
		return {
			// 表单数据
			form: {
				selectedService: 2, // 回显数据
				selectedSkills: [1, 3], // 回显多选数据
				selectedCategory: 12, // 回显级联数据（末级ID）
				selectedCategories: [11, 22], // 回显级联多选数据
				selectedCity: {
					provinceId: '110000',
					provinceName: '北京市',
					cityId: '110100',
					cityName: '北京市',
					districtId: '110101',
					districtName: '东城区'
				}, // 回显城市数据
				selectedRegions: [], // 多选城市数据
				requiredField: null,
				disabledField: 1
			},
			
			// 服务类型选项
			serviceOptions: [
				{ value: 1, label: '设计服务' },
				{ value: 2, label: '开发服务' },
				{ value: 3, label: '咨询服务' },
				{ value: 4, label: '运营服务' }
			],
			
			// 技能选项
			skillOptions: [
				{ value: 1, label: 'Vue.js' },
				{ value: 2, label: 'React' },
				{ value: 3, label: 'Angular' },
				{ value: 4, label: 'Node.js' },
				{ value: 5, label: 'Python' },
				{ value: 6, label: 'Java' }
			],
			
			// 级联选项（服务类别）
			categoryOptions: [
				{
					id: 1,
					name: '设计服务',
					children: [
						{ id: 11, name: 'UI设计' },
						{ id: 12, name: '平面设计' },
						{ id: 13, name: '品牌设计' }
					]
				},
				{
					id: 2,
					name: '开发服务',
					children: [
						{ id: 21, name: '前端开发' },
						{ id: 22, name: '后端开发' },
						{ id: 23, name: '移动开发' }
					]
				},
				{
					id: 3,
					name: '营销服务',
					children: [
						{ id: 31, name: '内容营销' },
						{ id: 32, name: '社媒运营' },
						{ id: 33, name: 'SEO优化' }
					]
				}
			]
		};
	},
	methods: {
		// 处理服务类型选择
		handleServiceChange(value, option) {
			console.log('服务类型选择:', value, option);
		},
		
		// 处理技能选择
		handleSkillChange(values, options) {
			console.log('技能选择:', values, options);
		},
		
		// 处理类别选择
		handleCategoryChange(value, option) {
			console.log('类别选择:', value, option);
		},
		
		// 处理多类别选择
		handleCategoriesChange(values, options) {
			console.log('多类别选择:', values, options);
		},
		
		// 处理城市选择
		handleCityChange(city) {
			console.log('城市选择:', city);
		},
		
		// 处理地区选择
		handleRegionChange(regions) {
			console.log('地区选择:', regions);
		},
		
		// 处理必填字段选择
		handleRequiredChange(value) {
			console.log('必填字段选择:', value);
		},
		
		// 重置表单
		resetForm() {
			this.form = {
				selectedService: null,
				selectedSkills: [],
				selectedCategory: null,
				selectedCategories: [],
				selectedCity: {},
				selectedRegions: [],
				requiredField: null,
				disabledField: 1
			};
			uni.showToast({
				title: '表单已重置',
				icon: 'success'
			});
		},
		
		// 提交表单
		submitForm() {
			// 简单验证
			if (!this.form.requiredField) {
				uni.showToast({
					title: '请填写必填字段',
					icon: 'none'
				});
				return;
			}
			
			console.log('提交表单数据:', this.form);
			uni.showToast({
				title: '提交成功',
				icon: 'success'
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.example-page {
	height: 100vh;
	background: #f5f5f5;
}

.example-content {
	height: calc(100vh - 88rpx);
	padding: 20rpx;
}

.example-section {
	margin-bottom: 40rpx;
	padding: 30rpx;
	background: #ffffff;
	border-radius: 12rpx;
}

.example-title {
	display: block;
	margin-bottom: 20rpx;
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.example-result {
	display: block;
	margin-top: 20rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	font-size: 26rpx;
	color: #666666;
	word-break: break-all;
}

.example-actions {
	display: flex;
	gap: 20rpx;
	margin-top: 40rpx;
	padding: 0 30rpx;
}

.example-btn {
	flex: 1;
	height: 88rpx;
	border: 1px solid #dcdfe6;
	border-radius: 8rpx;
	background: #ffffff;
	font-size: 30rpx;
	color: #606266;
	
	&.primary {
		background: #409eff;
		border-color: #409eff;
		color: #ffffff;
	}
}
</style>
