# x-form-select 使用说明

## 🎯 组件用途

`x-form-select` 是一个专门用于**替换表单中 input 标签**的显示组件，统一处理各种数据格式，支持文本和标签两种显示模式。

## 🔄 替换方式

### 原来的写法：
```vue
<view class="flex section-wrap__normal-cell" @click="onShowSelectPopup('serviceRegion')">
  <text>服务地区</text>
  <input :value="serviceRegionValue" placeholder="请选择服务地区" disabled />
  <image class="section-wrap__normal-cell__arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
</view>
```

### 使用 x-form-select 后：

```vue
<!-- 文本模式 -->
<view class="flex section-wrap__normal-cell" @click="onShowSelectPopup('data')">
  <text>选择数据</text>
  <x-form-select
    :value="form.selectedData"
    placeholder="请选择数据"
    display-mode="text"
  />
  <image class="section-wrap__normal-cell__arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
</view>

<!-- 标签模式 -->
<view class="flex section-wrap__normal-cell" @click="onShowSelectPopup('data')">
  <text>选择数据</text>
  <x-form-select
    :value="form.selectedData"
    placeholder="请选择数据"
    display-mode="tag"
    :allow-delete="true"
    @delete="handleDelete"
  />
  <image class="section-wrap__normal-cell__arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
</view>
```

## ✨ 主要优势

1. **自动回显**：传入数据源和选中值，自动显示中文文本
2. **无需计算属性**：不再需要写 `selectedDataValue` 这样的计算属性
3. **统一处理**：统一处理各种数据格式，无需区分数据类型
4. **双显示模式**：支持文本模式和标签模式
5. **标签删除**：标签模式支持删除功能，回调格式与 popup-cascader 一致
6. **统一样式**：提供一致的表单输入框样式

## 📋 支持的数据类型

### 1. 普通选择器 (type="select")
```javascript
// 单选
value: 2
options: [
  { value: 1, label: '设计服务' },
  { value: 2, label: '开发服务' }
]
// 显示：开发服务

// 多选
value: [1, 3]
options: [
  { value: 1, label: 'Vue.js' },
  { value: 2, label: 'React' },
  { value: 3, label: 'Angular' }
]
// 显示：Vue.js、Angular
```

### 2. 级联选择器 (type="cascader")
```javascript
// 传入末级ID，自动查找路径
value: 12
options: [
  {
    id: 1,
    name: '设计服务',
    children: [
      { id: 11, name: 'UI设计' },
      { id: 12, name: '平面设计' }
    ]
  }
]
// 显示：设计服务/平面设计

// 传入多个末级ID
value: [11, 22]
// 显示：UI设计、前端开发
```

### 3. 地区选择器数据
```javascript
value: {
  provinceName: '北京市',
  cityName: '北京市',
  districtName: '朝阳区'
}
// 显示：北京市/北京市/朝阳区
```

### 4. 多选分组数据
```javascript
value: [
  {
    name: '分组A',
    child: [
      { name: '选项1' },
      { name: '选项2' }
    ]
  }
]
// 显示：选项1、选项2
```

## 🚀 快速集成步骤

### 1. 复制组件文件
将 `components/x-form-select/x-form-select.vue` 复制到你的项目中

### 2. 在页面中引入
```vue
<script>
import XFormSelect from '@/components/x-form-select/x-form-select.vue';

export default {
  components: {
    XFormSelect
  }
}
</script>
```

### 3. 替换 input 标签
找到你表单中的 input 标签，按照上面的方式替换即可

### 4. 删除不需要的计算属性
替换后，你可以删除原来的 `serviceRegionValue`、`addressValue` 等计算属性

## 🎨 样式说明

组件会自动适配你现有的表单样式，无需额外的样式调整。

## 📝 API 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | Any | null | 选中的值 |
| options | Array | [] | 数据源（级联选择器需要） |
| placeholder | String | '请选择' | 占位符文本 |
| type | String | 'auto' | 数据类型，支持 auto/select/cascader/city/multiple-city |
| props | Object | {value:'value',label:'label',children:'children'} | 字段映射 |
| separator | String | '、' | 多选分隔符 |
| cascaderSeparator | String | '/' | 级联分隔符 |

## 🔧 注意事项

1. 组件只负责显示，不处理选择逻辑
2. 点击事件需要你自己处理（调用原有的选择器弹窗）
3. 表单验证逻辑保持不变
4. 如果使用 `type="auto"`，组件会自动识别数据类型
