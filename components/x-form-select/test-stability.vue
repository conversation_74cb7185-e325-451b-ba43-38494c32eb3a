<template>
	<view class="test-container">
		<text class="title">x-form-select 稳定性测试</text>
		
		<!-- 测试1: 空值处理 -->
		<view class="test-section">
			<text class="section-title">测试1: 空值处理</text>
			<x-form-select :value="null" placeholder="空值测试" />
			<x-form-select :value="undefined" placeholder="undefined测试" />
			<x-form-select :value="[]" placeholder="空数组测试" />
		</view>
		
		<!-- 测试2: 错误数据格式 -->
		<view class="test-section">
			<text class="section-title">测试2: 错误数据格式</text>
			<x-form-select :value="invalidData1" placeholder="无效对象测试" />
			<x-form-select :value="invalidData2" placeholder="无效数组测试" />
		</view>
		
		<!-- 测试3: 样式自定义 -->
		<view class="test-section">
			<text class="section-title">测试3: 样式自定义</text>
			<x-form-select 
				:value="testValue" 
				display-mode="text"
				custom-class="custom-style"
				input-height="100rpx"
				placeholder="自定义文本样式"
			/>
			<x-form-select 
				:value="testArray" 
				display-mode="tag"
				custom-class="custom-tag-style"
				tag-height="60rpx"
				tag-spacing="15rpx"
				min-height="80rpx"
				placeholder="自定义标签样式"
			/>
		</view>
		
		<!-- 测试4: 大数据量 -->
		<view class="test-section">
			<text class="section-title">测试4: 大数据量</text>
			<x-form-select 
				:value="largeDataArray" 
				display-mode="tag"
				:max-count="5"
				placeholder="大数据量测试"
			/>
		</view>
		
		<!-- 测试5: 动态切换 -->
		<view class="test-section">
			<text class="section-title">测试5: 动态切换</text>
			<button @click="switchMode">切换显示模式: {{ currentMode }}</button>
			<button @click="switchData">切换数据</button>
			<x-form-select 
				:value="dynamicValue" 
				:display-mode="currentMode"
				placeholder="动态切换测试"
			/>
		</view>
	</view>
</template>

<script>
export default {
	name: 'TestStability',
	data() {
		return {
			// 无效数据测试
			invalidData1: { invalid: 'object' },
			invalidData2: [null, undefined, 'invalid'],
			
			// 正常测试数据
			testValue: '测试文本',
			testArray: [1, 2, 3, 4, 5],
			
			// 大数据量测试
			largeDataArray: Array.from({ length: 50 }, (_, i) => i + 1),
			
			// 动态切换测试
			currentMode: 'text',
			dynamicValue: '初始文本',
			dataIndex: 0,
			testDataList: [
				'文本数据',
				[1, 2, 3],
				{ ident: 'cascader', value: [{ id: 1, name: '选项1' }] },
				[[1, 2], [3, 4]],
				null
			]
		};
	},
	methods: {
		switchMode() {
			this.currentMode = this.currentMode === 'text' ? 'tag' : 'text';
		},
		switchData() {
			this.dataIndex = (this.dataIndex + 1) % this.testDataList.length;
			this.dynamicValue = this.testDataList[this.dataIndex];
		}
	}
};
</script>

<style lang="scss" scoped>
.test-container {
	padding: 20rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	text-align: center;
	margin-bottom: 40rpx;
}

.test-section {
	margin-bottom: 40rpx;
	padding: 20rpx;
	border: 1rpx solid #e5e5e5;
	border-radius: 8rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
	color: #333;
}

button {
	margin: 10rpx;
	padding: 10rpx 20rpx;
	background: #007aff;
	color: white;
	border: none;
	border-radius: 4rpx;
}

/* 自定义样式测试 */
.custom-style {
	.x-form-select__input {
		border: 2rpx solid #ff6b6b;
		border-radius: 15rpx;
		background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
	}
}

.custom-tag-style {
	.x-form-select__tag {
		background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
		color: white;
		border: none;
		border-radius: 30rpx;
		font-weight: bold;
	}
}
</style>
