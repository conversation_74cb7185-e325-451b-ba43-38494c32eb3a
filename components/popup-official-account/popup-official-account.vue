<template>
	<view class="col popup-content">
		<view class="col container">
			<image class="oa-img" src="/static/image/ic_oa_wx.png"></image>
			<text class="oa-title">{{ platformName }}</text>
			<!-- 标题 -->
			<text class="title">关注接单提醒</text>
			<!-- 描述 -->
			<text class="describe">关注及时接收新单服务提醒</text>

			<!-- 按钮组 -->
			<view class="row button" @click="onConfirm">前往微信关注</view>
			<!-- 小贴士 -->
			<text class="tip">
				<rich-text nodes="*关注后，返回App查看接单"></rich-text>
			</text>
		</view>
		<!-- 关闭按钮 -->
		<u-image v-if="showClose" :customStyle="{ marginTop: '60rpx' }" src="/static/image/ic_close_w.png" width="60rpx" height="60rpx" @click="onClose" />
	</view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import { openMiniWx, iWxMiniProgram } from '@/common/utils.js';
export default {
	name: 'popup-official-account',
	props: {
		// 是否展示关闭按钮
		showClose: {
			type: Boolean,
			default: true
		},
		// 拓展信息
		extra: {
			type: [Boolean, String, Object, Array],
			default: () => {}
		}
	},
	computed: {
		...mapState(['userInfo']),
		// 名称
		platformName() {
			return `【${this.$conf.appName}】公众号`;
		}
	},
	data() {
		return {};
	},
	methods: {
		// 取消
		onClose() {
			uni.$u.http
				.post('/system/closeOA', {}, { custom: { toast: false } })
				.then((res) => {})
				.catch((err) => {});
			this.$emit('close');
		},
		// 按钮事件
		onConfirm(e) {
			openMiniWx(iWxMiniProgram('oa', this.$conf.miniProgramId));
			this.onClose();
		}
	}
};
</script>

<style lang="scss" scoped>
.row {
	display: flex;
	flex-direction: row;
}

.col {
	display: flex;
	flex-direction: column;
}

.popup-content {
	width: 560rpx !important;
	justify-content: center;
	align-items: center;
}

.container {
	width: 100%;
	padding: 60rpx 30rpx;
	background: #ffffff;
	justify-content: center;
	align-items: center;
	border-radius: 20rpx;
}

.oa-img {
	width: 120rpx;
	height: 120rpx;
}

.oa-title {
	margin-top: 15rpx;
	font-size: 24rpx;
	font-weight: 400;
	color: #999999;
}

.title {
	margin-top: 50rpx;
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
}

.describe {
	margin-top: 35rpx;
	font-size: 28rpx;
	color: #333333;
	text-align: center;
}

.button {
	margin-top: 50rpx;
	padding: 24rpx 40rpx;
	border-radius: 45rpx;
	background: #293bd1;
	color: #ffffff;
	font-size: 30rpx;
	font-weight: 500;
	align-items: center;
	justify-content: center;
}

.tip {
	margin-top: 40rpx;
	font-size: 24rpx;
	color: #ff0000;
	text-align: center;
	line-height: 46rpx;
}
</style>
