import dialog from "@/components/dialog/dialog.js"
module.exports = {
	/**
	 * 弹出提示
	 */
	alert(title = "", content = "", callback, confirmText = '确定') {
		// #ifdef APP-PLUS
		dialog.alert({
			content,
			title,
			confirmText
		}, callback)
		// #endif
		// #ifndef APP-PLUS
		uni.showModal({
			title,
			content,
			confirmText,
			showCancel: false,
			confirmColor: "#4E61FF",
			success: callback
		})
		// #endif
	},
	/**
	 * 确认提示框
	 */
	confirm(title = "", content = "", confirm, cancel, confirmText = '确定', cancelText = '取消') {
		// #ifdef APP-PLUS
		dialog.confirm({
			content,
			title,
			confirmText,
			cancelText,
		}, confirm, cancel)
		// #endif
		// #ifndef APP-PLUS
		uni.showModal({
			title,
			content,
			cancelText,
			confirmText,
			confirmColor: "#4E61FF",
			success: (e) => {
				if (e.confirm) {
					confirm && confirm()
				} else if (e.cancel) {
					cancel && cancel()
				}
			},
			fail: (e) => {
				console.log(e)
			}
		})
		// #endif
	},
}
