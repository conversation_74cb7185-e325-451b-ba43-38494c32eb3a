<template>
	<view @click="itemClick('mask')" class="mask-content">
		<view class="dialog-content" @click.stop="">
			<view class="head-content " v-if="info.title" :style="info.content?'':'min-height:90rpx;padding:30rpx'">
				<text>{{info.title}}</text>
			</view>
			<scroll-view class="main-content" scroll-y v-if="info.content">
				<view class="info-content">
					<text style="text-align: center;">{{info.content}}</text>
				</view>
			</scroll-view>
			<view class="foot-content alert" v-if="'alert'==info.type">
				<view class="btn active" @click.stop="itemClick('confirm')">
					{{info.confirmText}}
				</view>
			</view>
			<view class="foot-content confirm" v-if="'confirm'==info.type">
				<view class="btn cancel" @click="itemClick('cancel')">
					{{info.cancelText}}
				</view>
				<view class="btn active" @click.stop="itemClick('confirm')">
					{{info.confirmText}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				info: {
					// alert:单按钮,confirm:双按钮
					type: "alert",
					title: "",
					content: "",
					cancelText: "取消",
					confirmText: "确定",
					// 点击遮罩层关闭弹窗
					isMaskClose: "1",
				}
			}
		},
		onLoad(info = {}) {
			this.info = {
				...this.info,
				...info
			};
		},
		methods: {
			itemClick(type) {
				if (type == "mask" && this.info.isMaskClose != '1') {
					return;
				}
				uni.navigateBack()
				uni.$emit("wujw_common_dialog", type);
			}
		}
	}
</script>

<style lang="scss">
	$btncolor: #6C68F0;

	page {
		background: transparent;
	}

	.mask-content {
		position: fixed;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: rgba(0, 0, 0, 0.4);

		.dialog-content {
			background-color: #FFFFFF;
			width: 580rpx;
			border-radius: 20rpx;

			.head-content {
				display: flex;
				align-items: center;
				justify-content: center;
				color: #343434;
				font-weight: bold;
				font-size: 32rpx;
				padding: 50rpx 30rpx 20rpx 30rpx;
			}

			.main-content {
				max-height: 330rpx;

				.info-content {
					min-height: 80rpx;
					padding: 10rpx 30rpx;
					color: #636463;
					font-size: 30rpx;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}

			.foot-content {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 140rpx;

				.btn {
					font-size: 30rpx;
					border-radius: 15rpx;
					height: 80rpx;
					display: flex;
					justify-content: center;
					align-items: center;
				}

				&.alert {
					.btn {
						background-color: $btncolor;
						color: #FFFFFF;
						font-size: 28rpx;
						border-radius: 15rpx;
						height: 80rpx;
						width: 300rpx;
						padding: 0 40rpx;
						display: flex;
						justify-content: center;
						align-items: center;
					}
				}

				&.confirm {
					justify-content: space-around;

					.btn {
						min-width: 240rpx;

						&.active {
							background-color: $btncolor;
							color: #FFFFFF;
						}

						&.cancel {
							border: 0.8px solid $btncolor;
							color: $btncolor;
							border-radius: 15rpx;
						}
					}
				}

			}
		}
	}
</style>