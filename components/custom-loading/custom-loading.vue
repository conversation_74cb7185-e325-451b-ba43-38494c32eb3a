<template>
	<view class="flex-col wrap-loading" v-if="show">
		<view class="flex wrap-loading__wrap">
			<u-loading-icon :show="show" text="加载中..." color="#333333" size="40rpx" textSize="30rpx" textColor="#333333"></u-loading-icon>
		</view>
	</view>
</template>

<script>
export default {
	name: 'custom-loading',
	props: {
		// NONE-默认状态 LOADING.加载中 SUCCESS.加载成功 FAIL.加载失败
		status: {
			type: String,
			default: 'NONE'
		}
	},
	computed: {
		// 是否展示
		show() {
			return this.status == 'LOADING';
		}
	},
	watch: {
		// 监听刷新状态
		status: {
			immediate: true,
			handler(newVal, oldVal) {
				console.warn('刷新状态', newVal);
			}
		}
	},
	data() {
		return {};
	},
	methods: {}
};
</script>

<style lang="scss" scoped>
.wrap-loading {
	width: 100%;
	padding: 15rpx;
	align-items: center;
	justify-content: center;
	&__wrap {
		padding: 15rpx 30rpx;
		// background: rgba(0, 0, 0, 0.6);
		// border-radius: 12rpx;
	}
}
</style>
