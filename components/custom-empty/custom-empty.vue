<template>
	<view class="col empty-container">
		<image v-if="!$u.test.isEmpty(img)" class="img" :src="`/static/image/${img}`" mode="heightFix"></image>
		<!-- 标题 -->
		<text v-if="!$u.test.isEmpty(title)" class="title">{{ title || '' }}</text>
		<!-- 描述 -->
		<view v-if="!$u.test.isEmpty(message)" class="describe">
			<rich-text :nodes="message || ''"></rich-text>
		</view>
		<!-- 按钮组 -->
		<view v-if="!$u.test.isEmpty(actions)" class="row button-box" :style="{ justifyContent: actions.length > 1 ? 'space-between' : 'center' }">
			<view class="row button" :style="[btnStyle, $u.addStyle(item.customStyle)]" v-for="(item, index) in actions" :key="index" @click="onAction(item)">
				{{ item.title }}
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'custom-empty',
	props: {
		// 图片
		img: {
			type: String,
			default: function () {
				return 'img_data_empty.png';
			}
		},
		// 标题
		title: {
			type: String,
			default: function () {
				return '';
			}
		},
		// 描述文本
		message: {
			type: String,
			default: function () {
				return '';
			}
		},
		// 按钮
		actions: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			btnStyle: {
				width: '240rpx'
			}
		};
	},
	//方法
	methods: {
		// 按钮事件
		onAction(e) {
			this.$emit('btnClick', {
				type: e.type
			});
		}
	}
};
</script>

<style>
.row {
	display: flex;
	flex-direction: row;
}

.col {
	display: flex;
	flex-direction: column;
}

.empty-container {
	width: 100%;
	height: 100%;
	padding: 100rpx 30rpx 30rpx 30rpx;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
}

.img {
	height: 254rpx;
	margin-bottom: 40rpx;
}

.title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.describe {
	margin-top: 30rpx;
	font-size: 30rpx;
	color: #666666;
	text-align: center;
	line-height: 46rpx;
}

.button-box {
	/* width: 100%; */
	width: 400rpx;
	margin-top: 70rpx;
	flex-wrap: wrap;
	justify-content: center;
	align-items: center;
}

.button {
	width: 240rpx;
	height: 80rpx;
	border-radius: 40rpx;
	font-size: 30rpx;
	font-weight: 500;
	align-items: center;
	justify-content: center;
}
</style>
