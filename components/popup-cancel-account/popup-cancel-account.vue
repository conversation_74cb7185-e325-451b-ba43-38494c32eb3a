<template>
	<view class="col popup-content">
		<view class="col container">
			<text class="title">账号注销</text>
			<text class="describe">注销需要15个工作日且注销后数据无法恢复，请谨慎操作。</text>
			<rich-text class="tooltip" :nodes="tooltip"></rich-text>
			<view class="row input">
				<u--input :customStyle="{fontWeight:'400'}" v-model="inputText" placeholder="请输入“确认注销”" fontSize="30rpx"
					border="none"></u--input>
			</view>
			<!-- 按钮组 -->
			<view class="row button-box">
				<view class="row confirm" :class="[!confirmAvailable ? 'confirm-disabled' : '']" @click="onConfirm()">
					确认
				</view>
				<view class="row cancel" @click="onClose()">
					取消
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "popup-cancel-account",
		props: {},
		data() {
			return {
				inputText: '',
				// 确认按钮是否可用
				confirmAvailable: false,
			}
		},
		computed: {
			// 提示文本
			tooltip() {
				return '<p>请在下方输入“<span style="color:#E10000;">确认注销</span>”再进行<br/>“确认”操作</p>'
			}
		},
		watch: {
			// 监听输入内容
			inputText: {
				handler(newVal, oldVal) {
					if (newVal == '确认注销') {
						this.confirmAvailable = true
					} else {
						this.confirmAvailable = false
					}
				},
				immediate: true
			},
		},
		methods: {
			// 取消
			onClose() {
				this.$emit('cancel', {});
			},
			// 按钮事件
			onConfirm(e) {
				this.$emit('confirm', {});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.row {
		display: flex;
		flex-direction: row;
	}

	.col {
		display: flex;
		flex-direction: column;
	}

	.popup-content {
		width: 600rpx !important;
		justify-content: center;
		align-items: center;
	}

	.container {
		width: 600rpx;
		padding: 50rpx;
		background: #FFFFFF;
		justify-content: center;
		align-items: center;
		border-radius: 20rpx;
	}


	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
	}

	.describe {
		margin-top: 40rpx;
		font-size: 30rpx;
		color: #333333;
		font-weight: 600;
		text-align: center;
		line-height: 46rpx;
	}

	.tooltip {
		margin-top: 40rpx;
		font-size: 24rpx;
		font-weight: 500;
		color: #999999;
		text-align: center;
	}

	.input {
		width: 100%;
		padding: 0 20rpx;
		height: 100rpx;
		margin-top: 20rpx;
		background: #F7F7F7;
		border-radius: 15rpx;
		align-items: center;
		justify-content: center;
	}

	.button-box {
		width: 100%;
		margin-top: 50rpx;
		justify-content: space-between;
		align-items: center;
	}

	.cancel {
		width: 240rpx;
		height: 80rpx;
		border: 0.8px solid #6C68F0;
		border-radius: 15rpx;
		color: #6C68F0;
		font-size: 30rpx;
		font-weight: 700;
		align-items: center;
		justify-content: center;
	}

	.confirm {
		width: 240rpx;
		height: 80rpx;
		background: #6C68F0;
		border-radius: 15rpx;
		color: #FFFFFF;
		font-size: 30rpx;
		font-weight: 700;
		align-items: center;
		justify-content: center;
	}

	.confirm-disabled {
		opacity: 0.4;
		// filter: grayscale(40%);
		pointer-events: none;
	}
</style>