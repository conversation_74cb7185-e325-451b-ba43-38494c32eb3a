<template>
	<view class="col popup-content">
		<view class="col container">
			<view class="row title-view">
				<text class="title">{{ title }}</text>
			</view>
			<!-- 一级 -->
			<view v-if="level == 1" class="row list-box1">
				<scroll-view scroll-y class="col list1" :scroll-into-view="scrollFirstView">
					<view
						:id="`p-cell${item.id}`"
						class="row list-cell"
						:class="[isSelectFirst(item.id) || firstSelectIndex == index ? 'list-cell-selected list-cell-selected-bg' : '']"
						v-for="(item, index) in firstList"
						:key="index"
						@click="onClickFirst(index, item)"
					>
						<text>{{ item.name }}</text>
						<image class="check" :src="isSelectFirst(item.id) ? '/static/image/ic_check_sel.png' : '/static/image/ic_check_nor.png'" mode="aspectFit"></image>
					</view>
				</scroll-view>
			</view>
			<!-- 二级联动 -->
			<view v-else-if="level == 2" class="row list-box2">
				<scroll-view scroll-y class="col list1" :scroll-into-view="scrollFirstView">
					<view
						:id="`p-cell${item.id}`"
						class="row list-cell"
						:class="[isSelectFirst(item.id) || firstSelectIndex == index ? 'list-cell-selected list-cell-selected-bg' : '']"
						v-for="(item, index) in firstList"
						:key="index"
						@click="onClickFirst(index, item)"
					>
						<text>{{ `${item.name}${countByFirstId(item.id) !== 0 ? '(' + countByFirstId(item.id) + ')' : ''}` }}</text>
					</view>
				</scroll-view>
				<scroll-view scroll-y class="col list2" :scroll-into-view="scrollSecondView">
					<view :id="`c-cell${item.id}`" class="row list-cell" :class="[isSelectSecond(item.id) ? 'list-cell-selected' : '']" v-for="(item, index) in secondList" :key="index" @click="onClickSecond(index, item)">
						<text>{{ item.name }}</text>
						<image class="check" :src="isSelectSecond(item.id) ? '/static/image/ic_check_sel.png' : '/static/image/ic_check_nor.png'" mode="aspectFit"></image>
					</view>
				</scroll-view>
			</view>

			<view class="col bottom-view" v-if="maxNum > 1">
				<view class="row selected-box" v-if="level == 1">
					<view class="item" v-for="item in selectFirst" :key="item.id">
						{{ item.firstName }}
						<image class="item-close" src="/static/image/ic_item_delete.png" @click="deleteCategory(item)" mode="aspectFit"></image>
					</view>
				</view>
				<view class="row selected-box" v-else-if="level == 2">
					<view class="item" v-for="item in selectSecond" :key="item.id">
						{{ item.secondName }}
						<image class="item-close" src="/static/image/ic_item_delete.png" @click="deleteCategory(item)" mode="aspectFit"></image>
					</view>
				</view>
				<view class="row confirm" @click="onConfirm">{{ confimText }}</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'popup-link-select',
	props: {
		// 弹窗状态
		show: {
			type: Boolean,
			default: false
		},
		// 标题
		title: {
			type: String,
			default: '请选择'
		},
		// 联动层级
		level: {
			type: Number,
			default: 1
		},
		// 最大选择的数量
		maxNum: {
			type: Number,
			default: 1
		},
		// 数据源
		list: {
			type: Array,
			default: () => []
		},
		// 默认读取的键名
		keyName: {
			type: String,
			default: 'name'
		},
		// 选择类别
		//[{firstId:''，secondId:'',firstName:'',secondName:''}]
		value: {
			type: Array,
			default: () => []
		},
		// 选择完成事件
		complete: {
			type: Function,
			default: () => {}
		}
	},
	watch: {
		// 监听弹窗状态
		show: function (val) {
			if (val) {
				this.initData();
			}
		}
	},
	data() {
		return {
			// 联动数据
			dataList: [],
			// 一级数据集合
			firstList: [],
			// 二级数据集合
			secondList: [],

			// 选中的分类
			selectFirst: [],
			selectSecond: [],
			// 当前选中一级的索引
			firstSelectIndex: -1,
			// 默认滚动到指定位置
			scrollFirstView: '',
			scrollSecondView: ''
		};
	},
	computed: {
		// 确认按钮文本
		confimText() {
			if (this.level == 1) {
				return `确认选择（${this.selectFirst.length}/${this.maxNum}）`;
			} else if (this.level == 2) {
				return `确认选择（${this.selectSecond.length}/${this.maxNum}）`;
			}
			return '确认选择';
		},
		// 选中一级分类的ids
		selectPIds() {
			return this.selectFirst.map((obj) => obj.firstId);
		},
		// 选中二级分类的ids
		selectCIds() {
			return this.selectSecond.map((obj) => obj.secondId);
		}
	},
	created() {
		this.initData();
	},
	methods: {
		// 初始化数据
		initData() {
			if (this.level == 1) {
				this.dataList = this.list;
				this.firstList = this.dataList;
			} else if (this.level == 2) {
				this.dataList = this.list;
				this.firstList = this.dataList;
				this.secondList = this.firstList[0].children;
			}
			this.selectFirst = [];
			this.selectSecond = [];
			this.setDefaultSelectData();
		},
		// 判断一级选项是否已选中
		isSelectFirst(id) {
			return this.selectPIds.includes(id);
		},
		// 判断二级选项是否已选中
		isSelectSecond(id) {
			return this.selectCIds.includes(id);
		},
		// 查询父类别下面选中的子类数量
		countByFirstId(firstId) {
			const count = this.selectSecond.reduce((acc, item) => {
				if (item.firstId === firstId) {
					return acc + 1;
				}
				return acc;
			}, 0);
			return count;
		},
		// 设置默认选中的数据
		setDefaultSelectData() {
			let first = this.value.map((obj) => {
				return {
					firstId: obj.firstId,
					firstName: obj.firstName
				};
			});
			let second = this.value.map((obj) => {
				return {
					firstId: obj.firstId,
					secondId: obj.secondId,
					secondName: obj.secondName
				};
			});
			this.selectFirst = first;
			this.selectSecond = second;

			// 初始化分类数据
			if (!uni.$u.test.isEmpty(this.selectSecond)) {
				let firstId = this.selectSecond[0].firstId;
				let secondId = this.selectSecond[0].secondId;
				let firstIndex = this.firstList.findIndex((item) => item.id === firstId);
				this.secondList = this.firstList[firstIndex].children;
			} else {
				this.secondList = this.firstList[0].children;
			}
			// 设置默认选中
			this.setDefaultSelect();
		},
		// 设置默认选中
		setDefaultSelect() {
			if (!uni.$u.test.isEmpty(this.selectSecond)) {
				let secondItem = this.selectSecond[0];
				this.$nextTick(() => {
					this.scrollFirstView = `p-cell${secondItem.firstId}`;
					this.scrollSecondView = `c-cell${secondItem.secondId}`;
				});
			} else if (!uni.$u.test.isEmpty(this.selectFirst)) {
				let fristItem = this.selectFirst[0];
				this.$nextTick(() => {
					this.scrollFirstView = `p-cell${secondItem.firstId}`;
				});
			}
		},
		// 点击一级Item
		onClickFirst(i, e) {
			if (this.level == 1) {
				// 一级联动
				if (this.maxNum == 1) {
					// 单选
					this.selectFirst = [
						{
							firstId: e.id,
							firstName: e.name
						}
					];
					this.onConfirm();
				} else {
					// 多选
					if (this.selectFirst.some((obj) => obj.firstId === e.id)) {
						// 存在点击的分类
						this.selectFirst = this.selectFirst.filter((obj) => obj.firstId !== e.id);
					} else {
						// 不存在
						if (this.selectFirst.length >= this.maxNum) {
							uni.showToast({
								title: `最多只能选择${this.maxNum}个`,
								icon: 'none'
							});
						} else {
							this.selectFirst.push({
								firstId: e.id,
								firstName: e.name
							});
						}
					}
				}
			} else if (this.level == 2) {
				// 二级联动
				this.firstSelectIndex = i;
				let firstIndex = this.firstList.findIndex((item) => item.id === e.id);
				this.secondList = this.firstList[firstIndex].children;
			}
		},
		// 点击二级Item
		onClickSecond(i, e) {
			if (this.level == 2) {
				// 二级联动
				if (this.maxNum == 1) {
					// 单选
					if (this.selectSecond.some((obj) => obj.secondId === e.id)) {
						// 存在点击的分类
						this.selectSecond = this.selectSecond.filter((obj) => obj.secondId !== e.id);

						let secondItems = this.selectSecond.filter((obj) => obj.firstId === e.pid);
						if (secondItems <= 0) {
							this.selectFirst = this.selectFirst.filter((obj) => obj.firstId !== e.pid);
						}
					} else {
						this.selectSecond = [
							{
								firstId: e.pid,
								secondId: e.id,
								secondName: e.name
							}
						];
						this.selectFirst = [
							{
								firstId: e.pid
							}
						];
					}
					this.onConfirm();
				} else {
					// 多选
					if (this.selectSecond.some((obj) => obj.secondId === e.id)) {
						// 存在点击的分类
						this.selectSecond = this.selectSecond.filter((obj) => obj.secondId !== e.id);

						let secondItems = this.selectSecond.filter((obj) => obj.firstId === e.pid);
						if (secondItems <= 0) {
							this.selectFirst = this.selectFirst.filter((obj) => obj.firstId !== e.pid);
						}
					} else {
						// 不存在
						if (this.selectSecond.length >= this.maxNum) {
							uni.showToast({
								title: `最多只能选择${this.maxNum}个`,
								icon: 'none'
							});
						} else {
							this.selectSecond.push({
								firstId: e.pid,
								secondId: e.id,
								secondName: e.name
							});
							if (!this.selectFirst.some((obj) => obj.firstId === e.pid)) {
								this.selectFirst.push({
									firstId: e.pid
								});
							}
						}
					}
				}
			}
		},
		// 删除分类
		deleteCategory(e) {
			if (this.level == 1) {
				this.selectFirst = this.selectFirst.filter((obj) => obj.firstId !== e.firstId);
			} else if (this.level == 2) {
				this.selectSecond = this.selectSecond.filter((obj) => obj.secondId !== e.secondId);

				let secondItems = this.selectSecond.filter((obj) => obj.firstId === e.firstId);
				if (secondItems <= 0) {
					this.selectFirst = this.selectFirst.filter((obj) => obj.firstId !== e.firstId);
				}
			}
		},
		// 确认
		onConfirm() {
			if (this.level == 1) {
				this.complete && this.complete(this.selectFirst);
				this.$emit('confirm', this.selectFirst);
				this.onCancel();
			} else if (this.level == 2) {
				this.complete && this.complete(this.selectSecond);
				this.$emit('confirm', this.selectSecond);
				this.onCancel();
			}
		},
		// 取消
		onCancel() {
			this.$emit('close');
		}
	}
};
</script>

<style lang="scss" scoped>
.row {
	display: flex;
	flex-direction: row;
}

.col {
	display: flex;
	flex-direction: column;
}

.popup-content {
	width: 100vw;
	background-color: #ffffff;
	display: flex;
	flex-direction: column;
	border-radius: 20rpx 20rpx 0 0;
}

.container {
	background: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
}

.title-view {
	width: 100%;
	height: 120rpx;
	justify-content: center;
	align-items: center;
	border-bottom: 12rpx solid #f8f8f8;

	.title {
		font-size: 36rpx;
		font-weight: 500;
		color: #333333;
	}
}

.list-box1 {
	width: 100%;
	height: 800rpx;
	justify-content: space-between;
	align-items: center;

	.list1 {
		width: 100%;
		height: 100%;
		background-color: #ffffff;
	}
}

.list-box2 {
	width: 100%;
	height: 800rpx;
	justify-content: space-between;
	align-items: center;

	.list1 {
		width: 360rpx;
		height: 100%;
		background-color: #f7f7f7;
	}

	.list2 {
		width: 500rpx;
		height: 100%;
		background-color: #ffffff;
	}
}

.list-cell {
	height: 100rpx;
	padding: 0 30rpx;
	justify-content: space-between;
	align-items: center;
	font-size: 30rpx;
	font-weight: 500;
	color: #333333;

	.check {
		width: 36rpx;
		height: 36rpx;
	}
}

.list-cell-selected {
	color: #6c68f0;
}

.list-cell-selected-bg {
	background-color: #ffffff;
}

.bottom-view {
	padding: 30rpx 30rpx 50rpx 30rpx;
	background: #ffffff;
	box-shadow: 0rpx 0rpx 13rpx 0rpx rgba(181, 181, 181, 0.3);

	.selected-box {
		flex-wrap: wrap;

		.item {
			padding: 5rpx 10rpx;
			margin-right: 30rpx;
			margin-bottom: 15rpx;
			border: 0.8px solid #6c68f0;
			background-color: #f4f6ff;
			font-size: 24rpx;
			border-radius: 7rpx;
			color: #6c68f0;
			position: relative;

			.item-close {
				width: 20rpx;
				height: 20rpx;
				position: absolute;
				right: -10rpx;
				top: -10rpx;
			}
		}
	}

	.confirm {
		height: 100rpx;
		justify-content: center;
		align-items: center;
		font-size: 34rpx;
		font-weight: 600;
		color: #ffffff;
		border-radius: 20rpx;
		background: #6c68f0;
	}
}
</style>
