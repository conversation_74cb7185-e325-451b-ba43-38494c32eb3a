<template>
	<view class="flex-col popup-city">
		<view class="flex title">请选择地区</view>
		<view class="flex wrap" v-if="!$u.test.isEmpty(addressList)">
			<view class="flex-col wrap__list" style="background: #f7f7f7">
				<scroll-view scroll-y class="flex-col wrap__list__scroll" :scroll-into-view="scrollProvinceView">
					<view
						:id="`p-item${index}`"
						class="flex wrap__list__province-item"
						:class="[index == provinceIndex ? 'wrap__list__province-item--selected' : '']"
						v-for="(item, index) in provinces"
						:key="index"
						@click="clickProvince(index, item)"
					>
						{{ item.name }}
					</view>
				</scroll-view>
			</view>
			<view class="flex-col wrap__list">
				<scroll-view scroll-y class="flex-col wrap__list__scroll" :scroll-into-view="scrollCityView">
					<view :id="`c-item${index}`" class="flex wrap__list__city-item" :class="[index == cityIndex ? 'wrap__list__city-item--selected' : '']" v-for="(item, index) in citys" :key="index" @click="clickCity(index, item)">
						{{ item.name }}
					</view>
				</scroll-view>
			</view>
			<view v-if="length == 3" class="wrap__list">
				<scroll-view scroll-y class="flex-co wrap__list__scrolll" :scroll-into-view="scrollAreaView">
					<view :id="`a-item${index}`" class="flex wrap__list__area-item" :class="[index == areaIndex ? 'wrap__list__area-item--selected' : '']" v-for="(item, index) in areas" :key="index" @click="clickArea(index, item)">
						{{ item.name }}
					</view>
				</scroll-view>
			</view>
		</view>
		<view class="flex-col placeholder" v-else>
			<u-loading-icon text="加载中"></u-loading-icon>
		</view>
	</view>
</template>

<script>
export default {
	name: 'popup-city',
	props: {
		show: {
			type: Boolean,
			default() {
				return false;
			}
		},
		// 联动长度[省，市，区]
		length: {
			type: Number,
			default() {
				return 3;
			}
		},
		// 默认选中的城市
		value: {
			type: Object,
			default() {
				return {
					provinceId: '',
					cityId: '',
					districtId: ''
				};
			}
		},
		// 完成事件
		complete: {
			type: Function,
			default: () => {}
		}
	},
	watch: {
		// 监听弹窗状态
		show: {
			handler(val) {
				if (val) {
					// #ifdef MP
					this.init();
					// #endif
				}
			}
		}
	},
	data() {
		return {
			/* 三级联动数据 */
			addressList: [],
			/* 当前省级别数据 */
			provinces: [],
			/* 当前市级别数据 */
			citys: [],
			/* 当前区级别数据 */
			areas: [],

			/* 选中的省索引 */
			provinceIndex: 0,
			/* 选中的市索引 */
			cityIndex: 0,
			/* 选中的区索引 */
			areaIndex: 0,

			/* 默认滚动到指定位置 */
			scrollProvinceView: '',
			scrollCityView: '',
			scrollAreaView: ''
		};
	},
	created() {
		this.init();
	},
	methods: {
		async loadCitys() {
			const cacheData = uni.getStorageSync('a-citys-link-list') ?? [];
			if (uni.$u.test.isEmpty(cacheData)) {
				try {
					const data = await uni.$u.http.get('/system/area/tree');
					this.addressList = data ?? [];
					uni.setStorage({
						key: 'a-citys-link-list',
						data: this.addressList
					});
				} catch (error) {
					console.error('获取城市数据时发生错误：', error);
					this.addressList = [];
				}
			} else {
				this.addressList = cacheData;
			}
		},
		async init() {
			// 异步加载城市数据
			await this.loadCitys();

			// 检查 addressList 是否为空
			if (this.addressList.length === 0) return;

			// 从 value 对象中解构出省、市、区的 ID
			const { provinceId, cityId, districtId } = this.value || {};

			// 查找并设置默认省份
			const defaultProvince = this.addressList.find((item) => String(item.id) === String(provinceId));
			this.provinces = this.addressList;
			this.provinceIndex = defaultProvince ? this.provinces.indexOf(defaultProvince) : 0;

			// 如果省份索引无效，则退出
			if (this.provinceIndex === -1) return;

			// 查找并设置默认城市
			const defaultCity = this.provinces[this.provinceIndex]?.children?.find((item) => String(item.id) === String(cityId));
			this.citys = this.provinces[this.provinceIndex].children || [];
			this.cityIndex = defaultCity ? this.citys.indexOf(defaultCity) : 0;

			// 如果城市索引无效，则退出
			if (this.cityIndex === -1) return;

			// 查找并设置默认区域
			const defaultArea = this.citys[this.cityIndex]?.children?.find((item) => String(item.id) === String(districtId));
			this.areas = this.citys[this.cityIndex].children || [];
			this.areaIndex = defaultArea ? this.areas.indexOf(defaultArea) : 0;

			// 使用 $nextTick 确保 DOM 更新完成后再设置滚动视图
			this.$nextTick(() => {
				this.scrollProvinceView = `p-item${this.provinceIndex}`;
				this.scrollCityView = `c-item${this.cityIndex}`;
				this.scrollAreaView = `a-item${this.areaIndex}`;
			});
		},

		// 省份点击事件
		clickProvince(index, data) {
			// 更新选中的省索引
			this.provinceIndex = index;
			// 重置市和区的索引
			this.cityIndex = 0;
			this.areaIndex = 0;
			// 更新市级数据和区级数据
			this.citys = data.children || [];
			this.areas = this.citys.length > 0 ? this.citys[0].children || [] : [];
		},

		// 城市点击事件
		clickCity(index, data) {
			// 更新选中的市索引
			this.cityIndex = index;
			// 更新区级数据
			this.areas = data.children || [];

			// 如果选择到城市后需要结束选择
			if (this.length === 2) {
				this.emitSelection();
			}
		},

		// 区域点击事件
		clickArea(index, data) {
			// 更新选中的区索引
			this.areaIndex = index;
			this.emitSelection();
		},

		// 发送选择结果事件
		emitSelection() {
			const province = this.provinces[this.provinceIndex];
			const city = this.citys[this.cityIndex];
			const area = this.areas[this.areaIndex] || {};

			const result = {
				provinceId: province.id,
				provinceName: province.name,
				cityId: city.id,
				cityName: city.name,
				districtId: area.id || '',
				districtName: area.name || ''
			};
			// 发送事件
			this.complete && this.complete(result);
			this.$emit('confirm', result);
			this.$emit('close');
		}
	}
};
</script>
<style lang="scss" scoped>
.popup-city {
	width: 750rpx;
	background-color: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
}

.title {
	width: 100%;
	height: 120rpx;
	font-size: 36rpx;
	font-weight: 500;
	color: #333333;
	justify-content: center;
	border-bottom: 12rpx solid #f8f8f8;
}

.wrap {
	flex: 1;
	justify-content: space-between;
	align-items: center;
	&__list {
		width: 100%;
		height: 800rpx;

		&__scroll {
			flex: 1;
			height: 100%;
		}

		&__province-item,
		&__city-item,
		&__area-item {
			flex: 1;
			height: 100rpx;
			padding: 0 30rpx;
			font-size: 30rpx;
			color: #333333;
		}

		&__province-item {
			font-weight: 600;
			&--selected {
				font-size: 33rpx;
				color: #444bf1;
				background-color: #ffffff;
			}
		}

		&__city-item {
			color: #666666;
			font-weight: 500;
			&--selected {
				font-size: 33rpx;
				color: #444bf1;
			}
		}

		&__area-item {
			color: #666666;
			font-weight: 500;
			&--selected {
				font-size: 33rpx;
				color: #444bf1;
			}
		}
	}
}

.placeholder {
	width: 100%;
	height: 800rpx;
	padding-top: 40rpx;
	align-items: center;
}
</style>
