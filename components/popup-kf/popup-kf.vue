<template>
	<view>
		<view class="flex-col container">
			<view class="flex-col wrap-kf">
				<image class="wrap-kf__top-img" src="/static/image/img_top_kf.png" mode="aspectFill"></image>
				<web-view v-if="iWebview.show" :webview-styles="{ width: '1px', height: '1px' }" :src="iWebview.url" @load="webviewLoad" @error="webviewError"></web-view>
				<text class="wrap-kf__title" v-if="!$u.test.isEmpty(uiData.title)">{{ uiData.title }}</text>
				<text class="wrap-kf__subtitle" v-if="!$u.test.isEmpty(uiData.subtitle)">{{ uiData.subtitle }}</text>
				<view class="flex-col wrap-kf__content">
					<view class="flex-row wrap-kf__content__item" v-for="(item, index) in items" :key="index" @click="onItemClick(item)">
						<image class="wrap-kf__content__item__ic" :src="item.icon" mode="aspectFit"></image>
						<view class="flex-col wrap-kf__content__item__text">
							<text class="wrap-kf__content__item__text__title">{{ item.title }}</text>
							<text class="wrap-kf__content__item__text__desc">{{ item.subtitle }}</text>
						</view>
						<view class="flex-row wrap-kf__content__item__button" :style="{ background: item.color }">{{ item.confirmText }}</view>
					</view>
				</view>

				<!-- 入驻接单APP -->
				<view class="flex-row wrap-kf__app" v-if="!$u.test.isEmpty(appData) && appData.show" @click="onAppClick()">
					<image class="wrap-kf__app__ic" :src="appData.icon" mode="aspectFit"></image>
					<view class="flex-col wrap-kf__app__text">
						<text class="wrap-kf__app__text__title">入驻平台接单</text>
						<text class="wrap-kf__app__text__desc">家政服务实时更新</text>
					</view>
					<view class="flex-row wrap-kf__app__button">{{ appData.btn }}</view>
				</view>
			</view>
			<image class="close" src="/static/image/ic_close_w.png" mode="aspectFill" @click="onClose"></image>
		</view>
	</view>
</template>

<script>
import { iWxMiniProgram, callTel, openMiniWx, openOnlinkKF, copyWechat, openOtherApp } from '@/common/utils';
import { mapState } from 'vuex';
export default {
	name: 'popup-kf',
	props: {
		// 客服类型 （kf: 接单客服  jmkf:加盟客服 jskf:平台开发）
		kfType: {
			type: String,
			default: 'kf'
		}
	},
	data() {
		return {
			// 客服项
			items: [],
			// 控制加载客服助手的webview（因为客服助手是个链接url,通过url打开微信的添加好友界面，故使用此值隐藏和展示web-view来跳转微信）
			iWebview: {
				show: false,
				url: ''
			}
		};
	},
	computed: {
		...mapState(['sysConf']),
		uiData() {
			let info = { title: '', subtitle: '' };
			if (this.kfType == 'kf') {
				info = { subtitle: '对订单有疑问？可以咨询客服哦' };
			} else if (this.kfType == 'jskf') {
				info = { title: '平台开发', subtitle: '成熟稳定盈利行业App平台OEM定制' };
			}
			return info;
		},
		appData() {
			// #ifdef APP
			return {
				name: this.sysConf?.relatedApp?.app_name || '',
				icon: this.sysConf?.relatedApp?.app_icon || '',
				btn: '下载App',
				package: this.sysConf?.relatedApp?.package || '',
				appUrl: this.sysConf?.relatedApp?.download || '',
				show: this.sysConf?.relatedApp?.is_disabled == 0
			};
			// #endif

			// #ifdef MP
			return {
				name: this.sysConf?.relatedMiniProgram?.app_name || '',
				icon: this.sysConf?.relatedMiniProgram?.app_icon || '',
				btn: '打开程序',
				miniId: this.sysConf?.relatedMiniProgram?.program_id || '',
				show: this.sysConf?.relatedMiniProgram?.is_disabled == 0
			};
			// #endif
			return {};
		}
	},
	watch: {
		// 监听弹窗状态
		show: {
			handler(newVal, oldVal) {
				this.init();
			},
			immediate: true
		}
	},
	methods: {
		init() {
			const apiMap = { kf: '/system/getCustomerConf', jmkf: '', jskf: '' };
			uni.$u.http
				.post(apiMap[this.kfType] || '')
				.then((res) => {
					var temps = [];
					if (res.is_robot) {
						temps.push({
							type: 'wx24',
							color: '#FF8452',
							icon: '/static/image/ic_kf_24.png',
							title: '在线咨询',
							subtitle: '7x24h咨询',
							confirmText: '在线咨询',
							data: res
						});
					}
					if (res.is_phone) {
						temps.push({
							type: 'tel',
							color: '#444BF1',
							icon: '/static/image/ic_kf_tel.png',
							title: '电话客服',
							subtitle: res.mobile,
							confirmText: '拨打电话',
							data: res
						});
					}
					if (res.is_wechat) {
						temps.push({
							type: 'wx',
							color: '#31CEC6',
							icon: '/static/image/ic_kf_wx.png',
							title: '微信咨询',
							subtitle: uni.$u.test.isEmpty(res.wx_com_str) ? '识别微信添加' : res.wx_com_str,
							confirmText: '点击添加',
							data: res
						});
					}
					this.items = temps;
				})
				.catch((err) => {});
		},
		onClose() {
			this.$emit('close'), {};
		},
		onItemClick(item) {
			// 打开小程序
			const launchMiniProgram = (miniId) => {
				openMiniWx(iWxMiniProgram(this.kfType, miniId));
			};
			// 打开在线客服
			const startOnlineService = (comId, comUrl) => {
				openOnlinkKF({ wxId: comId, wxUrl: comUrl });
			};
			// 打开webview
			const displayWebView = (url) => {
				uni.showLoading({ title: '正在打开' });
				this.iWebview = { show: true, url: url };
				setTimeout(() => {
					uni.hideLoading();
					this.iWebview.show = false;
				}, 2000);
			};
			// 复制微信
			const copyWechatNumber = (wx) => {
				copyWechat({ wx: wx });
			};

			switch (item.type) {
				case 'tel':
					// 拨打电话
					callTel({
						tel: item.data?.mobile
					});
					break;
				case 'wx24':
					// 打开7x24客服的方式  0-小程序  1-在线客服 2-客服助手  3-在线客服【通过url内置webview】
					switch (parseInt(item.data?.online_open_type)) {
						case 0:
							// 打开小程序
							var miniId = item.data?.online_gh_program_id;
							// #ifdef MP
							miniId = item.data?.online_program_appid;
							// #endif
							launchMiniProgram(miniId);
							break;
						case 1:
							// 打开在线客服
							startOnlineService(item.data?.online_com_id, item.data?.online_com_url);
							break;
						case 2:
							// 直接打开客服添加【客服助手】
							displayWebView(item.data?.online_customer_url);
						case 3:
							// 在线客服【通过url内置webview】
							displayWebView(item.data?.online_com_url);
							break;
						case 4:
							// 打开在线客服（第三方在线客服）
							this.onJump({ url: '/pages/other/web-view', params: { url: encodeURI(item.data?.online_thrid_url) } });
							break;
						case 5:
							// 手动复制微信
							copyWechatNumber(item.data?.online_number);
							break;
					}
				case 'wx':
					// 打开微信客服的方式  0-小程序  1-在线客服 2-客服助手  3-在线客服【通过url内置webview】
					switch (parseInt(item.data?.wx_open_type)) {
						case 0:
							// 打开小程序
							var miniId = item.data?.gh_program_id;
							// #ifdef MP
							miniId = item.data?.program_appid;
							// #endif
							launchMiniProgram(miniId);
							break;
						case 1:
							// 打开在线客服
							startOnlineService(item.data?.wx_com_id, item.data?.wx_com_url);
							break;
						case 2:
							// 直接打开客服添加【客服助手】
							displayWebView(item.data?.wx_customer_url);
							break;
						case 3:
							// 在线客服【通过url内置webview】
							displayWebView(item.data?.wx_com_url);
							break;
						case 4:
							// 打开在线客服（第三方在线客服）
							this.onJump({ url: '/pages/other/web-view', params: { url: encodeURI(item.data?.thrid_url) } });
							break;
						case 5:
							// 手动复制微信
							copyWechatNumber(item.data?.wx_number);
							break;
					}
					break;
			}
			this.onClose();
		},
		onAppClick() {
			// #ifdef APP
			openOtherApp({
				package: this.appData?.package,
				appUrl: this.appData?.download
			});
			// #endif

			// #ifdef MP
			openMiniWx({ miniId: this.appData?.miniId, path: '/pages/home/<USER>' });
			// #endif
		},
		// webview加载成功
		webviewLoad() {
			uni.hideLoading();
			setTimeout(() => {
				this.iWebview.show = false;
				this.onClose();
			}, 1000);
		},
		// webview加载失败
		webviewError() {
			uni.hideLoading();
			this.iWebview.show = false;
			uni.showToast({
				icon: 'none'
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.container {
	align-items: center;
}

.wrap-kf {
	width: 560rpx;
	border-radius: 44rpx;
	background-color: #ffffff;
	overflow: hidden;
	&__top-img {
		width: 560rpx;
		height: 245rpx;
	}
	&__title {
		margin-top: 30rpx;
		font-weight: 800;
		font-size: 36rpx;
		color: #333333;
		text-align: center;
	}
	&__subtitle {
		margin-top: 20rpx;
		font-size: 30rpx;
		color: #333333;
		text-align: center;
	}
	&__content {
		flex: 1;
		padding: 30rpx;
		&__item {
			flex: 1;
			padding: 30rpx 15rpx;
			border-bottom: 0.8px solid #f0f0f0;
			&:last-child {
				border: none;
			}
			&__ic {
				width: 36rpx;
				height: 36rpx;
				margin-top: 5rpx;
			}
			&__text {
				margin-left: 30rpx;
				&__title {
					font-weight: bold;
					font-size: 30rpx;
					color: #333333;
				}
				&__desc {
					margin-top: 10rpx;
					font-size: 24rpx;
					color: #666666;
				}
			}
			&__button {
				width: 160rpx;
				height: 60rpx;
				margin-left: auto;
				background: #444BF1;
				border-radius: 30rpx;
				font-weight: bold;
				font-size: 26rpx;
				color: #ffffff;
				align-items: center;
				justify-content: center;
			}
		}
	}

	&__app {
		padding: 30rpx 40rpx 30rpx 15rpx;
		background: #f0eeff;
		&__ic {
			width: 80rpx;
			height: 80rpx;
			margin-top: 5rpx;
		}
		&__text {
			margin-left: 30rpx;
			&__title {
				font-weight: bold;
				font-size: 30rpx;
				color: #333333;
			}
			&__desc {
				margin-top: 10rpx;
				font-size: 24rpx;
				color: #666666;
			}
		}
		&__button {
			width: 160rpx;
			height: 60rpx;
			margin-left: auto;
			background: #444BF1;
			border-radius: 30rpx;
			font-weight: bold;
			font-size: 26rpx;
			color: #ffffff;
			align-items: center;
			justify-content: center;
		}
	}
}

.close {
	width: 50rpx;
	height: 50rpx;
	margin-top: 60rpx;
}
</style>
