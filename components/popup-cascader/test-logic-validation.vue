<template>
	<view class="test-container">
		<text class="title">multiple、max、level 逻辑验证测试</text>
		
		<!-- 测试按钮组 -->
		<view class="button-group">
			<button @click="testSingleAutoConfirm">单选自动确认 (multiple=false, max=1)</button>
			<button @click="testSingleManualConfirm">单选手动确认 (multiple=false, max=3)</button>
			<button @click="testMultipleSelect">真正多选 (multiple=true, max=3)</button>
			<button @click="testLevel1">1级测试 (level=1)</button>
			<button @click="testLevel3">3级测试 (level=3)</button>
		</view>
		
		<!-- 当前配置显示 -->
		<view class="config-section">
			<text class="section-title">当前配置：</text>
			<text class="config-text">multiple: {{ currentConfig.multiple }}</text>
			<text class="config-text">max: {{ currentConfig.max }}</text>
			<text class="config-text">level: {{ currentConfig.level }}</text>
			<text class="config-text">预期行为: {{ expectedBehavior }}</text>
		</view>
		
		<!-- 弹窗组件 -->
		<popup-cascader
			:show="showPopup"
			:ident="currentConfig.ident"
			:title="currentConfig.title"
			:level="currentConfig.level"
			:multiple="currentConfig.multiple"
			:max="currentConfig.max"
			:display-mode="currentConfig.displayMode"
			:menu-width="currentConfig.menuWidth"
			:show-all-levels="currentConfig.showAllLevels"
			:options="testData"
			:model-value="currentConfig.modelValue"
			@confirm="handleConfirm"
			@close="handleClose"
		/>
		
		<!-- 结果显示 -->
		<view class="result-section" v-if="lastResult">
			<text class="section-title">最后选择结果：</text>
			<text class="result-text">{{ JSON.stringify(lastResult, null, 2) }}</text>
		</view>
	</view>
</template>

<script>
import PopupCascader from './popup-cascader.vue';

export default {
	name: 'TestLogicValidation',
	components: {
		PopupCascader
	},
	data() {
		return {
			showPopup: false,
			lastResult: null,
			currentConfig: {
				ident: 'test',
				title: '测试',
				level: 3,
				multiple: false,
				max: 1,
				displayMode: 'list',
				menuWidth: null,
				showAllLevels: false,
				modelValue: []
			},
			testData: [
				{
					value: 1,
					label: '北京市',
					children: [
						{
							value: 11,
							label: '朝阳区',
							children: [
								{ value: 111, label: '三里屯街道' },
								{ value: 112, label: '建外街道' }
							]
						},
						{
							value: 12,
							label: '海淀区',
							children: [
								{ value: 121, label: '中关村街道' },
								{ value: 122, label: '学院路街道' }
							]
						}
					]
				},
				{
					value: 2,
					label: '上海市',
					children: [
						{
							value: 21,
							label: '浦东新区',
							children: [
								{ value: 211, label: '陆家嘴街道' },
								{ value: 212, label: '花木街道' }
							]
						},
						{
							value: 22,
							label: '黄浦区',
							children: [
								{ value: 221, label: '南京东路街道' },
								{ value: 222, label: '外滩街道' }
							]
						}
					]
				}
			]
		};
	},
	computed: {
		expectedBehavior() {
			const { multiple, max, level } = this.currentConfig;
			
			if (!multiple && max === 1) {
				return '单选，选择后自动确认，不显示底部按钮';
			} else if (!multiple && max > 1) {
				return '单选，但需要手动确认，显示底部按钮';
			} else if (multiple && max > 1) {
				return '多选，可选择多个项目，显示底部按钮';
			} else {
				return '配置异常';
			}
		}
	},
	methods: {
		// 单选自动确认测试
		testSingleAutoConfirm() {
			this.currentConfig = {
				ident: 'single-auto',
				title: '单选自动确认测试',
				level: 3,
				multiple: false,
				max: 1,
				displayMode: 'list',
				menuWidth: null,
				showAllLevels: false,
				modelValue: []
			};
			this.showPopup = true;
		},
		
		// 单选手动确认测试
		testSingleManualConfirm() {
			this.currentConfig = {
				ident: 'single-manual',
				title: '单选手动确认测试',
				level: 3,
				multiple: false,
				max: 3, // max>1 但 multiple=false，应该当做单选处理
				displayMode: 'list',
				menuWidth: null,
				showAllLevels: false,
				modelValue: []
			};
			this.showPopup = true;
		},
		
		// 真正多选测试
		testMultipleSelect() {
			this.currentConfig = {
				ident: 'multiple',
				title: '真正多选测试',
				level: 3,
				multiple: true,
				max: 3,
				displayMode: 'list',
				menuWidth: null,
				showAllLevels: false,
				modelValue: []
			};
			this.showPopup = true;
		},
		
		// 1级测试
		testLevel1() {
			this.currentConfig = {
				ident: 'level-1',
				title: '1级测试',
				level: 1,
				multiple: false,
				max: 1,
				displayMode: 'list',
				menuWidth: null,
				showAllLevels: false,
				modelValue: []
			};
			this.showPopup = true;
		},
		
		// 3级测试
		testLevel3() {
			this.currentConfig = {
				ident: 'level-3',
				title: '3级测试',
				level: 3,
				multiple: true,
				max: 2,
				displayMode: 'list',
				menuWidth: null,
				showAllLevels: false,
				modelValue: []
			};
			this.showPopup = true;
		},
		
		handleConfirm(result) {
			console.log('选择结果:', result);
			this.lastResult = result;
			this.showPopup = false;
		},
		
		handleClose() {
			this.showPopup = false;
		}
	}
};
</script>

<style lang="scss" scoped>
.test-container {
	padding: 40rpx;
	background: #f5f5f5;
	min-height: 100vh;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	text-align: center;
	margin-bottom: 40rpx;
	color: #333;
}

.button-group {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	margin-bottom: 40rpx;
	
	button {
		padding: 20rpx;
		background: #444bf1;
		color: white;
		border: none;
		border-radius: 8rpx;
		font-size: 28rpx;
		text-align: left;
	}
}

.config-section, .result-section {
	background: white;
	padding: 30rpx;
	border-radius: 12rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.config-text, .result-text {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 10rpx;
	display: block;
	line-height: 1.5;
}

.result-text {
	font-family: monospace;
	background: #f8f8f8;
	padding: 20rpx;
	border-radius: 8rpx;
	white-space: pre-wrap;
}
</style>
