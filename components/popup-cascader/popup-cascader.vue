<template>
	<view class="el-cascader-popup">
		<view class="el-cascader">
			<!-- 标题栏 -->
			<view class="el-cascader__header">
				<text class="el-cascader__title">{{ title }}</text>
				<view class="el-cascader__close" @click="handleClose">
					<image src="/static/image/ic_close.png" mode="aspectFit"></image>
				</view>
			</view>

			<!-- 级联选择面板 -->
			<view class="el-cascader__panel" :style="{ height: panelHeight + 'rpx' }">
				<!-- 动态渲染所有级别的菜单 -->
				<scroll-view
					v-for="menuConfig in menuConfigs"
					:key="`menu-${menuConfig.level}`"
					scroll-y
					class="el-cascader__menu"
					:class="{
						'el-cascader__menu--first': menuConfig.isFirst,
						'el-cascader__menu--grid': menuConfig.displayMode === 'grid'
					}"
					:style="{ width: menuConfig.width + 'rpx' }"
					:scroll-into-view="scrollIntoView[menuConfig.level]"
				>
					<!-- 列表模式 -->
					<view v-if="menuConfig.displayMode === 'list'" class="el-cascader__nodes">
						<view
							:id="`level-${menuConfig.level}-${item[valueKey]}`"
							class="el-cascader__node"
							:class="{
								'is-active': isSelected(item),
								'is-disabled': item[disabledKey],
								'is-current-click': isCurrentClick(menuConfig.level, index)
							}"
							v-for="(item, index) in menuConfig.data"
							:key="item[valueKey]"
							@click="handleNodeClick(item, menuConfig.level, index)"
						>
							<text class="el-cascader__label">
								{{ item[labelKey] }}
								<text v-if="multiple && getChildrenCount(item, menuConfig.level) > 0" class="el-cascader__count">({{ getChildrenCount(item, menuConfig.level) }})</text>
							</text>
							<image v-if="shouldShowIcon(menuConfig.level)" class="el-cascader__icon" :src="isSelected(item) ? checkedIcon : uncheckedIcon" mode="aspectFit"></image>
						</view>
					</view>

					<!-- 网格模式 -->
					<view v-else class="el-cascader__grid">
						<view
							:id="`level-${menuConfig.level}-${item[valueKey]}`"
							class="el-cascader__grid-item"
							:class="{
								'is-active': isSelected(item),
								'is-disabled': item[disabledKey],
								'is-current-click': isCurrentClick(menuConfig.level, index)
							}"
							v-for="(item, index) in menuConfig.data"
							:key="item[valueKey]"
							@click="handleNodeClick(item, menuConfig.level, index)"
						>
							<text class="el-cascader__grid-label">{{ item[labelKey] }}</text>
						</view>
					</view>
				</scroll-view>
			</view>

			<!-- 已选择项展示和确认按钮 -->
			<view v-if="shouldShowFooter" class="el-cascader__footer">
				<view class="el-cascader__tags">
					<view class="el-cascader__tag" v-for="item in selectedNodes" :key="getNodeKey(item)">
						{{ getNodeLabel(item) }}
						<image src="/static/image/ic_tag_delete.png" @click="handleRemoveTag(item)" mode="aspectFit"></image>
					</view>
				</view>
				<view class="el-cascader__confirm" @click="handleConfirm">
					{{ confirmText }}
				</view>
			</view>
		</view>
		<u-safe-bottom :customStyle="{ background: '#FFF' }"></u-safe-bottom>
	</view>
</template>

<script>
/**
 * Element Plus 风格级联选择器组件
 * @description 融合多种级联选择功能的通用组件，支持单选/多选、多级联动、末级回显等功能
 * @version 1.0.0
 */
export default {
	name: 'ElCascaderPopup',
	props: {
		// 弹窗标识
		ident: {
			type: String,
			default: 'cascader'
		},
		// 弹窗显示状态
		show: {
			type: Boolean,
			default: false
		},
		// 弹窗标题
		title: {
			type: String,
			default: '请选择'
		},
		// 级联层级数量
		level: {
			type: Number,
			default: 2,
			validator: (value) => value > 0 && value <= 10
		},
		// 是否支持多选
		multiple: {
			type: Boolean,
			default: false
		},
		// 最大选择数量（多选时有效）
		max: {
			type: Number,
			default: 1,
			validator: (value) => value > 0
		},
		// 显示模式：支持字符串（所有级别相同）或数组（每级别独立设置）
		displayMode: {
			type: [String, Array],
			default: 'list',
			validator: (value) => {
				if (typeof value === 'string') {
					return ['list', 'grid'].includes(value);
				}
				if (Array.isArray(value)) {
					return value.every(mode => ['list', 'grid'].includes(mode));
				}
				return false;
			}
		},
		// 菜单宽度：支持数组（每级别独立设置宽度，单位：rpx）
		menuWidth: {
			type: [Number, Array],
			default: null, // 改为 null，让智能默认逻辑生效
			validator: (value) => {
				if (value === null) {
					return true; // 允许 null 值
				}
				if (typeof value === 'number') {
					return value > 0;
				}
				if (Array.isArray(value)) {
					return value.every(width => typeof width === 'number' && width > 0);
				}
				return false;
			}
		},
		// 是否展示全部级别（true: 默认展开所有级别，false: 点击展开下一级）
		showAllLevels: {
			type: Boolean,
			default: false
		},
		// 级联数据源
		options: {
			type: Array,
			default: () => [],
			validator: (value) => {
				if (!Array.isArray(value)) return false;
				// 简单验证数据结构
				return value.every((item) => item && typeof item === 'object' && (item.value !== undefined || item.id !== undefined) && (item.label !== undefined || item.name !== undefined));
			}
		},
		// 数据源字段配置
		props: {
			type: Object,
			default: () => ({
				value: 'id',
				label: 'name',
				children: 'children',
				disabled: 'disabled'
			})
		},
		// 选中值列表（支持末级编号传入）
		modelValue: {
			type: Array,
			default: () => []
		},
		// 是否严格模式（只能选择叶子节点）
		checkStrictly: {
			type: Boolean,
			default: true
		},
		// 是否返回完整路径
		emitPath: {
			type: Boolean,
			default: true
		},
		// 面板高度（单位：rpx）
		panelHeight: {
			type: Number,
			default: 800,
			validator: (value) => value > 0
		},
		// 完成选择回调函数
		complete: {
			type: Function,
			default: () => {}
		}
	},
	data() {
		return {
			// 当前激活的各级菜单数据
			activeMenus: [],
			// 当前选中的节点列表
			selectedNodes: [],
			// 滚动视图定位ID列表
			scrollIntoView: [],
			// 当前选中的路径索引列表
			activeIndexes: [],
			// 每一级别当前点击的索引（用于高亮显示）
			currentClickIndexes: []
		};
	},
	computed: {
		// 数据字段映射 - 值字段键名
		valueKey() {
			return this.props.value || 'value';
		},

		// 数据字段映射 - 标签字段键名
		labelKey() {
			return this.props.label || 'label';
		},

		// 数据字段映射 - 子项字段键名
		childrenKey() {
			return this.props.children || 'children';
		},

		// 数据字段映射 - 禁用字段键名
		disabledKey() {
			return this.props.disabled || 'disabled';
		},

		// 选中状态图标路径
		checkedIcon() {
			return this.multiple ? '/static/image/ic_checkbox_sel.png' : '/static/image/ic_check_sel.png';
		},

		// 未选中状态图标路径
		uncheckedIcon() {
			return this.multiple ? '/static/image/ic_checkbox_nor.png' : '/static/image/ic_check_nor.png';
		},

		// 确认按钮文本
		confirmText() {
			// 真正的多选模式（multiple=true 且 max>1）
			if (this.multiple && this.max > 1) {
				return `确认选择（${this.selectedNodes.length}/${this.max}）`;
			}
			// 单选模式但需要手动确认（multiple=false 但 max>1）
			if (!this.multiple && this.max > 1) {
				return `确认选择（${this.selectedNodes.length}/1）`;
			}
			return '确认选择';
		},

		// 是否显示底部区域
		shouldShowFooter() {
			// 真正的多选模式（multiple=true）
			if (this.multiple) {
				return this.max > 1 || this.selectedNodes.length > 0;
			}
			// 单选模式：当max>1时显示（需要手动确认），max=1时不显示（自动确认）
			// 注意：即使 multiple=false 但 max>1，也当做单选处理，只是需要手动确认
			return this.max > 1 && this.selectedNodes.length > 0;
		},

		// 获取指定级别的显示模式
		getDisplayMode() {
			return (level) => {
				if (typeof this.displayMode === 'string') {
					return this.displayMode;
				}
				if (Array.isArray(this.displayMode)) {
					return this.displayMode[level] || 'list';
				}
				return 'list';
			};
		},

		// 获取指定级别的菜单宽度
		getMenuWidth() {
			return (level) => {
				// 如果没有设置 menuWidth 或为 null，使用智能默认值
				if (this.menuWidth === null || this.menuWidth === undefined) {
					return this.getDefaultWidth(level);
				}
				// 如果是数字，所有级别使用相同宽度
				if (typeof this.menuWidth === 'number') {
					return this.menuWidth;
				}
				// 如果是数组，使用对应级别的宽度，没有则使用默认值
				if (Array.isArray(this.menuWidth)) {
					return this.menuWidth[level] || this.getDefaultWidth(level);
				}
				// 兜底使用默认值
				return this.getDefaultWidth(level);
			};
		},



		// 动态生成菜单列表配置
		menuConfigs() {
			const configs = [];

			// 第一级菜单（根级别）
			configs.push({
				level: 0,
				data: this.options,
				displayMode: this.getDisplayMode(0),
				width: this.getMenuWidth(0),
				isFirst: true
			});

			// 后续级别菜单
			for (let i = 0; i < this.activeMenus.length && i < this.level - 1; i++) {
				configs.push({
					level: i + 1,
					data: this.activeMenus[i],
					displayMode: this.getDisplayMode(i + 1),
					width: this.getMenuWidth(i + 1),
					isFirst: false
				});
			}

			return configs;
		}
	},

	watch: {
		// 监听弹窗显示状态变化
		show(newVal) {
			if (newVal) {
				this.init();
			}
		},

		// 监听选中值变化
		modelValue: {
			handler() {
				this.initSelectedNodes();
			},
			deep: true
		}
	},
	created() {
		this.init();
	},

	methods: {
		// 获取默认宽度
		getDefaultWidth(level) {
			// 一级默认撑满
			if (this.level === 1) {
				return 750;
			}
			// 两级别：第一级250，第二级200
			if (this.level === 2) {
				return level === 0 ? 250 : 200;
			}
			// 其他情况默认250
			return 250;
		},

		// 初始化组件
		init() {
			this.initSelectedNodes();
			this.initActiveMenus();
			this.updateScrollIntoView();
		},

		// 初始化选中节点列表
		initSelectedNodes() {
			this.selectedNodes = [];
			if (!this.modelValue || this.modelValue.length === 0) return;

			// 支持末级编号传入，自动构建完整路径
			this.modelValue.forEach((value) => {
				try {
					if (typeof value === 'string' || typeof value === 'number') {
						// 单个值，查找完整路径
						const path = this.findPathByValue(value, this.options);
						if (path.length > 0) {
							const selectedNode = this.createSelectedNode(path);
							if (selectedNode) {
								this.selectedNodes.push(selectedNode);
							}
						}
					} else if (Array.isArray(value)) {
						// 路径数组
						const path = this.findPathByArray(value, this.options);
						if (path.length > 0) {
							const selectedNode = this.createSelectedNode(path);
							if (selectedNode) {
								this.selectedNodes.push(selectedNode);
							}
						}
					} else if (value && typeof value === 'object') {
						// 对象格式，验证必要字段
						if (value[this.valueKey] && value[this.labelKey]) {
							this.selectedNodes.push(value);
						}
					}
				} catch (error) {
					console.warn('[ElCascaderPopup] 初始化选中项失败:', error, value);
				}
			});
		},

		// 根据值查找完整路径
		findPathByValue(targetValue, data, currentPath = []) {
			if (!Array.isArray(data) || data.length === 0) return [];

			for (const item of data) {
				if (!item || typeof item !== 'object') continue;

				const newPath = [...currentPath, item];
				if (item[this.valueKey] === targetValue) {
					return newPath;
				}
				if (item[this.childrenKey] && Array.isArray(item[this.childrenKey]) && item[this.childrenKey].length > 0) {
					const result = this.findPathByValue(targetValue, item[this.childrenKey], newPath);
					if (result.length > 0) return result;
				}
			}
			return [];
		},

		// 根据值数组查找完整路径
		findPathByArray(valueArray, data) {
			if (!Array.isArray(valueArray) || !Array.isArray(data)) return [];

			let currentData = data;
			const path = [];

			for (const value of valueArray) {
				if (!Array.isArray(currentData)) return [];
				const item = currentData.find((item) => item && item[this.valueKey] === value);
				if (!item) return [];
				path.push(item);
				currentData = item[this.childrenKey] || [];
			}
			return path;
		},

		// 创建选中节点对象
		createSelectedNode(path) {
			if (!path || path.length === 0) {
				console.warn('[ElCascaderPopup] 创建选中节点失败：路径为空');
				return null;
			}

			const lastItem = path[path.length - 1];
			if (!lastItem) {
				console.warn('[ElCascaderPopup] 创建选中节点失败：最后一项为空');
				return null;
			}

			const selectedNode = {
				[this.valueKey]: lastItem[this.valueKey],
				[this.labelKey]: lastItem[this.labelKey],
				path: path.map((item) => item[this.valueKey]),
				pathLabels: path.map((item) => item[this.labelKey]),
				level: path.length - 1
			};

			console.log('[ElCascaderPopup] 创建选中节点:', selectedNode);
			return selectedNode;
		},

		// 初始化激活菜单列表
		initActiveMenus() {
			this.activeMenus = [];
			this.activeIndexes = [];
			this.currentClickIndexes = []; // 清空点击高亮索引

			// 如果有选中项，根据第一个选中项初始化菜单
			if (this.selectedNodes.length > 0) {
				const firstSelected = this.selectedNodes[0];
				this.buildActiveMenusFromPath(firstSelected.path);
			} else if (this.options.length > 0 && this.level > 1) {
				if (this.showAllLevels) {
					// 展示全部级别模式：递归展开所有级别的第一项
					this.expandAllLevels();
				} else {
					// 传统模式：只展开第一项的子级
					const firstOption = this.options[0];
					if (firstOption[this.childrenKey] && firstOption[this.childrenKey].length > 0) {
						this.activeMenus.push(firstOption[this.childrenKey]);
						this.activeIndexes.push(0);
						// 设置第一级的默认点击高亮
						this.$set(this.currentClickIndexes, 0, 0);
					}
				}
			}
		},

		// 展开所有级别（showAllLevels 模式）
		expandAllLevels() {
			let currentData = this.options;
			let currentLevel = 0;

			// 递归展开每一级的第一项
			while (currentData && currentData.length > 0 && currentLevel < this.level - 1) {
				const firstItem = currentData[0];

				// 设置当前级别的激活索引和点击高亮
				this.activeIndexes[currentLevel] = 0;
				this.$set(this.currentClickIndexes, currentLevel, 0);

				// 如果有子级，添加到激活菜单中
				if (firstItem[this.childrenKey] && firstItem[this.childrenKey].length > 0) {
					this.activeMenus[currentLevel] = firstItem[this.childrenKey];
					currentData = firstItem[this.childrenKey];
					currentLevel++;
				} else {
					// 没有子级，停止展开
					break;
				}
			}
		},

		// 从指定节点开始展开后续所有级别（showAllLevels 模式）
		expandAllLevelsFromNode(nodeData, startLevel) {
			let currentData = nodeData;
			let currentLevel = startLevel;

			// 递归展开每一级的第一项
			while (currentData && currentData.length > 0 && currentLevel < this.level - 1) {
				const firstItem = currentData[0];

				// 设置当前级别的激活索引和点击高亮
				this.activeIndexes[currentLevel] = 0;
				this.$set(this.currentClickIndexes, currentLevel, 0);

				// 如果有子级，添加到激活菜单中
				if (firstItem[this.childrenKey] && firstItem[this.childrenKey].length > 0) {
					this.activeMenus[currentLevel] = firstItem[this.childrenKey];
					currentData = firstItem[this.childrenKey];
					currentLevel++;
				} else {
					// 没有子级，停止展开
					break;
				}
			}
		},

		// 根据路径构建激活菜单列表
		buildActiveMenusFromPath(path) {
			let currentData = this.options;

			for (let i = 0; i < path.length - 1; i++) {
				const value = path[i];
				const index = currentData.findIndex((item) => item[this.valueKey] === value);
				if (index !== -1) {
					this.activeIndexes[i] = index;
					const item = currentData[index];
					if (item[this.childrenKey] && item[this.childrenKey].length > 0) {
						this.activeMenus[i] = item[this.childrenKey];
						currentData = item[this.childrenKey];
					}
				}
			}
		},

		// 更新滚动视图定位
		updateScrollIntoView() {
			this.$nextTick(() => {
				this.scrollIntoView = [];
				if (this.selectedNodes.length > 0) {
					const firstSelected = this.selectedNodes[0];
					firstSelected.path.forEach((value, index) => {
						this.scrollIntoView[index] = `level-${index}-${value}`;
					});
				}
			});
		},

		// 处理节点点击事件
		handleNodeClick(node, level, index) {
			// 验证参数
			if (!node || typeof node !== 'object') {
				console.warn('[ElCascaderPopup] 无效的节点数据:', node);
				return;
			}

			if (node[this.disabledKey]) return;

			// 记录当前点击的索引（用于高亮显示）
			this.$set(this.currentClickIndexes, level, index);

			// 更新激活菜单
			this.updateActiveMenus(node, level, index);

			// 处理选择逻辑
			// 注意：只有当 multiple=true 时才是真正的多选，否则都是单选
			if (this.multiple) {
				this.handleMultipleSelect(node, level);
			} else {
				// multiple=false 时，无论 max 是多少，都当做单选处理
				this.handleSingleSelect(node, level);
			}
		},

		// 更新激活菜单列表
		updateActiveMenus(node, level, index) {
			// 更新当前层级的选中索引
			this.activeIndexes[level] = index;

			// 清除后续层级
			this.activeMenus.splice(level);
			this.activeIndexes.splice(level + 1);

			// 清除后续层级的点击高亮索引
			this.currentClickIndexes.splice(level + 1);

			// 如果有子级且不是最后一级，添加子级数据
			if (node[this.childrenKey] && node[this.childrenKey].length > 0 && level < this.level - 1) {
				this.activeMenus[level] = node[this.childrenKey];

				// 如果是 showAllLevels 模式，需要继续展开后续所有级别
				if (this.showAllLevels) {
					this.expandAllLevelsFromNode(node[this.childrenKey], level + 1);
				}
			}
		},

		// 处理单选逻辑
		handleSingleSelect(node, level) {
			// 构建完整路径
			const path = this.buildCurrentPath(node, level);

			// 如果路径构建失败，不进行选择
			if (path.length === 0) {
				console.warn('[ElCascaderPopup] 路径构建失败，无法选择');
				return;
			}

			// 如果是严格模式且不是叶子节点，不进行选择
			if (this.checkStrictly && !this.isLeafNode(node, level)) {
				return;
			}

			// 单选直接替换
			this.selectedNodes = [this.createSelectedNode(path)];

			console.log('[ElCascaderPopup] 单选结果:', this.selectedNodes);

			// 只有在 multiple=false 且 max=1 时才自动确认
			// 如果 multiple=false 但 max>1，则需要手动确认
			if (!this.multiple && this.max === 1) {
				// 延迟一下确认，让用户看到选择效果
				this.$nextTick(() => {
					setTimeout(() => {
						this.handleConfirm();
					}, 200);
				});
			}
		},

		// 处理多选逻辑
		handleMultipleSelect(node, level) {
			// 如果是严格模式且不是叶子节点，不进行选择
			if (this.checkStrictly && !this.isLeafNode(node, level)) {
				return;
			}

			const path = this.buildCurrentPath(node, level);
			if (path.length === 0) {
				console.warn('[ElCascaderPopup] 多选路径构建失败');
				return;
			}

			const selectedNode = this.createSelectedNode(path);
			if (!selectedNode) {
				console.warn('[ElCascaderPopup] 多选节点创建失败');
				return;
			}

			const existingIndex = this.selectedNodes.findIndex((selected) => selected && selected[this.valueKey] === node[this.valueKey]);

			if (existingIndex !== -1) {
				// 已选中，移除
				this.selectedNodes.splice(existingIndex, 1);
			} else {
				// 未选中，添加
				if (this.selectedNodes.length >= this.max) {
					uni.showToast({
						title: `最多只能选择${this.max}项`,
						icon: 'none'
					});
					return;
				}
				this.selectedNodes.push(selectedNode);
			}
		},

		// 构建当前选择路径
		buildCurrentPath(node, level) {
			const path = [];

			// 添加前面层级的选中项
			let currentData = this.options;
			for (let i = 0; i < level; i++) {
				const activeIndex = this.activeIndexes[i];
				if (activeIndex !== undefined && currentData && currentData[activeIndex]) {
					path.push(currentData[activeIndex]);
					currentData = currentData[activeIndex][this.childrenKey] || [];
				} else {
					// 如果路径断裂，返回空路径
					console.warn('[ElCascaderPopup] 路径构建失败，层级索引不连续');
					return [];
				}
			}

			// 添加当前选中项
			if (node) {
				path.push(node);
			}
			return path;
		},

		// 判断是否为叶子节点
		isLeafNode(node, currentLevel = null) {
			// 当 level 为 1 时，所有第一级节点都是叶子节点
			if (this.level === 1) {
				return true;
			}
			// 当前层级是最后一级时，也是叶子节点
			if (currentLevel !== null && currentLevel >= this.level - 1) {
				return true;
			}
			// 否则根据是否有子节点判断
			return !node[this.childrenKey] || node[this.childrenKey].length === 0;
		},

		// 判断节点是否被选中（包括路径上的节点）
		isSelected(node) {
			if (!node || !this.selectedNodes || this.selectedNodes.length === 0) {
				return false;
			}

			// 检查是否是最终选中的节点
			const isDirectlySelected = this.selectedNodes.some((selected) =>
				selected && selected[this.valueKey] === node[this.valueKey]
			);

			// 如果是多选模式，只高亮最终选中的节点
			if (this.multiple) {
				return isDirectlySelected;
			}

			// 单选模式：检查是否在选中路径上（高亮整个路径）
			const isInSelectedPath = this.selectedNodes.some((selected) =>
				selected && selected.path && selected.path.includes(node[this.valueKey])
			);

			return isDirectlySelected || isInSelectedPath;
		},

		// 获取选中的子项数量
		getChildrenCount(node, level) {
			if (!this.multiple) return 0;

			return this.selectedNodes.filter((selected) => {
				// 检查是否为该项的子项
				return selected.path && selected.path.length > level + 1 && selected.path[level] === node[this.valueKey];
			}).length;
		},

		// 判断是否为叶子层级
		isLeaf(level) {
			return level >= this.level - 1;
		},

		// 判断是否应该显示选择图标
		shouldShowIcon(level) {
			// 单选模式：所有级别都显示图标（因为可以在任何级别选择）
			if (!this.multiple) {
				return true;
			}
			// 多选模式：只在叶子级别显示图标（只能选择叶子节点）
			return this.isLeaf(level);
		},

		// 判断某个项是否为当前点击的高亮项
		isCurrentClick(level, index) {
			return this.currentClickIndexes[level] === index;
		},

		// 移除选中标签
		handleRemoveTag(node) {
			const index = this.selectedNodes.findIndex((selected) => selected[this.valueKey] === node[this.valueKey]);
			if (index !== -1) {
				this.selectedNodes.splice(index, 1);
			}
		},

		// 获取节点唯一键
		getNodeKey(node) {
			return node[this.valueKey];
		},

		// 获取节点显示文本
		getNodeLabel(node) {
			return node[this.labelKey];
		},

		// 确认选择
		handleConfirm() {
			console.log('[ElCascaderPopup] 开始确认选择，当前选中项:', this.selectedNodes);

			// 验证是否有选中项
			if (this.selectedNodes.length === 0) {
				uni.showToast({
					title: '请至少选择一项',
					icon: 'none'
				});
				return;
			}

			// 过滤掉空的选中项
			const validSelectedNodes = this.selectedNodes.filter((node) => node !== null && node !== undefined);

			if (validSelectedNodes.length === 0) {
				console.warn('[ElCascaderPopup] 没有有效的选中项');
				uni.showToast({
					title: '选择数据异常，请重新选择',
					icon: 'none'
				});
				return;
			}

			const result = {
				ident: this.ident,
				value: validSelectedNodes,
				paths: this.getSelectedPaths()
			};

			console.log('[ElCascaderPopup] 确认选择结果:', result);

			// 触发回调和事件
			try {
				this.complete && this.complete(result);
				this.$emit('confirm', result);
				this.handleClose();
			} catch (error) {
				console.error('[ElCascaderPopup] 确认选择时发生错误:', error);
			}
		},

		// 获取选中路径列表
		getSelectedPaths() {
			const validNodes = this.selectedNodes.filter((node) => node !== null && node !== undefined);

			if (this.emitPath) {
				return validNodes.map((node) => {
					if (node.path && Array.isArray(node.path)) {
						return node.path;
					} else {
						return [node[this.valueKey]];
					}
				});
			} else {
				return validNodes.map((node) => node[this.valueKey]);
			}
		},

		// 关闭弹窗
		handleClose() {
			this.$emit('close', { ident: this.ident });
		}
	}
};
</script>

/** * Element Plus 风格级联选择器组件样式 * 遵循 Element Plus BEM 命名规范 */
<style lang="scss" scoped>
/* ==========================================================================
   级联选择器主容器
   ========================================================================== */
.el-cascader-popup {
	width: 750rpx;
	background-color: #ffffff;
	display: flex;
	flex-direction: column;
	border-radius: 20rpx 20rpx 0 0;
}

.el-cascader {
	background: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
	display: flex;
	flex-direction: column;
}

/* ==========================================================================
   级联选择器头部
   ========================================================================== */
.el-cascader__header {
	width: 100%;
	height: 120rpx;
	padding: 0 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 12rpx solid #f8f8f8;
}

.el-cascader__title {
	font-size: 36rpx;
	font-weight: 500;
	color: #333333;
	line-height: 1.4;
}

.el-cascader__close {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;

	image {
		width: 32rpx;
		height: 32rpx;
	}
}

/* ==========================================================================
   级联选择器面板
   ========================================================================== */
.el-cascader__panel {
	display: flex;
	background: #ffffff;
}

.el-cascader__menu {
	background: #ffffff;
	flex-shrink: 0;

	&.el-cascader__menu--first {
		background: #f5f5f5;
	}

	&:not(.el-cascader__menu--first) {
		min-width: 250rpx;
	}

	&.el-cascader__menu--grid {
		.el-cascader__grid {
			padding: 15rpx 25rpx;
			display: flex;
			flex-wrap: wrap;
		}
	}
}

/* ==========================================================================
   级联选择器节点容器
   ========================================================================== */
.el-cascader__nodes {
	display: flex;
	flex-direction: column;
}

/* ==========================================================================
   级联选择器节点项
   ========================================================================== */
.el-cascader__node {
	height: 100rpx;
	padding: 0 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 30rpx;
	font-weight: 500;
	color: #333333;
	cursor: pointer;
	transition: all 0.2s ease;

	&.is-active {
		color: #444bf1;
		background: #ffffff;
	}

	&.is-current-click {
		color: #444bf1;
		transform: scale(0.98);
		transition: all 0.15s ease-out;
	}

	// 当同时有 is-active 和 is-current-click 时，is-active 优先级更高
	&.is-active.is-current-click {
		color: #444bf1;
		background: #ffffff;
		transform: scale(0.98);
	}

	&.is-disabled {
		color: #c0c4cc;
		background: #f5f7fa;
		cursor: not-allowed;
	}
}

.el-cascader__label {
	flex: 1;
	line-height: 1.4;
}

.el-cascader__count {
	color: #909399;
	font-size: 24rpx;
	margin-left: 8rpx;
}

.el-cascader__icon {
	width: 36rpx;
	height: 36rpx;
	flex-shrink: 0;
}

/* ==========================================================================
   级联选择器网格容器和网格项
   ========================================================================== */
.el-cascader__grid {
	padding: 15rpx 25rpx;
	display: flex;
	flex-wrap: wrap;
}

.el-cascader__grid-item {
	width: 215rpx;
	height: 60rpx;
	margin: 10rpx 20rpx 10rpx 0;
	background: #f5f7fa;
	border-radius: 10rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	font-weight: 400;
	font-size: 24rpx;
	color: #606266;
	cursor: pointer;
	transition: all 0.2s ease;

	&:nth-child(2n) {
		margin-right: 0;
	}

	&.is-active {
		color: #444bf1;
		background: #ecf5ff;
		border: 1px solid #444bf1;
	}

	&.is-current-click {
		color: #444bf1;
		transform: scale(0.95);
		transition: all 0.15s ease-out;
	}

	// 当同时有 is-active 和 is-current-click 时，is-active 优先级更高
	&.is-active.is-current-click {
		color: #444bf1;
		background: #ecf5ff;
		border: 1px solid #444bf1;
		transform: scale(0.95);
	}

	&.is-disabled {
		color: #c0c4cc;
		background: #f0f0f0;
		cursor: not-allowed;
	}
}

.el-cascader__grid-label {
	text-align: center;
	line-height: 1.4;
}

/* ==========================================================================
   级联选择器底部区域
   ========================================================================== */
.el-cascader__footer {
	padding: 30rpx;
	background: #ffffff;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	display: flex;
	flex-direction: column;
}

/* ==========================================================================
   级联选择器已选标签
   ========================================================================== */
.el-cascader__tags {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 20rpx;
}

.el-cascader__tag {
	padding: 5rpx 10rpx;
	margin-right: 30rpx;
	margin-bottom: 15rpx;
	border: 1px solid #dcdfe6;
	background: #f4f4f5;
	color: #606266;
	font-size: 24rpx;
	border-radius: 4rpx;
	position: relative;
	line-height: 1.4;

	image {
		width: 20rpx;
		height: 20rpx;
		position: absolute;
		right: -10rpx;
		top: -10rpx;
		cursor: pointer;
	}
}

/* ==========================================================================
   级联选择器确认按钮
   ========================================================================== */
.el-cascader__confirm {
	height: 100rpx;
	background: #444bf1;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 10rpx;
	font-size: 34rpx;
	font-weight: 500;
	color: #ffffff;
	cursor: pointer;
	transition: background-color 0.3s;

	&:hover {
		background: #66b1ff;
	}

	&:active {
		background: #3a8ee6;
	}
}

/* ==========================================================================
   动画效果
   ========================================================================== */
.el-cascader__tag {
	animation-duration: 0.3s;
	animation-fill-mode: both;
}

@keyframes el-zoom-in-center {
	0% {
		opacity: 0;
		transform: scaleX(0);
	}
	100% {
		opacity: 1;
		transform: scaleX(1);
	}
}

.el-cascader__tag {
	animation-name: el-zoom-in-center;
}
</style>
