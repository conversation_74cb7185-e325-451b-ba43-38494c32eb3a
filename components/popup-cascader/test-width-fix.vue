<template>
	<view class="test-container">
		<text class="title">menuWidth 智能默认值测试</text>
		
		<!-- 测试按钮组 -->
		<view class="button-group">
			<button @click="test1Level">1级测试 (应该750rpx)</button>
			<button @click="test2Level">2级测试 (应该250/200rpx)</button>
			<button @click="test3Level">3级测试 (应该250rpx)</button>
		</view>
		
		<!-- 当前配置显示 -->
		<view class="config-section">
			<text class="section-title">当前配置：</text>
			<text class="config-text">级别: {{ currentConfig.level }}</text>
			<text class="config-text">menuWidth: {{ currentConfig.menuWidth }}</text>
			<text class="config-text">预期宽度: {{ expectedWidth }}</text>
		</view>
		
		<!-- 弹窗组件 -->
		<popup-cascader
			:show="showPopup"
			:ident="currentConfig.ident"
			:title="currentConfig.title"
			:level="currentConfig.level"
			:multiple="currentConfig.multiple"
			:max="currentConfig.max"
			:display-mode="currentConfig.displayMode"
			:menu-width="currentConfig.menuWidth"
			:show-all-levels="currentConfig.showAllLevels"
			:options="testData"
			:model-value="currentConfig.modelValue"
			@confirm="handleConfirm"
			@close="handleClose"
		/>
	</view>
</template>

<script>
import PopupCascader from './popup-cascader.vue';

export default {
	name: 'TestWidthFix',
	components: {
		PopupCascader
	},
	data() {
		return {
			showPopup: false,
			currentConfig: {
				ident: 'test',
				title: '测试',
				level: 1,
				multiple: false,
				max: 1,
				displayMode: 'list',
				menuWidth: null,
				showAllLevels: false,
				modelValue: []
			},
			testData: [
				{
					value: 1,
					label: '北京市',
					children: [
						{
							value: 11,
							label: '朝阳区',
							children: [
								{ value: 111, label: '三里屯街道' },
								{ value: 112, label: '建外街道' }
							]
						},
						{
							value: 12,
							label: '海淀区',
							children: [
								{ value: 121, label: '中关村街道' },
								{ value: 122, label: '学院路街道' }
							]
						}
					]
				},
				{
					value: 2,
					label: '上海市',
					children: [
						{
							value: 21,
							label: '浦东新区',
							children: [
								{ value: 211, label: '陆家嘴街道' },
								{ value: 212, label: '花木街道' }
							]
						},
						{
							value: 22,
							label: '黄浦区',
							children: [
								{ value: 221, label: '南京东路街道' },
								{ value: 222, label: '外滩街道' }
							]
						}
					]
				}
			]
		};
	},
	computed: {
		expectedWidth() {
			if (this.currentConfig.level === 1) {
				return '750rpx';
			} else if (this.currentConfig.level === 2) {
				return '[250rpx, 200rpx]';
			} else {
				return '250rpx (所有级别)';
			}
		}
	},
	methods: {
		// 1级测试
		test1Level() {
			this.currentConfig = {
				ident: 'level-1',
				title: '1级选择测试',
				level: 1,
				multiple: false,
				max: 1,
				displayMode: 'list',
				menuWidth: null, // 使用智能默认值
				showAllLevels: false,
				modelValue: []
			};
			this.showPopup = true;
		},
		
		// 2级测试
		test2Level() {
			this.currentConfig = {
				ident: 'level-2',
				title: '2级选择测试',
				level: 2,
				multiple: false,
				max: 1,
				displayMode: 'list',
				menuWidth: null, // 使用智能默认值
				showAllLevels: false,
				modelValue: []
			};
			this.showPopup = true;
		},
		
		// 3级测试
		test3Level() {
			this.currentConfig = {
				ident: 'level-3',
				title: '3级选择测试',
				level: 3,
				multiple: false,
				max: 1,
				displayMode: 'list',
				menuWidth: null, // 使用智能默认值
				showAllLevels: false,
				modelValue: []
			};
			this.showPopup = true;
		},
		
		handleConfirm(result) {
			console.log('选择结果:', result);
			this.showPopup = false;
		},
		
		handleClose() {
			this.showPopup = false;
		}
	}
};
</script>

<style lang="scss" scoped>
.test-container {
	padding: 40rpx;
	background: #f5f5f5;
	min-height: 100vh;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	text-align: center;
	margin-bottom: 40rpx;
	color: #333;
}

.button-group {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	margin-bottom: 40rpx;
	
	button {
		padding: 20rpx;
		background: #444bf1;
		color: white;
		border: none;
		border-radius: 8rpx;
		font-size: 28rpx;
	}
}

.config-section {
	background: white;
	padding: 30rpx;
	border-radius: 12rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.config-text {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 10rpx;
	display: block;
}
</style>
