# popup-cascader v2.0.0 迁移指南

## 概述

popup-cascader v2.0.0 是一个**完全向后兼容**的重大更新。现有代码无需修改即可正常工作，同时提供了更强大的新功能。

## 🎉 好消息：无需迁移！

如果你的现有代码工作正常，**无需做任何修改**。v2.0.0 完全兼容 v1.0.0 的所有 API 和功能。

## 新功能使用指南

### 1. 混合显示模式

**v1.0.0**：所有级别使用相同显示模式
```vue
<popup-cascader displayMode="list" />
<popup-cascader displayMode="grid" />
```

**v2.0.0**：支持每级别独立设置
```vue
<!-- 前两级列表，第三级网格 -->
<popup-cascader :displayMode="['list', 'list', 'grid']" />

<!-- 第一级网格，后续级别列表 -->
<popup-cascader :displayMode="['grid', 'list', 'list']" />

<!-- 仍然支持字符串格式（向后兼容） -->
<popup-cascader displayMode="list" />
```

### 2. 自定义菜单宽度

**v2.0.0 新增**：支持每级别独立设置宽度
```vue
<!-- 每级不同宽度 -->
<popup-cascader :menuWidth="[300, 200, 250]" />

<!-- 单级选择时使用全宽 -->
<popup-cascader :level="1" :menuWidth="750" />

<!-- 仍然支持数字格式（向后兼容） -->
<popup-cascader :menuWidth="250" />
```

### 3. 任意级别支持

**v1.0.0**：主要支持 2-3 级
**v2.0.0**：支持 1-10 级任意级别

```vue
<!-- 1级选择 -->
<popup-cascader :level="1" :menuWidth="750" />

<!-- 4级选择 -->
<popup-cascader :level="4" :menuWidth="200" />

<!-- 5级选择 -->
<popup-cascader :level="5" :menuWidth="[250, 200, 200, 200, 180]" />
```

## 最佳实践建议

### 1. 单级选择优化

```vue
<!-- 推荐：单级选择时使用全宽 -->
<popup-cascader
  :level="1"
  :menuWidth="750"
  :options="singleOptions"
/>
```

### 2. 多级选择优化

```vue
<!-- 推荐：根据内容调整宽度 -->
<popup-cascader
  :level="3"
  :menuWidth="[280, 220, 250]"
  :displayMode="['list', 'list', 'grid']"
  :options="cityOptions"
/>
```

### 3. 移动端适配

```vue
<!-- 推荐：移动端使用较小宽度 -->
<popup-cascader
  :level="4"
  :menuWidth="[200, 180, 180, 160]"
  :options="mobileOptions"
/>
```

## 性能提升

v2.0.0 在性能方面也有显著提升：

1. **统一渲染逻辑**：减少了重复代码和判断
2. **计算属性缓存**：菜单配置自动缓存，避免重复计算
3. **按需渲染**：只渲染激活的菜单级别

## 常见问题

### Q: 我的现有代码还能正常工作吗？
A: 是的！v2.0.0 完全向后兼容，现有代码无需修改。

### Q: 如何升级到新功能？
A: 只需要将相应的 prop 从字符串/数字改为数组格式即可。

### Q: 数组参数的长度需要和 level 一致吗？
A: 不需要。如果数组长度不足，会使用默认值填充。

### Q: 可以混合使用新旧参数格式吗？
A: 可以！例如：`displayMode="list"` 和 `:menuWidth="[300, 200]"` 可以同时使用。

## 示例对比

### 传统用法（仍然支持）

```vue
<template>
  <popup-cascader
    :level="3"
    displayMode="list"
    :menuWidth="250"
    :multiple="false"
    :options="cityOptions"
    @confirm="onConfirm"
  />
</template>
```

### 新功能用法

```vue
<template>
  <popup-cascader
    :level="3"
    :displayMode="['list', 'list', 'grid']"
    :menuWidth="[300, 200, 250]"
    :multiple="true"
    :max="2"
    :options="cityOptions"
    @confirm="onConfirm"
  />
</template>
```

## 升级步骤

1. **无需修改现有代码**：直接使用新版本即可
2. **可选：使用新功能**：根据需要添加数组格式的参数
3. **测试验证**：使用 `test-dynamic-levels.vue` 测试各种场景

## 总结

popup-cascader v2.0.0 是一个**无痛升级**的版本：

- ✅ **完全兼容**：现有代码无需修改
- ✅ **功能增强**：支持更灵活的配置
- ✅ **性能提升**：更快的渲染和更好的用户体验
- ✅ **易于使用**：渐进式的功能增强

立即升级，享受更强大的级联选择功能！
