<template>
	<view class="example-page">
		<view class="demo-section">
			<text class="section-title">级联选择器示例</text>
			
			<!-- 单选示例 -->
			<view class="demo-item">
				<text class="demo-title">单选模式</text>
				<view class="demo-btn" @click="showSingleSelect">
					{{ singleResult.length > 0 ? singleResult[0].pathLabels.join(' / ') : '请选择地区' }}
				</view>
			</view>
			
			<!-- 多选示例 -->
			<view class="demo-item">
				<text class="demo-title">多选模式（最多3个）</text>
				<view class="demo-btn" @click="showMultipleSelect">
					{{ multipleResult.length > 0 ? `已选择${multipleResult.length}项` : '请选择地区' }}
				</view>
				<view v-if="multipleResult.length > 0" class="selected-list">
					<text v-for="item in multipleResult" :key="item.value" class="selected-item">
						{{ item.pathLabels.join(' / ') }}
					</text>
				</view>
			</view>
			
			<!-- 网格模式示例 -->
			<view class="demo-item">
				<text class="demo-title">网格模式</text>
				<view class="demo-btn" @click="showGridSelect">
					{{ gridResult.length > 0 ? gridResult[0].pathLabels.join(' / ') : '请选择地区' }}
				</view>
			</view>
			
			<!-- 末级编号回显示例 -->
			<view class="demo-item">
				<text class="demo-title">末级编号回显</text>
				<view class="demo-btn" @click="showValueEcho">
					{{ echoResult.length > 0 ? echoResult[0].pathLabels.join(' / ') : '点击查看回显效果' }}
				</view>
			</view>
		</view>
		
		<!-- 级联选择器弹窗 -->
		<u-popup v-model="popupShow" mode="bottom" border-radius="20" :safe-area-inset-bottom="false">
			<el-cascader-popup
				:show="popupShow"
				:title="currentConfig.title"
				:level="currentConfig.level"
				:multiple="currentConfig.multiple"
				:max="currentConfig.max"
				:displayMode="currentConfig.displayMode"
				:options="cityOptions"
				:modelValue="currentConfig.modelValue"
				:props="cascaderProps"
				@confirm="onCascaderConfirm"
				@close="onCascaderClose"
			/>
		</u-popup>
	</view>
</template>

<script>
import ElCascaderPopup from './popup-cascader.vue';

export default {
	name: 'CascaderExample',
	components: {
		ElCascaderPopup
	},
	data() {
		return {
			popupShow: false,
			currentConfig: {},
			
			// 单选结果
			singleResult: [],
			// 多选结果
			multipleResult: [],
			// 网格模式结果
			gridResult: [],
			// 回显结果
			echoResult: [],
			
			// 级联选择器配置
			cascaderProps: {
				value: 'id',
				label: 'name',
				children: 'children'
			},
			
			// 模拟城市数据
			cityOptions: [
				{
					id: '110000',
					name: '北京市',
					children: [
						{
							id: '110100',
							name: '北京市',
							children: [
								{ id: '110101', name: '东城区' },
								{ id: '110102', name: '西城区' },
								{ id: '110105', name: '朝阳区' },
								{ id: '110106', name: '丰台区' },
								{ id: '110107', name: '石景山区' },
								{ id: '110108', name: '海淀区' },
								{ id: '110109', name: '门头沟区' },
								{ id: '110111', name: '房山区' },
								{ id: '110112', name: '通州区' },
								{ id: '110113', name: '顺义区' },
								{ id: '110114', name: '昌平区' },
								{ id: '110115', name: '大兴区' },
								{ id: '110116', name: '怀柔区' },
								{ id: '110117', name: '平谷区' },
								{ id: '110118', name: '密云区' },
								{ id: '110119', name: '延庆区' }
							]
						}
					]
				},
				{
					id: '310000',
					name: '上海市',
					children: [
						{
							id: '310100',
							name: '上海市',
							children: [
								{ id: '310101', name: '黄浦区' },
								{ id: '310104', name: '徐汇区' },
								{ id: '310105', name: '长宁区' },
								{ id: '310106', name: '静安区' },
								{ id: '310107', name: '普陀区' },
								{ id: '310109', name: '虹口区' },
								{ id: '310110', name: '杨浦区' },
								{ id: '310112', name: '闵行区' },
								{ id: '310113', name: '宝山区' },
								{ id: '310114', name: '嘉定区' },
								{ id: '310115', name: '浦东新区' },
								{ id: '310116', name: '金山区' },
								{ id: '310117', name: '松江区' },
								{ id: '310118', name: '青浦区' },
								{ id: '310120', name: '奉贤区' },
								{ id: '310151', name: '崇明区' }
							]
						}
					]
				},
				{
					id: '440000',
					name: '广东省',
					children: [
						{
							id: '440100',
							name: '广州市',
							children: [
								{ id: '440103', name: '荔湾区' },
								{ id: '440104', name: '越秀区' },
								{ id: '440105', name: '海珠区' },
								{ id: '440106', name: '天河区' },
								{ id: '440111', name: '白云区' },
								{ id: '440112', name: '黄埔区' },
								{ id: '440113', name: '番禺区' },
								{ id: '440114', name: '花都区' },
								{ id: '440115', name: '南沙区' },
								{ id: '440117', name: '从化区' },
								{ id: '440118', name: '增城区' }
							]
						},
						{
							id: '440300',
							name: '深圳市',
							children: [
								{ id: '440303', name: '罗湖区' },
								{ id: '440304', name: '福田区' },
								{ id: '440305', name: '南山区' },
								{ id: '440306', name: '宝安区' },
								{ id: '440307', name: '龙岗区' },
								{ id: '440308', name: '盐田区' },
								{ id: '440309', name: '龙华区' },
								{ id: '440310', name: '坪山区' },
								{ id: '440311', name: '光明区' }
							]
						}
					]
				}
			]
		};
	},
	methods: {
		// 显示单选
		showSingleSelect() {
			this.currentConfig = {
				title: '选择地区（单选）',
				level: 3,
				multiple: false,
				max: 1,
				displayMode: 'list',
				modelValue: this.singleResult.map(item => item.value)
			};
			this.popupShow = true;
		},

		// 显示多选
		showMultipleSelect() {
			this.currentConfig = {
				title: '选择地区（多选）',
				level: 3,
				multiple: true,
				max: 3,
				displayMode: 'list',
				modelValue: this.multipleResult.map(item => item.value)
			};
			this.popupShow = true;
		},

		// 显示网格模式
		showGridSelect() {
			this.currentConfig = {
				title: '选择地区（网格）',
				level: 3,
				multiple: false,
				max: 1,
				displayMode: 'grid',
				modelValue: this.gridResult.map(item => item.value)
			};
			this.popupShow = true;
		},

		// 显示末级编号回显
		showValueEcho() {
			this.currentConfig = {
				title: '末级编号回显',
				level: 3,
				multiple: false,
				max: 1,
				displayMode: 'list',
				modelValue: ['110105'] // 直接传入朝阳区的编号
			};
			this.popupShow = true;
		},
		
		// 级联选择确认
		onCascaderConfirm(result) {
			console.log('选择结果：', result);
			
			// 根据当前配置更新对应的结果
			if (this.currentConfig.title.includes('单选')) {
				this.singleResult = result.value;
			} else if (this.currentConfig.title.includes('多选')) {
				this.multipleResult = result.value;
			} else if (this.currentConfig.title.includes('网格')) {
				this.gridResult = result.value;
			} else if (this.currentConfig.title.includes('回显')) {
				this.echoResult = result.value;
			}
		},
		
		// 级联选择关闭
		onCascaderClose() {
			this.popupShow = false;
		}
	}
};
</script>

<style lang="scss" scoped>
.example-page {
	padding: 30rpx;
	background: #f5f5f5;
	min-height: 100vh;
}

.demo-section {
	background: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 40rpx;
}

.demo-item {
	margin-bottom: 40rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
}

.demo-title {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 20rpx;
}

.demo-btn {
	height: 80rpx;
	padding: 0 30rpx;
	background: #f8f8f8;
	border: 1px solid #e0e0e0;
	border-radius: 10rpx;
	display: flex;
	align-items: center;
	font-size: 30rpx;
	color: #333333;
}

.selected-list {
	margin-top: 20rpx;
}

.selected-item {
	display: block;
	padding: 10rpx 20rpx;
	margin-bottom: 10rpx;
	background: #f3f4ff;
	border: 1px solid #444bf1;
	border-radius: 8rpx;
	font-size: 24rpx;
	color: #444bf1;
}
</style>
