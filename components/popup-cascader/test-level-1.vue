<template>
	<view class="test-container">
		<text class="title">popup-cascader Level=1 测试</text>
		
		<!-- 测试按钮 -->
		<view class="button-group">
			<button @click="showSingleSelect">单选测试 (level=1)</button>
			<button @click="showMultiSelect">多选测试 (level=1)</button>
			<button @click="showGridMode">网格模式测试 (level=1)</button>
		</view>
		
		<!-- 显示选中结果 -->
		<view class="result-section">
			<text class="section-title">选中结果：</text>
			<text class="result-text">{{ resultText }}</text>
		</view>
		
		<!-- popup-cascader 组件 -->
		<popup-cascader
			:show="showPopup"
			:ident="currentIdent"
			:title="currentTitle"
			:level="1"
			:multiple="currentMultiple"
			:max="currentMax"
			:display-mode="currentDisplayMode"
			:options="testOptions"
			:model-value="currentValue"
			@confirm="handleConfirm"
			@close="handleClose"
		/>
	</view>
</template>

<script>
export default {
	name: 'TestLevel1',
	data() {
		return {
			showPopup: false,
			currentIdent: '',
			currentTitle: '',
			currentMultiple: false,
			currentMax: 1,
			currentDisplayMode: 'list',
			currentValue: [],
			resultText: '暂无选择',
			
			// 测试数据 - 只有一级，没有子级
			testOptions: [
				{ id: 1, name: '选项1' },
				{ id: 2, name: '选项2' },
				{ id: 3, name: '选项3' },
				{ id: 4, name: '选项4' },
				{ id: 5, name: '选项5' },
				{ id: 6, name: '选项6' },
				{ id: 7, name: '选项7' },
				{ id: 8, name: '选项8' },
				{ id: 9, name: '选项9' },
				{ id: 10, name: '选项10' }
			]
		};
	},
	methods: {
		showSingleSelect() {
			this.currentIdent = 'single-select';
			this.currentTitle = '单选测试 (level=1)';
			this.currentMultiple = false;
			this.currentMax = 1;
			this.currentDisplayMode = 'list';
			this.currentValue = [];
			this.showPopup = true;
		},
		
		showMultiSelect() {
			this.currentIdent = 'multi-select';
			this.currentTitle = '多选测试 (level=1)';
			this.currentMultiple = true;
			this.currentMax = 3;
			this.currentDisplayMode = 'list';
			this.currentValue = [];
			this.showPopup = true;
		},
		
		showGridMode() {
			this.currentIdent = 'grid-mode';
			this.currentTitle = '网格模式测试 (level=1)';
			this.currentMultiple = true;
			this.currentMax = 5;
			this.currentDisplayMode = 'grid';
			this.currentValue = [];
			this.showPopup = true;
		},
		
		handleConfirm(result) {
			console.log('选择结果:', result);
			
			if (result && result.value && result.value.length > 0) {
				const names = result.value.map(item => item.name || item.label);
				this.resultText = `已选择: ${names.join('、')}`;
			} else {
				this.resultText = '暂无选择';
			}
			
			this.showPopup = false;
		},
		
		handleClose() {
			this.showPopup = false;
		}
	}
};
</script>

<style lang="scss" scoped>
.test-container {
	padding: 20rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	text-align: center;
	margin-bottom: 40rpx;
	color: #333;
}

.button-group {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	margin-bottom: 40rpx;
}

button {
	height: 80rpx;
	background: #007aff;
	color: white;
	border: none;
	border-radius: 8rpx;
	font-size: 32rpx;
}

.result-section {
	padding: 20rpx;
	background: #f5f5f5;
	border-radius: 8rpx;
	margin-bottom: 40rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.result-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
}
</style>
