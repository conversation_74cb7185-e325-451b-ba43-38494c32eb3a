<template>
	<view class="test-container">
		<text class="title">popup-cascader 动态级别测试</text>
		<text class="subtitle">✨ 新增点击高亮功能：点击项会有灰色高亮，已选中项保持蓝色高亮</text>
		
		<!-- 测试按钮组 -->
		<view class="button-group">
			<button @click="testLevel1">1级测试</button>
			<button @click="testLevel2">2级测试</button>
			<button @click="testLevel3">3级测试</button>
			<button @click="testLevel4">4级测试</button>
			<button @click="testMixedDisplay">混合显示模式</button>
			<button @click="testCustomWidth">自定义宽度</button>
			<button @click="testShowAllLevels">展示全部级别</button>
		</view>
		
		<!-- 当前配置显示 -->
		<view class="config-section">
			<text class="section-title">当前配置：</text>
			<text class="config-text">级别: {{ currentConfig.level }}</text>
			<text class="config-text">显示模式: {{ JSON.stringify(currentConfig.displayMode) }}</text>
			<text class="config-text">菜单宽度: {{ JSON.stringify(currentConfig.menuWidth) }}</text>
			<text class="config-text">展示全部级别: {{ currentConfig.showAllLevels ? '是' : '否' }}</text>
		</view>
		
		<!-- 选中结果显示 -->
		<view class="result-section">
			<text class="section-title">选中结果：</text>
			<text class="result-text">{{ resultText }}</text>
		</view>
		
		<!-- popup-cascader 组件 -->
		<u-popup v-model="showPopup" mode="bottom" border-radius="20">
			<popup-cascader
				:show="showPopup"
				:ident="currentConfig.ident"
				:title="currentConfig.title"
				:level="currentConfig.level"
				:multiple="currentConfig.multiple"
				:max="currentConfig.max"
				:display-mode="currentConfig.displayMode"
				:menu-width="currentConfig.menuWidth"
				:show-all-levels="currentConfig.showAllLevels"
				:options="testData"
				:model-value="currentConfig.modelValue"
				@confirm="handleConfirm"
				@close="handleClose"
			/>
		</u-popup>
	</view>
</template>

<script>
export default {
	name: 'TestDynamicLevels',
	data() {
		return {
			showPopup: false,
			resultText: '暂无选择',
			currentConfig: {
				ident: 'test',
				title: '测试',
				level: 1,
				multiple: false,
				max: 1,
				displayMode: 'list',
				menuWidth: null, // 使用智能默认值
				showAllLevels: false,
				modelValue: []
			},
			
			// 4级测试数据
			testData: [
				{
					id: 1,
					name: '一级选项1',
					children: [
						{
							id: 11,
							name: '二级选项1-1',
							children: [
								{
									id: 111,
									name: '三级选项1-1-1',
									children: [
										{ id: 1111, name: '四级选项1-1-1-1' },
										{ id: 1112, name: '四级选项1-1-1-2' }
									]
								},
								{
									id: 112,
									name: '三级选项1-1-2',
									children: [
										{ id: 1121, name: '四级选项1-1-2-1' },
										{ id: 1122, name: '四级选项1-1-2-2' }
									]
								}
							]
						},
						{
							id: 12,
							name: '二级选项1-2',
							children: [
								{
									id: 121,
									name: '三级选项1-2-1',
									children: [
										{ id: 1211, name: '四级选项1-2-1-1' }
									]
								}
							]
						}
					]
				},
				{
					id: 2,
					name: '一级选项2',
					children: [
						{
							id: 21,
							name: '二级选项2-1',
							children: [
								{
									id: 211,
									name: '三级选项2-1-1',
									children: [
										{ id: 2111, name: '四级选项2-1-1-1' }
									]
								}
							]
						}
					]
				},
				{
					id: 3,
					name: '一级选项3',
					children: [
						{
							id: 31,
							name: '二级选项3-1',
							children: [
								{ id: 311, name: '三级选项3-1-1' },
								{ id: 312, name: '三级选项3-1-2' },
								{ id: 313, name: '三级选项3-1-3' },
								{ id: 314, name: '三级选项3-1-4' },
								{ id: 315, name: '三级选项3-1-5' },
								{ id: 316, name: '三级选项3-1-6' }
							]
						}
					]
				}
			]
		};
	},
	methods: {
		// 1级测试
		testLevel1() {
			this.currentConfig = {
				ident: 'level-1',
				title: '1级选择测试',
				level: 1,
				multiple: false,
				max: 1,
				displayMode: 'list',
				menuWidth: null, // 使用智能默认值
				showAllLevels: false,
				modelValue: []
			};
			this.showPopup = true;
		},

		// 2级测试
		testLevel2() {
			this.currentConfig = {
				ident: 'level-2',
				title: '2级选择测试',
				level: 2,
				multiple: false,
				max: 1,
				displayMode: 'list',
				menuWidth: null, // 使用智能默认值
				showAllLevels: false,
				modelValue: []
			};
			this.showPopup = true;
		},

		// 3级测试
		testLevel3() {
			this.currentConfig = {
				ident: 'level-3',
				title: '3级选择测试',
				level: 3,
				multiple: true,
				max: 2,
				displayMode: 'list',
				menuWidth: null, // 使用智能默认值
				showAllLevels: false,
				modelValue: []
			};
			this.showPopup = true;
		},

		// 4级测试
		testLevel4() {
			this.currentConfig = {
				ident: 'level-4',
				title: '4级选择测试',
				level: 4,
				multiple: false,
				max: 1,
				displayMode: 'list',
				menuWidth: null, // 使用智能默认值
				showAllLevels: false,
				modelValue: []
			};
			this.showPopup = true;
		},

		// 混合显示模式测试
		testMixedDisplay() {
			this.currentConfig = {
				ident: 'mixed-display',
				title: '混合显示模式测试',
				level: 3,
				multiple: true,
				max: 3,
				displayMode: ['list', 'list', 'grid'], // 前两级列表，第三级网格
				menuWidth: null, // 使用智能默认值
				showAllLevels: false,
				modelValue: []
			};
			this.showPopup = true;
		},
		
		// 自定义宽度测试
		testCustomWidth() {
			this.currentConfig = {
				ident: 'custom-width',
				title: '自定义宽度测试',
				level: 3,
				multiple: false,
				max: 1,
				displayMode: 'list',
				menuWidth: [300, 200, 250], // 每级不同宽度
				showAllLevels: false,
				modelValue: []
			};
			this.showPopup = true;
		},

		// 展示全部级别测试
		testShowAllLevels() {
			this.currentConfig = {
				ident: 'show-all-levels',
				title: '展示全部级别测试',
				level: 3,
				multiple: false,
				max: 1,
				displayMode: 'list',
				menuWidth: null, // 使用智能默认值
				showAllLevels: true, // 开启展示全部级别
				modelValue: []
			};
			this.showPopup = true;
		},
		
		handleConfirm(result) {
			console.log('选择结果:', result);
			
			if (result && result.value && result.value.length > 0) {
				const names = result.value.map(item => {
					if (item.pathLabels && item.pathLabels.length > 0) {
						return item.pathLabels.join(' > ');
					}
					return item.name || item.label || '未知';
				});
				this.resultText = `已选择: ${names.join('、')}`;
			} else {
				this.resultText = '暂无选择';
			}
			
			this.showPopup = false;
		},
		
		handleClose() {
			this.showPopup = false;
		}
	}
};
</script>

<style lang="scss" scoped>
.test-container {
	padding: 20rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	text-align: center;
	margin-bottom: 20rpx;
	color: #333;
}

.subtitle {
	font-size: 28rpx;
	text-align: center;
	margin-bottom: 40rpx;
	color: #666;
	line-height: 1.5;
}

.button-group {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	margin-bottom: 40rpx;
}

button {
	height: 80rpx;
	background: #007aff;
	color: white;
	border: none;
	border-radius: 8rpx;
	font-size: 32rpx;
}

.config-section, .result-section {
	padding: 20rpx;
	background: #f5f5f5;
	border-radius: 8rpx;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
	display: block;
}

.config-text, .result-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
	display: block;
	margin-bottom: 5rpx;
}
</style>
