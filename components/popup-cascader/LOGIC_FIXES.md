# popup-cascader 逻辑修复说明

## 修复的问题

### 1. multiple=false 但 max>1 的处理逻辑

**问题描述**：
当 `multiple=false` 但 `max>1` 时，组件的行为不一致，应该始终当做单选处理。

**修复方案**：
- 明确规则：只有 `multiple=true` 时才是真正的多选，`multiple=false` 时无论 `max` 是多少都当做单选
- 修复了 `confirmText`、`shouldShowFooter`、选择逻辑等相关代码

### 2. 确认按钮文本逻辑

**修复前**：
```javascript
confirmText() {
  if (this.multiple && this.max > 1) {
    return `确认选择（${this.selectedNodes.length}/${this.max}）`;
  }
  return '确认选择';
}
```

**修复后**：
```javascript
confirmText() {
  // 真正的多选模式（multiple=true 且 max>1）
  if (this.multiple && this.max > 1) {
    return `确认选择（${this.selectedNodes.length}/${this.max}）`;
  }
  // 单选模式但需要手动确认（multiple=false 但 max>1）
  if (!this.multiple && this.max > 1) {
    return `确认选择（${this.selectedNodes.length}/1）`;
  }
  return '确认选择';
}
```

### 3. 底部区域显示逻辑

**修复前**：
```javascript
shouldShowFooter() {
  if (this.multiple) {
    return this.max > 1 || this.selectedNodes.length > 0;
  }
  return this.max > 1 && this.selectedNodes.length > 0;
}
```

**修复后**：
```javascript
shouldShowFooter() {
  // 真正的多选模式（multiple=true）
  if (this.multiple) {
    return this.max > 1 || this.selectedNodes.length > 0;
  }
  // 单选模式：当max>1时显示（需要手动确认），max=1时不显示（自动确认）
  // 注意：即使 multiple=false 但 max>1，也当做单选处理，只是需要手动确认
  return this.max > 1 && this.selectedNodes.length > 0;
}
```

### 4. 自动确认逻辑

**修复前**：
```javascript
if (this.max === 1) {
  // 自动确认
}
```

**修复后**：
```javascript
// 只有在 multiple=false 且 max=1 时才自动确认
// 如果 multiple=false 但 max>1，则需要手动确认
if (!this.multiple && this.max === 1) {
  // 自动确认
}
```

### 5. 选择图标显示逻辑

**问题描述**：
原来的逻辑 `v-if="!multiple || isLeaf(menuConfig.level)"` 在单选模式下只在叶子级别显示图标，这是不正确的。

**修复方案**：
- 添加 `shouldShowIcon(level)` 方法
- 单选模式：所有级别都显示图标（因为可以在任何级别选择）
- 多选模式：只在叶子级别显示图标（只能选择叶子节点）

```javascript
shouldShowIcon(level) {
  // 单选模式：所有级别都显示图标（因为可以在任何级别选择）
  if (!this.multiple) {
    return true;
  }
  // 多选模式：只在叶子级别显示图标（只能选择叶子节点）
  return this.isLeaf(level);
}
```

## 行为规范

### 单选模式 (multiple=false)

| max 值 | 行为描述 | 底部按钮 | 确认方式 |
|--------|----------|----------|----------|
| 1 | 标准单选，选择后自动确认 | 不显示 | 自动确认 |
| >1 | 单选但需要手动确认 | 显示 | 手动确认 |

### 多选模式 (multiple=true)

| max 值 | 行为描述 | 底部按钮 | 确认方式 |
|--------|----------|----------|----------|
| 1 | 实际上是单选（不推荐） | 显示 | 手动确认 |
| >1 | 标准多选，可选择多个项目 | 显示 | 手动确认 |

## 测试用例

创建了 `test-logic-validation.vue` 来验证所有修复的逻辑：

1. **单选自动确认** (multiple=false, max=1)
2. **单选手动确认** (multiple=false, max=3)
3. **真正多选** (multiple=true, max=3)
4. **1级测试** (level=1)
5. **3级测试** (level=3)

## 关键修复点总结

1. ✅ 明确了 `multiple` 参数的优先级：只有 `multiple=true` 才是多选
2. ✅ 修复了 `multiple=false` 但 `max>1` 时的处理逻辑
3. ✅ 修复了确认按钮文本显示
4. ✅ 修复了底部区域显示逻辑
5. ✅ 修复了自动确认条件
6. ✅ 修复了选择图标显示逻辑
7. ✅ 保持了所有 level 相关逻辑的正确性
