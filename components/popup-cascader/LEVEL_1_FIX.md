# popup-cascader Level=1 问题修复

## 问题描述

当 `level` 设置为 1 时，popup-cascader 组件存在以下问题：

1. **无法点击选择**：第一级列表项无法正常点击选择
2. **显示异常**：界面显示不正确，可能出现多余的面板
3. **选择逻辑错误**：严格模式下无法选择任何项目

## 问题原因

1. **叶子节点判断错误**：`isLeafNode` 方法没有考虑 level=1 的特殊情况
2. **菜单初始化问题**：`initActiveMenus` 方法在 level=1 时仍尝试展开子级
3. **模板渲染问题**：二级及以上面板在 level=1 时不应该显示
4. **图标显示逻辑**：选择图标的显示条件不完整

## 修复方案

### 1. 修复叶子节点判断逻辑

```javascript
// 判断是否为叶子节点
isLeafNode(node, currentLevel = null) {
  // 当 level 为 1 时，所有第一级节点都是叶子节点
  if (this.level === 1) {
    return true;
  }
  // 当前层级是最后一级时，也是叶子节点
  if (currentLevel !== null && currentLevel >= this.level - 1) {
    return true;
  }
  // 否则根据是否有子节点判断
  return !node[this.childrenKey] || node[this.childrenKey].length === 0;
}
```

### 2. 优化菜单初始化

```javascript
// 初始化激活菜单列表
initActiveMenus() {
  this.activeMenus = [];
  this.activeIndexes = [];

  // 如果有选中项，根据第一个选中项初始化菜单
  if (this.selectedNodes.length > 0) {
    const firstSelected = this.selectedNodes[0];
    this.buildActiveMenusFromPath(firstSelected.path);
  } else if (this.options.length > 0 && this.level > 1) {
    // 只有当级联层级大于1时，才默认展开第一项的子级
    const firstOption = this.options[0];
    if (firstOption[this.childrenKey] && firstOption[this.childrenKey].length > 0) {
      this.activeMenus.push(firstOption[this.childrenKey]);
      this.activeIndexes.push(0);
    }
  }
  // 当 level 为 1 时，不需要展开任何子级菜单
}
```

### 3. 修复模板渲染逻辑

```vue
<!-- 二级及以上选项（仅当 level > 1 时显示） -->
<template v-if="level > 1">
  <scroll-view
    v-for="(menuData, menuIndex) in activeMenus"
    :key="menuIndex"
    scroll-y
    class="el-cascader__menu"
    :class="{ 'el-cascader__menu--grid': displayMode === 'grid' && menuIndex === activeMenus.length - 1 }"
    :scroll-into-view="scrollIntoView[menuIndex + 1]"
  >
    <!-- 菜单内容 -->
  </scroll-view>
</template>
```

### 4. 修复图标显示条件

```vue
<!-- 第一级选项图标 -->
<image 
  v-if="!multiple || isLeaf(0) || level === 1" 
  class="el-cascader__icon" 
  :src="isSelected(item) ? checkedIcon : uncheckedIcon" 
  mode="aspectFit"
/>

<!-- 二级及以上选项图标 -->
<image 
  v-if="!multiple || isLeaf(menuIndex + 1) || level === 1" 
  class="el-cascader__icon" 
  :src="isSelected(item) ? checkedIcon : uncheckedIcon" 
  mode="aspectFit"
/>
```

## 修复后的效果

### 1. 单选模式 (level=1)
- ✅ 可以正常点击选择任意选项
- ✅ 选择后自动确认（当 max=1 时）
- ✅ 界面显示正常，只显示一级列表

### 2. 多选模式 (level=1)
- ✅ 可以选择多个选项
- ✅ 显示选择计数
- ✅ 支持手动确认

### 3. 网格模式 (level=1)
- ✅ 网格布局正常显示
- ✅ 选择状态正确显示
- ✅ 支持多选功能

## 测试用例

使用 `test-level-1.vue` 组件进行测试：

```vue
<popup-cascader
  :show="showPopup"
  :level="1"
  :multiple="false"
  :max="1"
  :options="testOptions"
  @confirm="handleConfirm"
  @close="handleClose"
/>
```

测试数据：
```javascript
testOptions: [
  { id: 1, name: '选项1' },
  { id: 2, name: '选项2' },
  { id: 3, name: '选项3' },
  // ...更多选项
]
```

## 注意事项

1. **数据格式**：level=1 时，options 数据不需要包含 children 字段
2. **严格模式**：level=1 时，checkStrictly 参数不影响选择行为
3. **路径返回**：选中结果的 path 数组只包含一个元素
4. **兼容性**：修复后仍然兼容 level > 1 的所有功能

## 相关文件

- `components/popup-cascader/popup-cascader.vue` - 主组件文件
- `components/popup-cascader/test-level-1.vue` - 测试组件
- `components/popup-cascader/LEVEL_1_FIX.md` - 修复说明文档
