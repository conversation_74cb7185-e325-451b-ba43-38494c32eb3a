<template>
	<view>
		<u-popup :show="show" mode="bottom" bgColor="transparent" zIndex="999988" :safeAreaInsetBottom="false" @close="onClose">
			<view class="flex-col popup-publish-form">
				<view class="flex close" @click.stop="onClose">
					<image :src="iImage('ic_close_x.png')"></image>
				</view>

				<view class="flex-col form-wrap">
					<view class="form-wrap__header">
						<image :src="iImage(`img_publish_form_header_${publishFormPopup.type}.png`)"></image>
					</view>

					<view class="form-wrap__content">
						<!-- 发布需求 -->
						<view v-if="publishFormPopup.type == cons.DEMAND_ORIGIN_SUBTYPE_NORMAL">
							<view class="flex form-row">
								<view class="flex form-item form-item--w50" :class="{ verify: formVerify && $u.test.isEmpty(serviceCategoryName) }" @click="onShowSelectPopup('serviceCategory')">
									<input class="input" :value="serviceCategoryName" placeholder="业务类型" disabled />
									<image class="arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
								</view>
								<view class="flex form-item form-item--w50" :class="{ verify: formVerify && $u.test.isEmpty(budgetName) }" @click="onShowSelectPopup('budget')">
									<input class="input" :value="budgetName" placeholder="预算报价" disabled />
									<image class="arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
								</view>
							</view>
							<view class="flex form-row">
								<view class="flex form-item form-item--w100" :class="{ verify: formVerify && $u.test.isEmpty(areaName) }" @click="onShowSelectPopup('city')">
									<input class="input" :value="areaName" placeholder="选择城市" disabled />
									<image class="arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
								</view>
							</view>
						</view>
						<!-- 发布资质服务需求 -->
						<view v-else-if="publishFormPopup.type == cons.DEMAND_ORIGIN_SUBTYPE_QUALIFICATION">
							<view class="flex" style="margin-bottom: 20rpx; padding: 0 20rpx">
								<u-radio-group v-model="form.qualificationTradeType" :activeColor="themeColror">
									<u-radio
										:customStyle="{ marginRight: '50rpx', fontWeight: '500' }"
										v-for="(item, index) in cons.DEMAND_QUALIFICATION_TRADE_TYPE"
										:key="index"
										:label="item.name"
										:name="item.type"
										labelColor="#333333"
										labelSize="30rpx"
									></u-radio>
								</u-radio-group>
							</view>
							<view class="flex form-row">
								<view class="flex form-item form-item--w50" :class="{ verify: formVerify && $u.test.isEmpty(qualificationCategoryName) }" @click="onShowSelectPopup('qualificationCategory')">
									<input class="input" :value="qualificationCategoryName" placeholder="资质类型" disabled />
									<image class="arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
								</view>
								<view class="flex form-item form-item--w50" :class="{ verify: formVerify && $u.test.isEmpty(industryCategoryName) }" @click="onShowSelectPopup('industryCategory')">
									<input class="input" :value="industryCategoryName" placeholder="所属行业" disabled />
									<image class="arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
								</view>
							</view>
							<view class="flex form-row">
								<view class="flex form-item form-item--w50" :class="{ verify: formVerify && $u.test.isEmpty(areaName) }" @click="onShowSelectPopup('city')">
									<input class="input" :value="areaName" placeholder="选择城市" disabled />
									<image class="arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
								</view>
								<view class="flex form-item form-item--w50" :class="{ verify: formVerify && $u.test.isEmpty(budgetName) }" @click="onShowSelectPopup('budget')">
									<input class="input" :value="budgetName" placeholder="预算报价" disabled />
									<image class="arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
								</view>
							</view>
						</view>
						<!-- 发布申报服务需求 -->
						<view v-else-if="publishFormPopup.type == cons.DEMAND_ORIGIN_SUBTYPE_DECLARATION">
							<view class="flex form-row">
								<view class="flex form-item form-item--w50" :class="{ verify: formVerify && $u.test.isEmpty(declarationCategoryName) }" @click="onShowSelectPopup('declarationCategory')">
									<input class="input" :value="declarationCategoryName" placeholder="申报类型" disabled />
									<image class="arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
								</view>
								<view class="flex form-item form-item--w50" :class="{ verify: formVerify && $u.test.isEmpty(subjectCategoryName) }" @click="onShowSelectPopup('subjectCategory')">
									<input class="input" :value="subjectCategoryName" placeholder="主体类型" disabled />
									<image class="arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
								</view>
							</view>
							<view class="flex form-row">
								<view class="flex form-item form-item--w50" :class="{ verify: formVerify && $u.test.isEmpty(areaName) }" @click="onShowSelectPopup('city')">
									<input class="input" :value="areaName" placeholder="选择城市" disabled />
									<image class="arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
								</view>
								<view class="flex form-item form-item--w50" :class="{ verify: formVerify && $u.test.isEmpty(budgetName) }" @click="onShowSelectPopup('budget')">
									<input class="input" :value="budgetName" placeholder="预算报价" disabled />
									<image class="arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
								</view>
							</view>
						</view>
						<view class="flex form-row">
							<view class="flex form-item form-item--w100" :class="{ verify: formVerify && $u.test.isEmpty(form.contactTitle) }">
								<input class="input" v-model="form.contactTitle" placeholder="您的称呼" />
								<view style="display: flex; flex-direction: row; justify-content: center">
									<u-radio-group v-model="form.sex" :activeColor="themeColror">
										<u-radio :customStyle="{ marginLeft: '50rpx', fontWeight: '500' }" v-for="(item, index) in sexList" :key="index" :label="item.name" :name="item.id" labelColor="#333333" labelSize="30rpx"></u-radio>
									</u-radio-group>
								</view>
							</view>
						</view>
						<view class="flex form-row">
							<view class="form-item form-item--w100" style="height: 200rpx">
								<textarea class="textarea" v-model="form.remark" placeholder="请输入200字以内需求说明" :maxlength="200" />
							</view>
						</view>
						<view class="flex" style="margin-top: 50rpx">
							<u-checkbox-group v-model="agreementState">
								<u-checkbox name="procotol" shape="circle" :activeColor="themeColror" label="我已阅读并同意" labelColor="#333333" labelSize="15px" />
							</u-checkbox-group>
							<text :style="{ fontSize: '15px', color: themeColror }" @click="onPageJump('/pagesMine/privacy/agreement', { type: cons.PROTOCOL_PROTECT }, false)">
								{{ `《${cons.PROTOCOL_MAPS[cons.PROTOCOL_PROTECT]}》` }}
							</text>
						</view>
						<view class="flex submit" @click="onPrepareSubmit">立即发布</view>
					</view>
					<u-safe-bottom></u-safe-bottom>
				</view>
			</view>
		</u-popup>
		<!-- 选择器 -->
		<u-popup :show="selectPopup.show" bgColor="transparent" mode="bottom" zIndex="999999" :close-on-click-overlay="false" :safe-area-inset-bottom="false" closeable @close="selectPopup.show = false">
			<popup-select
				:title="selectPopup.title"
				:list="selectPopup.list"
				:mode="selectPopup.mode"
				:value="selectPopup.value"
				:max-num="selectPopup.maxNum"
				:show-input="selectPopup.showInput"
				:complete="selectPopup.complete"
				@close="selectPopup.show = false"
			></popup-select>
		</u-popup>
		<!-- 级联选择器 -->
		<u-popup :show="linkSelectPopup.show" bgColor="transparent" mode="bottom" zIndex="999999" :safe-area-inset-bottom="false" closeable @close="linkSelectPopup.show = false">
			<popup-link-select
				:title="linkSelectPopup.title"
				:level="linkSelectPopup.level"
				:list="linkSelectPopup.list"
				:value="linkSelectPopup.value"
				:complete="linkSelectPopup.complete"
				@close="linkSelectPopup.show = false"
			></popup-link-select>
		</u-popup>
		<!-- 城市选择器 -->
		<u-popup :show="cityPopup.show" bgColor="transparent" mode="bottom" zIndex="999999" :safe-area-inset-bottom="false" closeable @close="cityPopup.show = false">
			<popup-city :length="cityPopup.level" :show="cityPopup.show" :value="cityPopup.value" :complete="cityPopup.complete" @close="cityPopup.show = false"></popup-city>
		</u-popup>
		<!-- 协议确认 -->
		<u-popup :show="showAgreementValidationPopup" zIndex="999999" :safe-area-inset-bottom="false" mode="bottom" @close="showAgreementValidationPopup = false" closeable>
			<popup-agreement-validation v-model="agreementState" :protocol-type="[cons.PROTOCOL_USER, cons.PROTOCOL_PRIVACY]" @confirm="onPrepareSubmit" @close="showAgreementValidationPopup = false"></popup-agreement-validation>
		</u-popup>
	</view>
</template>

<script>
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';
import { handleTree } from '@/common/tool.js';
export default {
	name: 'popup-publish-form',
	props: {
		// 弹窗所在的页面
		inPage: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			show: false,
			themeColror: '#444BF1',
			// 性别数据集
			sexList: [
				{
					id: '1',
					name: '先生'
				},
				{
					id: '2',
					name: '女士'
				}
			],
			// 表单参数
			form: {
				// 资质服务交易类型
				qualificationTradeType: '',
				// 称呼
				contactTitle: '',
				// 业务类型
				serviceCategory: [],
				// 行业分类
				industryCategory: [],
				// 资质类型
				qualificationCategory: [],
				// 申报类型
				declarationCategory: [],
				// 主体类型
				subjectCategory: [],
				budgetMin: '',
				budgetMax: '',
				// 备注
				remark: '',
				// 性别
				sex: '1',
				// 地区
				area: {}
			},
			// 称呼输入过滤
			nicknameFormatter: (value) => value.replace(/[^\u4e00-\u9fa5a-zA-Z]/g, '').slice(0, 2),
			// 地址输入过滤
			addressFormatter: (value) => value.replace(/[^\u4e00-\u9fa5a-zA-Z0-9#-]/g, ''),

			// 表单验证
			formVerify: false,

			// 协议
			agreementState: [],

			// 协议确认弹窗
			showAgreementValidationPopup: false,
			// 城市选择器
			cityPopup: {
				show: false
			},
			// 选择器
			selectPopup: {
				show: false,
				list: []
			},
			// 选择器
			linkSelectPopup: {
				show: false,
				list: []
			},
			// 通用弹窗
			promptPopup: {
				show: false
			}
		};
	},
	computed: {
		...mapState(['currentAddress', 'userInfo', 'publishFormPopup']),
		...mapGetters(['getterIsLogin']),
		// 城市名称
		areaName() {
			return [this.form.area.provinceName, this.form.area.cityName, this.form.area.districtName].filter((e) => !uni.$u.test.isEmpty(e)).join('/');
		},
		// 预算
		budgetName() {
			return this.form.budgetMin;
		},
		// 业务类型名称
		serviceCategoryName() {
			if (!uni.$u.test.isEmpty(this.form.serviceCategory)) {
				return this.form.serviceCategory.map((e) => e.secondName).join('/');
			}
			return '';
		},
		// 行业分类
		industryCategoryName() {
			if (!uni.$u.test.isEmpty(this.form.industryCategory)) {
				return this.form.industryCategory.map((e) => e.name).join('/');
			}
			return '';
		},
		// 资质类型
		qualificationCategoryName() {
			if (!uni.$u.test.isEmpty(this.form.qualificationCategory)) {
				return this.form.qualificationCategory.map((e) => e.name).join('/');
			}
			return '';
		},
		// 申报类型
		declarationCategoryName() {
			if (!uni.$u.test.isEmpty(this.form.declarationCategory)) {
				return this.form.declarationCategory.map((e) => e.name).join('/');
			}
			return '';
		},
		// 主题类型
		subjectCategoryName() {
			if (!uni.$u.test.isEmpty(this.form.subjectCategory)) {
				return this.form.subjectCategory.map((e) => e.name).join('/');
			}
			return '';
		}
	},
	watch: {
		// 监弹窗状态
		'publishFormPopup.show': {
			handler(newVal, oldVal) {
				if (newVal && uni.$u.page() == this.inPage) {
					this.show = true;
					this.form.area = this.currentAddress;
				} else {
					this.show = false;
				}
			},
			immediate: true
		}
	},
	mounted() {
		// 交易类型默认选中
		this.form.qualificationTradeType = this.cons.DEMAND_QUALIFICATION_TRADE_TYPE.BUY.type;
	},
	methods: {
		...mapMutations(['SET_PUBLISH_FORM_POPUP']),
		...mapActions(['agreementProtocol']),

		// 展示选择器
		onShowSelectPopup(type) {
			const popupConfigMap = {
				city: {
					target: 'cityPopup',
					level: 3,
					valueKey: 'area',
					immediate: true
				},
				serviceCategory: {
					target: 'linkSelectPopup',
					url: '/category/common/list-by-type',
					reqParam: {
						type: this.cons.CATEGORY_TYPE_SERVICE
					},
					level: 2,
					tree: true,
					valueKey: 'serviceCategory'
				},
				industryCategory: {
					target: 'selectPopup',
					url: '/category/common/list-by-type',
					reqParam: {
						type: this.cons.CATEGORY_TYPE_INDUSTRY
					},
					mode: 'grid',
					maxNum: 1,
					valueKey: 'industryCategory'
				},
				qualificationCategory: {
					target: 'selectPopup',
					url: '/category/common/list-by-type',
					reqParam: {
						type: this.cons.CATEGORY_TYPE_QUALIFICATION
					},
					mode: 'grid',
					maxNum: 1,
					valueKey: 'qualificationCategory'
				},
				declarationCategory: {
					target: 'selectPopup',
					url: '/category/common/list-by-type',
					reqParam: {
						type: this.cons.CATEGORY_TYPE_DECLARATION
					},
					mode: 'grid',
					maxNum: 1,
					valueKey: 'declarationCategory'
				},
				subjectCategory: {
					target: 'selectPopup',
					url: '/category/common/list-by-type',
					reqParam: {
						type: this.cons.CATEGORY_TYPE_SUBJECT
					},
					mode: 'grid',
					maxNum: 1,
					valueKey: 'subjectCategory'
				},
				budget: {
					target: 'selectPopup',
					url: '/category/common/list-by-type',
					reqParam: {
						type: this.cons.CATEGORY_TYPE_SERVICE
					},
					showInput: true,
					maxNum: 1,
					valueKey: 'budget',
					customComplete: true
				}
			};

			const popupConfig = popupConfigMap[type];
			if (!popupConfig) return;

			const value = this.form[popupConfig.valueKey];

			// 直接展示，无需请求
			if (popupConfig.immediate) {
				this[popupConfig.target] = {
					show: true,
					level: popupConfig.level,
					value,
					complete: (res) => {
						this.form[popupConfig.valueKey] = res;
					}
				};
				return;
			}

			// 请求 + 设置
			uni.$u.http.get(popupConfig.url, { params: popupConfig.reqParam }).then((res) => {
				const list = popupConfig.tree ? handleTree(res) : res;

				this[popupConfig.target] = {
					show: true,
					level: popupConfig.level,
					mode: popupConfig.mode,
					maxNum: popupConfig.maxNum,
					list,
					showInput: popupConfig.showInput,
					value,
					complete: (res) => {
						if (popupConfig.valueKey === 'budget') {
							if (!uni.$u.test.isEmpty(res.inputText)) {
								this.form.budgetMin = res.inputText;
								this.form.budgetMax = res.inputText;
							} else {
							}
						} else {
							this.form[popupConfig.valueKey] = res.result ?? res;
						}
					}
				};
			});
		},
		// 校验表达
		checkFormVerify(value, tip) {
			if (uni.$u.test.isEmpty(value)) {
				uni.$u.toast(tip);
				return;
			}
		},
		onPrepareSubmit() {
			this.formVerify = true;

			// 定义验证规则
			const validations = {
				common: [
					{ condition: uni.$u.test.isEmpty(this.form.area), message: '请选择所在地区' },
					{ condition: uni.$u.test.isEmpty(this.form.contactTitle), message: '请填写称呼' }
				],
				[this.cons.DEMAND_ORIGIN_SUBTYPE_NORMAL]: [
					{ condition: uni.$u.test.isEmpty(this.form.serviceCategory), message: '请选择业务类型' },
					{ condition: uni.$u.test.isEmpty(this.form.budgetMin), message: '请选择预算' }
				],
				[this.cons.DEMAND_ORIGIN_SUBTYPE_QUALIFICATION]: [{ condition: uni.$u.test.isEmpty(this.form.budgetMin), message: '请选择预算报价' }],
				[this.cons.DEMAND_ORIGIN_SUBTYPE_DECLARATION]: [
					{ condition: uni.$u.test.isEmpty(this.form.declarationCategory), message: '请选择申报类型' },
					{ condition: uni.$u.test.isEmpty(this.form.subjectCategory), message: '请选择主体类型' },
					{ condition: uni.$u.test.isEmpty(this.form.budgetMin), message: '请选择预算' }
				]
			};

			// 执行验证
			const allValidations = [...(validations[this.publishFormPopup.type] || []), ...validations.common];

			for (const validation of allValidations) {
				if (validation.condition) {
					uni.$u.toast(validation.message);
					return;
				}
			}

			if (uni.$u.test.isEmpty(this.agreementState)) {
				this.showAgreementValidationPopup = true;
				return;
			}

			this.judgeLogin(() => {
				this.onSubmit();
			});
		},
		// 提交
		onSubmit() {
			const params = {
				originType: this.cons.DEMAND_ORIGIN_TYPE_PUBLISH,
				originSubtype: this.publishFormPopup.type,
				contactTitle: this.form.contactTitle, // 称呼
				budgetMin: this.form.budgetMin,
				budgetMax: this.form.budgetMax,
				sex: this.form.sex, // 性别
				remark: this.form.remark, // 备注
				provinceCode: this.form.area.provinceId, // 地区
				cityCode: this.form.area.cityId,
				districtCode: this.form.area.districtId
			};
			if (this.publishFormPopup.type == this.cons.DEMAND_ORIGIN_SUBTYPE_NORMAL) {
				// 发布需求
				params.serviceCategoryId = this.form.serviceCategory.map((e) => e.secondId).join(','); // 业务类型
			} else if (this.publishFormPopup.type == this.cons.DEMAND_ORIGIN_SUBTYPE_QUALIFICATION) {
				// 发布资质服务需求
				params.qualificationTradeType = this.form.qualificationTradeType;
				params.qualificationCategoryId = this.form.qualificationCategory.map((e) => e.id).join(','); // 资质类型
				params.industryCategoryId = this.form.industryCategory.map((e) => e.id).join(','); // 行业分类
			} else if (this.publishFormPopup.type == this.cons.DEMAND_ORIGIN_SUBTYPE_DECLARATION) {
				// 发布申报服务需求
				params.declarationCategoryId = this.form.declarationCategory.map((e) => e.id).join(','); // 申报类型
				params.subjectCategoryId = this.form.subjectCategory.map((e) => e.id).join(','); // 主体类型
			}
			uni.$u.http.post('/demand/user/create', params, { custom: { showLoading: true, loadingMsg: '提交中..', showSuccess: true, successMsg: '您的需求已提交成功！' } }).then((res) => {
				this.agreementProtocol(this.cons.PROTOCOL_PROTECT);
				this.onClose();
			});
		},
		// 关闭
		onClose() {
			// 重置表单数据
			this.formVerify = false;
			this.form.qualificationTradeType = '';
			this.form.contactTitle = '';
			this.form.serviceCategory = [];
			this.form.industryCategory = [];
			this.form.qualificationCategory = [];
			this.form.declarationCategory = [];
			this.form.subjectCategory = [];
			this.form.budgetMin = '';
			this.form.budgetMax = '';
			this.form.remark = '';

			this.SET_PUBLISH_FORM_POPUP({
				show: false
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.close {
	z-index: 20;
	padding: 30rpx;
	position: absolute;
	right: 0;
	top: 0;

	image {
		width: 26rpx;
		height: 26rpx;
	}
}

.form-wrap {
	width: 750rpx;
	align-items: center;
	border-radius: 30rpx 30rpx 0 0;

	&__header {
		width: 100%;
		height: 240rpx;
		z-index: 5;

		image {
			width: 100%;
			height: 100%;
		}
	}

	&__content {
		flex: 1;
		z-index: 10;
		margin-top: -20rpx;
		padding: 30rpx 30rpx 40rpx 30rpx;
		background-color: #ffffff;
		border-radius: 30rpx;
		align-items: center;

		.form-row {
			width: 100%;
			margin-top: 10rpx;
			align-items: center;
			justify-content: space-between;
		}

		.form-item {
			height: 100rpx;
			padding: 0 20rpx;
			border-radius: 15rpx;
			background-color: #f3f3f5;
			align-items: center;
			justify-content: space-between;

			&--w100 {
				width: 690rpx;
			}

			&--w50 {
				width: 340rpx;
			}

			&--w40 {
				width: 260rpx;
			}

			&--border {
				border: 1px solid red;
			}

			.input {
				flex: 1;
				font-size: 30rpx;
				color: #1e2134;
			}

			.textarea {
				width: 100%;
				height: 200rpx;
				padding: 20rpx 0;
			}

			.arrow {
				width: 10rpx;
				height: 17rpx;
			}
		}

		.protocol {
			width: 100%;
			margin-top: 40rpx;
			justify-content: flex-start;
			align-items: center;
		}

		.submit {
			width: 100%;
			height: 100rpx;
			margin: 20rpx 0 0 0;
			background-color: #444bf1;
			border-radius: 10rpx;
			font-size: 36rpx;
			font-weight: bold;
			color: #ffffff;
			align-items: center;
			justify-content: center;
		}
	}
}

/* 校验 */
.verify {
	animation: shake 0.2s ease-in-out 1;
	border: 1px solid red;
}

@keyframes shake {
	0%,
	100% {
		transform: translateX(0);
	}
	25% {
		transform: translateX(-8px);
	}
	50% {
		transform: translateX(8px);
	}
	75% {
		transform: translateX(-8px);
	}
}
</style>
