<template>
	<view class="x-payment-option">
		<view class="flex x-payment-option__cell" v-for="(item, index) in payChannels" :key="index" @click="onChannelClick(index)">
			<view class="flex x-payment-option__cell__left">
				<image class="x-payment-option__cell__left__icon" :src="item.icon"></image>
				<text class="x-payment-option__cell__left__title">{{ item.title }}</text>
			</view>
			<image class="x-payment-option__cell__check-box" :src="item.code == value ? iImage('ic_check_sel.png') : iImage('ic_check_nor.png')"></image>
		</view>
	</view>
</template>

<script>
export default {
	name: 'x-payment-option',
	props: {
		// 双向绑定的支付渠道code
		value: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			// 支付方式
			payChannels: []
		};
	},
	created() {
		// 初始化支付渠道
		this.payChannels = [
			{
				code: 'wx_app',
				icon: this.iImage('ic_pay_wx.png'),
				title: '微信支付',
				subtitle: '',
				sort: 1,
				recommend: false
			},
			// {
			// 	code: 'wx_lite',
			// 	icon: this.iImage('ic_pay_wx.png'),
			// 	title: '微信支付',
			// 	subtitle: '',
			// 	sort: 1,
			// 	recommend: false
			// },
			{
				code: 'alipay_app',
				icon: this.iImage('ic_pay_al.png'),
				title: '支付宝支付',
				subtitle: '',
				sort: 5,
				recommend: false
			}
		];

		// #ifdef MP
		// this.payChannels = this.payChannels.filter((e) => e.code !== 'alipay');
		// #endif

		// 默认选中第一个支付方式
		this.onChannelClick(0);

		// 获取支付方式
		// this.queryPayChannel();
	},
	methods: {
		// 获取支付渠道
		// queryPayChannel() {
		// 	uni.$u.http
		// 		.get('/pay/channel/get-enable-code-list', { params: { appId: 0 } })
		// 		.then((res) => {
		// 			// this.$refs.paging.complete(res.list);
		// 		})
		// 		.catch((err) => {});
		// },
		// 选择支付方式
		onChannelClick(index) {
			const item = this.payChannels[index];
			this.$emit('input', item.code);
		}
	}
};
</script>

<style lang="scss" scoped>
.x-payment-option {
	flex: 1;

	&__cell {
		padding: 25rpx 0;
		justify-content: space-between;

		&__left {
			align-items: center;

			&__icon {
				width: 46rpx;
				height: 46rpx;
			}
			&__title {
				margin-left: 15rpx;
				font-weight: 500;
				font-size: 30rpx;
				color: #090b35;
				line-height: 40rpx;
			}
		}

		&__check-box {
			width: 34rpx;
			height: 34rpx;
		}
	}
}
</style>
