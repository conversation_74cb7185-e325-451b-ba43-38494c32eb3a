<template>
	<view v-if="!isCanBack" class="row left-box" :style="{borderColor:borderColor}" @click.stop="goHome">
		<u-icon name="arrow-left" size="19" :color="color"></u-icon>
		<u-line direction="column" :hairline="false" length="16" margin="0 8px" :color="color"></u-line>
		<u-icon name="home" size="20" :color="color"></u-icon>
	</view>
	<view v-else>
		<u-icon :customStyle="{marginRight:'15rpx'}" name="arrow-left" size="20px" :color="color"
			@click="goBack"></u-icon>
	</view>
</template>

<script>
	export default {
		name: "back-home-button", 
		props: {
			// 主体颜色
			color: {
				type: String,
				default: '#303133' 
			}
		},
		data() {
			return {

			};
		},
		computed: {
			isCanBack() {
				let pages = getCurrentPages()
				return pages.length > 1
			},
			borderColor() {
				let alpha = 0.5
				// 获取边框颜色
				const hexToRgb = (hex) => ({
					r: parseInt(hex.slice(1, 3), 16),
					g: parseInt(hex.slice(3, 5), 16),
					b: parseInt(hex.slice(5, 7), 16),
				});
				const rgbToHex = (rgb) => `#${(1 << 24 | rgb.r << 16 | rgb.g << 8 | rgb.b).toString(16).slice(1)}`;
				const opacityToHex = (opacity) => Math.round(opacity * 255).toString(16).toUpperCase();
				const rgb = hexToRgb(this.color);
				return `${rgbToHex(rgb)}${opacityToHex(alpha)}`;
			}
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack()
			},
			// 跳转首页
			goHome() {
				uni.reLaunch({
					url: '/pages/home/<USER>'
				});
			}
		}
	}
</script>

<style>
	.row {
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.col {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.left-box {
		border-width: 1px;
		border-radius: 100px;
		border-style: solid;
		padding: 3px 7px;
	}
</style>