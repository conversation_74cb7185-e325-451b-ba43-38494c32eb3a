<template>
	<view class="popup-content">
		<text class="title">请阅读并同意以下条款</text>
		<view class="content">
			<text class="text" v-for="(item, index) in fragments" :key="index" @click="onLinkTap(item)" :style="{ color: item.color }">
				{{ item.name }}
			</text>
		</view>
		<u-gap height="50rpx"></u-gap>
		<u-line></u-line>
		<view class="row button-view">
			<button v-if="nextType == 'WxLogin'" class="button" open-type="getPhoneNumber" @getphonenumber="onPhoneNumber">
				<text class="btn-text">同意并继续</text>
			</button>
			<button v-else class="button" @click="onConfirm">
				<text class="btn-text">同意并继续</text>
			</button>
		</view>
		<u-safe-bottom></u-safe-bottom>
	</view>
</template>

<script>
export default {
	name: 'popup-agreement-validation',
	props: {
		value: {
			type: Array,
			default: () => []
		},
		// 协议类型
		protocolType: {
			type: [String, Array],
			default() {
				return '';
			}
		},
		// 同意协议后的操作类型 【WxLogin:微信登录】
		nextType: {
			type: String,
			default() {
				return '';
			}
		}
	},
	data() {
		return {};
	},
	computed: {
		// 协议片段
		fragments() {
			let ary = [];
			ary.push({
				type: 'text',
				name: '我已阅读并同意',
				color: '#333333'
			});
			if (Array.isArray(this.protocolType)) {
				// 数组
				this.protocolType.forEach((v, i) => {
					ary.push({
						type: 'link',
						name: `《${this.cons.PROTOCOL_MAPS[v]}》`,
						href: v,
						color: '#6C68F0'
					});
					// 如果不是最后一个协议，添加 '、' 文本片段
					if (i < this.protocolType.length - 1) {
						ary.push({
							type: 'text',
							name: '、',
							color: '#333333'
						});
					}
				});
			} else if (typeof this.protocolType === 'string') {
				// 字符串
				ary.push({
					type: 'link',
					name: `《${this.cons.PROTOCOL_MAPS[this.protocolType]}》`,
					href: this.protocolType,
					color: '#6C68F0'
				});
			}
			return ary;
		}
	},
	created() {},
	methods: {
		// 协议链接点击
		onLinkTap(e) {
			if (e.type === 'link') {
				onJump({
					url: '/pagesMine/privacy/agreement',
					params: {
						type: e.href
					}
				});
			}
		},
		// 同意
		onConfirm() {
			this.$emit('input', ['procotol']);
			this.onClose();
			this.$emit('confirm');
		},
		// 微信小程序获取手机号
		onPhoneNumber(e) {
			this.$emit('input', ['procotol']);
			this.onClose();
			this.$emit('confirm', {
				type: this.nextType,
				detail: e
			});
		},
		onClose() {
			this.$emit('close');
		}
	}
};
</script>
<style lang="scss" scoped>
.popup-content {
	width: 750rpx !important;
	min-width: 750rpx;
	background: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.title {
	margin-top: 50rpx;
	font-size: 34rpx;
	font-weight: bold;
	color: #333333;
}

.content {
	margin: 40rpx 40rpx 0rpx 40rpx;
	display: flex;
	flex-direction: row;
	justify-content: center;
	white-space: nowrap;
}

.text {
	font-size: 32rpx;
	font-weight: 400;
}

.button-view {
	width: 690rpx;
	height: 100rpx;
	margin-bottom: 50rpx;
	border-radius: 15rpx;
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	background-color: #6c68f0;
}

.button {
	flex: 1;
	height: 100rpx;
	// border-radius: 50rpx;
	background: transparent;
	background-color: transparent;
}

.btn-text {
	color: #ffffff;
	font-weight: 800;
	font-size: 35rpx;
	line-height: 100rpx;
	text-align: center;
}
</style>
