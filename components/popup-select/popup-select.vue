<template>
	<view class="col popup-select">
		<view class="col container">
			<view class="row title-view">
				<text class="title-view__title">{{ title }}</text>
			</view>
			<view class="flex input-wrap" v-if="showInput">
				<view class="flex input-wrap__box">
					<input class="input-wrap__box__input" v-model="inputText" :cursor-spacing="30" :placeholder="inputArg.placeholder" :maxlength="inputArg.maxlength" />
					<view slot="input-wrap__box__suffix" v-if="!$u.test.isEmpty(inputArg.suffix)">
						{{ inputArg.suffix }}
					</view>
				</view>
				<view class="row input-wrap__btn" @click="onInputConfirm">确定</view>
			</view>
			<!-- 列表样式 -->
			<scroll-view scroll-y v-if="mode == 'list'" class="list-scroll" :scroll-into-view="scrollToView">
				<view :id="`list-cell${index}`" class="row list-scroll__cell" :class="{ 'list-scroll__active-cell': selectIndexs.includes(index) }" v-for="(item, index) in list" :key="index" @click="onItemClick(index)">
					<text class="list-scroll__cell__text" :class="{ 'list-scroll__active-cell__text': selectIndexs.includes(index) }">{{ item[keyName] }}</text>
					<image class="list-scroll__cell__check" :src="selectIndexs.includes(index) ? '/static/ic_check_sel.png' : '/static/ic_check_nor.png'" mode="aspectFit"></image>
				</view>
			</scroll-view>
			<!-- 九宫格样式 -->
			<scroll-view v-else-if="mode == 'grid'" scroll-y class="grid-scroll">
				<view class="row grid-scroll__wrapper">
					<view class="row grid-scroll__wrapper__cell" :class="{ 'grid-scroll__wrapper__active-cell': selectIndexs.includes(index) }" :style="gridStyle" v-for="(item, index) in list" :key="index" @click="onItemClick(index)">
						<text class="grid-scroll__wrapper__cell__text" :class="{ 'list-scroll__wrapper__active-cell__text': selectIndexs.includes(index) }">{{ item[keyName] }}</text>
					</view>
				</view>
			</scroll-view>
			<view v-if="maxNum > 1" class="col bottom-wrapper">
				<view class="row bottom-wrapper__confirm" @click="onConfirm">{{ `确认(${selectIndexs.length}/${maxNum})` }}</view>
			</view>
			<u-safe-bottom></u-safe-bottom>
		</view>
	</view>
</template>

<script>
export default {
	name: 'popup-select',
	props: {
		show: {
			type: Boolean,
			default: false
		},
		// 弹窗标识
		ident: {
			type: String,
			default: 'popup-select'
		},
		// 标题
		title: {
			type: String,
			default: '请选择'
		},
		// 标签数组
		list: {
			type: Array,
			default: function () {
				return [];
			}
		},
		// 展示风格【list-列表  grid-九宫格】
		mode: {
			type: String,
			default: 'grid'
		},
		// mode为grid样式下显示的列数
		column: {
			type: Number,
			default: 2
		},
		// 最大选择的数量(默认1为单选，大于1为多选)
		maxNum: {
			type: Number,
			default: 1
		},
		// 默认读取的键名
		keyName: {
			type: String,
			default: 'name'
		},
		// 选中的标签索引
		value: {
			type: Array,
			default: function () {
				return [];
			}
		},
		// 是否展示输入框
		showInput: {
			type: Boolean,
			default: false
		},
		// 输入框属性
		inputArg: {
			type: Object,
			default: function () {
				return { length: 10, type: 'text', placeholder: '请输入', formatter: '', suffix: '' };
			}
		},
		// 选择完成事件
		complete: {
			type: Function,
			default: () => {}
		}
	},
	watch: {
		// #ifdef MP-WEIXIN
		show(newValue, oldValue) {
			if (newValue) {
				this.init();
			}
		}
		// #endif
	},
	data() {
		return {
			// 选中的索引
			selectIndexs: [],
			// 默认滚动到指定位置
			scrollToView: '',
			// 输入框内容
			inputText: ''
		};
	},
	computed: {
		gridStyle() {
			let itemWidth = (750 - 60 - (this.column - 1) * 35) / this.column;
			return {
				width: `${itemWidth}rpx`
			};
		}
	},
	created() {
		this.init();
	},
	methods: {
		init() {
			if (!uni.$u.test.isEmpty(this.value)) {
				this.selectIndexs = this.value;
				// 设置默认滚动到第一个
				if (this.mode == 'list') {
					let minIndex = this.selectIndexs.sort(function (a, b) {
						return a - b;
					})[0];
					this.$nextTick(() => {
						this.scrollToView = `list-cell${minIndex}`;
					});
				}
			} else {
				this.selectIndexs = [];
			}
		},
		// 点击条目Item
		onItemClick(i) {
			if (this.maxNum == 1) {
				// 单选
				this.selectIndexs = [i];
				this.onConfirm();
			} else {
				// 多选
				if (this.selectIndexs.includes(i)) {
					this.selectIndexs = this.selectIndexs.filter((index) => index !== i);
				} else {
					if (this.selectIndexs.length >= this.maxNum) {
						uni.showToast({
							title: `最多只能选择${this.maxNum}个`,
							icon: 'none'
						});
						return;
					}
					this.selectIndexs.push(i);
				}
			}
		},
		// 输入框确认
		onInputConfirm() {
			if (uni.$u.test.isEmpty(this.inputText)) {
				uni.showToast({
					title: this.inputArg.placeholder ?? '请填写',
					icon: 'none'
				});
				return;
			}
			this.onConfirm();
			this.onCancel();
		},
		// 确认
		onConfirm() {
			const res = {
				ident: this.ident,
				indexs: this.selectIndexs,
				result: this.selectIndexs.map((index) => this.list[index]),
				inputText: this.inputText
			};
			this.complete && this.complete(res);
			this.$emit('confirm', res);
			this.onCancel();
		},
		// 取消
		onCancel() {
			this.$emit('close', {
				ident: this.ident
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.row {
	display: flex;
	flex-direction: row;
}

.col {
	display: flex;
	flex-direction: column;
}

.popup-select {
	width: 750rpx;
	padding-bottom: 40rpx;
	background: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
}

.title-view {
	width: 100%;
	height: 120rpx;
	border-bottom: 5px solid #f8f8f8;
	justify-content: center;
	align-items: center;

	&__title {
		font-size: 36rpx;
		font-weight: 500;
		color: #333333;
	}
}

.input-wrap {
	width: 690rpx;
	margin: 0 auto 30rpx auto;
	justify-content: space-between;
	&__box {
		flex: 1;
		height: 80rpx;
		padding: 0 30rpx;
		background: #ffffff;
		border-radius: 10rpx;
		border: 0.8px solid #444bf1;

		&__input {
			flex: 1;
			font-size: 30rpx;
			color: #1e2134;
		}

		&__suffix {
			font-size: 30rpx;
			color: #1e2134;
		}
	}

	&__btn {
		height: 80rpx;
		width: 120rpx;
		margin-left: 15rpx;
		background: #444bf1;
		border-radius: 10rpx;
		color: #ffffff;
		font-weight: bold;
		font-size: 30rpx;
		color: #ffffff;
		align-items: center;
		justify-content: center;
	}
}

.list-scroll {
	width: 100%;
	height: 600rpx;
	max-height: 600rpx;
	overflow: auto;
	justify-content: center;
	align-items: center;

	&__cell {
		width: 100%;
		height: 100rpx;
		padding: 0 30rpx;
		justify-content: space-between;
		align-items: center;

		&__text {
			font-size: 30rpx;
			font-weight: 500;
			color: #333333;
		}

		&__check {
			width: 36rpx;
			height: 36rpx;
		}
	}

	&__active-cell {
		&__text {
			color: #444bf1;
		}
	}
}

.grid-scroll {
	width: 100%;
	max-height: 600rpx;
	padding: 0 30rpx;

	&__wrapper {
		width: 100%;
		height: 100%;
		justify-content: space-between;
		flex-wrap: wrap;

		&__cell {
			width: 300rpx;
			height: 80rpx;
			margin-bottom: 30rpx;
			border-radius: 10rpx;
			background: #f8f8f8;
			align-items: center;
			justify-content: center;

			&__text {
				font-size: 30rpx;
				font-weight: 500;
				color: #333333;
			}
		}

		&__active-cell {
			background: #f4f4ff;
			border: 0.8px solid #444bf1;

			&__text {
				color: #ffffff;
			}
		}
	}
}

.bottom-wrapper {
	padding: 30rpx 30rpx 30rpx 30rpx;
	background: #ffffff;

	&__confirm {
		height: 100rpx;
		justify-content: center;
		align-items: center;
		font-size: 34rpx;
		font-weight: 600;
		color: #ffffff;
		border-radius: 10rpx;
		background: #444bf1;
	}
}
</style>
