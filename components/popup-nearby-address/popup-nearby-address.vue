<template>
	<view class="flex-col wrap-popup">
		<view class="flex-col wrap-popup__title">定位地址</view>
		<view class="flex-col wrap-popup__search">
			<u-search v-model="searchText" placeholder="搜索小区名/大厦名" bgColor="#F7F7F7" borderColor="#F7F7F7" :showAction="true" actionText="搜索" :animation="true" @search="onSearch" @custom="onSearch"></u-search>
		</view>
		<view class="flex-col wrap-popup__address">
			<text class="wrap-popup__address__title">当前定位</text>
			<view class="flex wrap-popup__address__value">
				<text class="wrap-popup__address__value__loc" @click="cityPopup.show = true">{{ curAddressText }}</text>
				<image class="wrap-popup__address__value__arrow" src="/static/image/ic_small_arrow_right.png" mode="aspectFit"></image>
				<text class="wrap-popup__address__value__btn" @click="getLocation">重新定位</text>
			</view>
		</view>
		<view class="flex-col wrap-popup__content">
			<view style="padding: 40rpx" v-if="loading">
				<u-loading-icon :show="loading" mode="circle" text="加载中" textSize="16"></u-loading-icon>
			</view>
			<scroll-view v-else scroll-y class="flex-col wrap-popup__content__scroll">
				<view class="flex wrap-popup__content__scroll__item" v-for="(item, index) in list" :key="index" @click="onListItemClick(item)">
					<view class="flex wrap-popup__content__scroll__item__wrap">
						<image class="wrap-popup__content__scroll__item__wrap__ic" src="/pagesMine/static/ic_address_loc.png" mode="aspectFill"></image>
						<text class="wrap-popup__content__scroll__item__wrap__mark" v-if="parseFloat(item.distance) == 0">[当前位置]</text>
						<text class="wrap-popup__content__scroll__item__wrap__name">{{ item.name }}</text>
					</view>
					<view class="flex wrap-popup__content__scroll__item__detail">
						<text class="wrap-popup__content__scroll__item__detail__text line1">{{ item.address }}</text>
						<text class="wrap-popup__content__scroll__item__detail__distance" v-if="parseFloat(item.distance) > 0">{{ Math.round(parseFloat(item.distance)) }}m</text>
					</view>
				</view>
			</scroll-view>
		</view>
		<!-- 城市选择弹窗 -->
		<u-popup :show="cityPopup.show" bgColor="transparent" mode="bottom" @close="cityPopup.show = false" :safe-area-inset-bottom="false" closeable>
			<popup-city :length="2" :show="cityPopup.show" :value="cityPopup.value" @confirm="onSelectedCity"></popup-city>
		</u-popup>
	</view>
</template>

<script>
import { GPSLocation, placeSearch2 } from '@/common/location.js';
export default {
	name: 'popup-nearby-address',
	props: {
		show: {
			type: Boolean,
			default: false
		}
	},
	computed: {
		curAddressText() {
			let text = ` ${this.curAddress.cityName || ''}  ${this.curAddress.name || ''}`;
			if (uni.$u.test.isEmpty(uni.$u.trim(text, 'all'))) {
				return '定位中';
			}
			return text;
		}
	},
	data() {
		return {
			// 当前位置
			curAddress: {},
			// 搜索文本
			searchText: '',
			// 数据列表
			list: [],

			// 城市选择
			cityPopup: {
				show: false
			},

			loading: true
		};
	},
	watch: {
		// 监听弹窗状态
		// 小程序使用，因为小程序弹窗的create只会执行一次
		show: function (val) {
			if (val) {
				// #ifdef MP
				this.init();
				// #endif
			}
		}
	},
	created() {
		this.init();
	},
	methods: {
		init() {
			if (uni.$u.test.isEmpty(this.curAddress) || uni.$u.test.isEmpty(this.list)) {
				this.getLocation();
			}
		},
		// 选择城市回调
		onSelectedCity(resutl) {
			this.cityPopup.show = false;
			this.curAddress = {
				provinceName: resutl.provinceName,
				provinceId: resutl.provinceId,
				cityName: resutl.cityName,
				cityId: resutl.cityId,
				districtName: resutl.areaName,
				districtId: resutl.areaId
			};
		},
		onListItemClick(item) {
			this.$emit('confirm', item);
		},
		onSearch(text) {
			this.loading = true;
			let params = {
				keywords: encodeURIComponent(text),
				cityLimit: '1',
				region: this.curAddress.districtId || this.curAddress.cityId
			};
			placeSearch2(params)
				.then((res) => {
					this.list = res;
					// 加载状态
					this.loading = false;
				})
				.catch((error) => {
					// 加载状态
					this.loading = false;
				});
		},
		// 获取当前位置以及周边信息
		getLocation() {
			this.loading = true;
			GPSLocation()
				.then((res) => {
					this.curAddress = {
						name: res.name,
						formatAddress: res.formatAddress,
						provinceName: res.provinceName,
						provinceId: res.provinceId,
						cityName: res.cityName,
						cityId: res.cityId,
						districtName: res.districtName,
						districtId: res.districtId,
						townshipName: res.townshipName,
						townshipId: res.townshipCode,
						lat: res.lat,
						lng: res.lng
					};
					this.list = res.pois;
					// 加载状态
					this.loading = false;
				})
				.catch((error) => {
					// 加载状态
					this.loading = false;
				});
		}
	}
};
</script>

<style lang="scss" scoped>
.wrap-popup {
	width: 750rpx;
	background-color: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
	align-items: center;
	justify-content: center;
	&__title {
		height: 100rpx;
		font-weight: bold;
		font-size: 32rpx;
		color: #333333;
		// border-bottom: 5px solid #f8f8f8;
		align-items: center;
		justify-content: center;
	}
	&__search {
		width: 100%;
		padding: 0 30rpx;
	}
	&__address {
		width: 100%;
		padding: 15rpx 30rpx;
		border-bottom: 5px solid #f7f7f7;
		&__title {
			font-weight: 500;
			font-size: 24rpx;
			color: #999999;
		}
		&__value {
			margin-top: 20rpx;
			// justify-content: space-between;
			&__loc {
				font-weight: 700;
				font-size: 30rpx;
				color: #333333;
			}
			&__arrow{
				width: 40rpx;
				height: 20rpx;
				margin-left: 5rpx;
				transform: rotate(90deg)
			}
			&__btn {
				margin-left: auto;
				font-weight: bold;
				font-size: 24rpx;
				color: #444BF1;
			}
		}
	}

	&__content {
		width: 100%;
		height: calc(100vh * 0.6);
		max-height: calc(100vh * 0.6);

		&__scroll {
			flex: 1;
			height: 100%;
			padding: 0 30rpx;
			&__item {
				flex: 1;
				padding: 25rpx 0;
				flex-direction: column;
				align-items: flex-start;
				border-bottom: 0.6px solid #ebebeb;
				&__wrap {
					&__ic {
						width: 35rpx;
						height: 35rpx;
					}
					&__mark {
						margin-left: 10rpx;
						font-weight: 600;
						font-size: 30rpx;
						color: #444BF1;
					}
					&__name {
						margin-left: 10rpx;
						font-weight: 600;
						font-size: 28rpx;
						color: #333333;
					}
				}
				&__detail {
					width: 100%;
					margin-top: 15rpx;
					padding-left: 45rpx;
					justify-content: space-between;

					&__text {
						font-size: 26rpx;
						color: #666666;
					}
					&__distance {
						font-size: 26rpx;
						color: #666666;
					}
				}
			}
		}
	}
}
</style>
