<template>
	<view>
		<!-- 加载动画组件 -->
		<x-loading></x-loading>
		<!-- 登录弹窗 -->
		<x-login></x-login>
		<!-- 消息通知 -->
		<x-notify></x-notify>
		<!-- 发布弹窗 -->
		<popup-publish :inPage="currentPage"></popup-publish>
		<!-- 发布表单弹窗 -->
		<popup-publish-form :inPage="currentPage"></popup-publish-form>
		<!-- #ifdef APP-PLUS -->
		<!-- 通知设置 -->
		<!-- <popup-notice-setting></popup-notice-setting> -->
		<!-- #endif -->
	</view>
</template>

<script>
import xLoading from '@/components/x-loading/x-loading.vue';
import xLogin from '@/components/x-login/x-login.vue';
import xNotify from '@/components/x-notify/x-notify.vue';
import popupPublish from '@/components/popup-publish/popup-publish.vue';
export default {
	components: {
		xLoading,
		xLogin,
		xNotify,
		popupPublish
	},
	data() {
		return {};
	},
	computed: {
		// 当前页面路径
		currentPage() {
			const pages = getCurrentPages();
			const curPage = pages[pages.length - 1];
			return `/${curPage.route}`;
		}
	},
	methods: {}
};
</script>
<style lang="scss" scoped></style>
