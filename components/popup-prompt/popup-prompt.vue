<!-- ************** 通用提示框 ************** -->
<!-- ************** 配置有些繁琐 有时间重构 ************** -->
<template>
	<view class="col popup-prompt">
		<view class="col container" :style="[$u.addStyle(customStyle)]">
			<image class="bg-img" :src="backgroundImage"></image>
			<image v-if="!$u.test.isEmpty(icon)" :style="[$u.addStyle(iconStyle)]" class="icon" :src="icon" mode="aspectFit"></image>
			<!-- 标题 -->
			<text class="title" :style="[$u.addStyle(titleStyle)]">{{ title || '' }}</text>
			<!-- 描述 -->
			<scroll-view scroll-y class="message" :style="[$u.addStyle(messageStyle)]">
				<rich-text :nodes="richTextNode"></rich-text>
			</scroll-view>
			<!-- 按钮组 -->
			<!-- TODO: button-box样式有一个问题 如果按钮大于两个并且是垂直排列的情况下无法居中 【添加按钮的时候 添加margin 设置左右 auto 即可解决】 -->
			<!-- 小程序中必须这样写'[btnBoxStyle]'  否则无效 -->
			<view class="row button-box" :style="[btnBoxStyle]">
				<view class="row button" v-for="(item, index) in actions" :key="index" :style="[btnStyle, $u.addStyle(item.customStyle)]">
					<image v-if="!$u.test.isEmpty(item.backgroundImage)" class="button-bg" :src="item.backgroundImage" mode="aspectFit" />
					<!-- #ifdef MP -->
					<button v-if="item.type == 'now-share'" class="row mp-share-button-con" open-type="share">
						<!-- #ifdef APP-NVUE -->
						<text :style="btnTextStyle(item)">{{ item.title }}</text>
						<!-- #endif -->
						<!-- #ifndef APP-NVUE -->
						{{ item.title }}
						<!-- #endif -->
					</button>
					<view v-else class="row button-con" @tap="onAction(item)">
						<!-- #ifdef APP-NVUE -->
						<text :style="btnTextStyle(item)">{{ item.title }}</text>
						<!-- #endif -->
						<!-- #ifndef APP-NVUE -->
						{{ item.title }}
						<!-- #endif -->
					</view>
					<!-- #endif -->

					<!-- #ifdef APP-PLUS -->
					<view class="row button-con" @tap.stop="onAction(item)">
						<!-- #ifdef APP-NVUE -->
						<text :style="btnTextStyle(item)">{{ item.title }}</text>
						<!-- #endif -->
						<!-- #ifndef APP-NVUE -->
						{{ item.title }}
						<!-- #endif -->
					</view>
					<!-- #endif -->
				</view>
			</view>
			<!-- 小贴士 -->
			<view v-if="!$u.test.isEmpty(tipText)" class="tip">
				<rich-text :nodes="tipText || ''"></rich-text>
			</view>
		</view>
		<!-- 关闭按钮 -->
		<image v-if="showClose" class="close" src="/static/image/ic_close_w.png" mode="widthFix" @click="onClose" />
	</view>
</template>

<script>
import parseHtml from '@/plugins/html-parser.js';
export default {
	name: 'popup-prompt',
	// #ifdef APP-NVUE
	mixins: [uni.$u.mpMixin, uni.$u.mixin],
	// #endif
	props: {
		// 弹窗类型标识
		ident: {
			type: String,
			default: ''
		},
		// 图标
		icon: {
			type: String,
			default: ''
		},
		// 图标样式
		iconStyle: {
			type: [Object, String],
			default: ''
		},
		// 标题
		title: {
			type: String,
			default: ''
		},
		// 标题样式
		titleStyle: {
			type: [Object, String],
			default: ''
		},
		// 消息内容
		message: {
			type: String,
			default: ''
		},
		// 消息内容样式
		messageStyle: {
			type: [Object, String],
			default: ''
		},
		// 按钮组
		actions: {
			type: Array,
			default: () => []
		},
		// 确认按钮底部的提示文字
		tipText: {
			type: String,
			default: ''
		},
		// 是否展示关闭按钮
		showClose: {
			type: Boolean,
			default: true
		},
		// 按钮组是否固定到底部
		buttonBoxFixedBottom: {
			type: Boolean,
			default: false
		},
		// 按钮组的JustifyContent样式
		buttonBoxJustifyContent: {
			type: String,
			default: ''
		},
		// 拓展信息
		extra: {
			type: [Boolean, String, Object, Array],
			default: () => {}
		},
		// 背景图片
		backgroundImage: {
			type: String,
			default: ''
		},
		// 弹窗样式
		customStyle: {
			type: [Object, String],
			default: ''
		},
		// 点击自定义关闭按钮的回调函数
		cancel: {
			type: Function,
			default: null
		}
	},
	computed: {
		// APP-NVUE情况下 rich-text的node需要为数组格式
		richTextNode() {
			return parseHtml(this.message);
		},
		// 按钮组样式
		btnBoxStyle() {
			let style = {
				justifyContent: this.actions.length > 1 ? 'space-between' : 'center'
			};
			if (!uni.$u.test.isEmpty(this.buttonBoxJustifyContent)) {
				style.justifyContent = this.buttonBoxJustifyContent;
			}
			if (this.buttonBoxFixedBottom) {
				style['position'] = 'absolute';
				style['bottom'] = '60rpx';
			}
			return style;
		}
	},
	data() {
		return {
			btnStyle: {
				width: '240rpx'
			}
		};
	},
	methods: {
		// 按钮文字样式
		btnTextStyle(style) {
			return {
				color: style.color || '#FFFFFF',
				fontSize: style.fontSize || '36rpx',
				fontWeight: style.fontWeight || '500'
			};
		},
		// 取消
		onClose() {
			const res = {
				ident: this.ident,
				extra: this.extra
			};
			this.cancel && this.cancel(res);
			this.$emit('cancel', res);
		},
		// 按钮事件
		onAction(e) {
			const res = {
				ident: this.ident,
				type: e.type,
				extra: this.extra
			};
			e.action && e.action(res);
			this.$emit('action', res);
		}
	}
};
</script>

<style lang="scss" scoped>
.row {
	display: flex;
	flex-direction: row;
}

.col {
	display: flex;
	flex-direction: column;
}

.popup-prompt {
	justify-content: center;
	align-items: center;
}

.container {
	width: 560rpx;
	padding: 50rpx 30rpx 50rpx 30rpx;
	justify-content: center;
	align-items: center;
	border-radius: 30rpx;
	
	background-color: #F1F2FF;
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 100% 100%;
	position: relative;
}

.bg-img {
	width: 100%;
	height: 100%;
	z-index: -1;
	position: absolute;
	top: 0;
	left: 0;
}

.icon {
	width: 125rpx;
	height: 125rpx;
	margin-bottom: 50rpx;
}

.title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333333;
}

.message {
	max-height: 600rpx;
	margin: 40rpx 5rpx 0 5rpx;
	background: transparent;
	font-size: 30rpx;
	color: #333333;
	text-align: center;
	line-height: 46rpx;
}

.button-box {
	/* #ifdef APP-NVUE */
	flex: 1;
	/* #endif */
	/* #ifndef APP-NVUE */
	width: 100%;
	/* #endif */
	margin-top: 45rpx;
	flex-wrap: wrap;
	justify-content: center;
	align-items: center;
}

.button {
	width: 240rpx;
	height: 80rpx;
	border-radius: 15rpx;
	font-size: 36rpx;
	font-weight: 500;
	align-items: center;
	justify-content: center;
	position: relative;
}

.button-bg {
	z-index: 5;
	/* #ifdef APP-NVUE */
	flex: 1;
	/* #endif */
	/* #ifndef APP-NVUE */
	width: 100%;
	height: 100%;
	/* #endif */
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
}

.button-con {
	z-index: 10;
	/* #ifdef APP-NVUE */
	flex: 1;
	/* #endif */
	/* #ifndef APP-NVUE */
	width: 100%;
	height: 100%;
	/* #endif */
	justify-content: center;
	align-items: center;
}

.mp-share-button-con {
	/* #ifndef APP-NVUE */
	width: 100%;
	height: 100%;
	/* #endif */
	justify-content: center;
	align-items: center;
	background-color: transparent;
	font-size: 30rpx;
	font-weight: 500;
	color: #ffffff;
}

.tip {
	margin-top: 40rpx;
	font-size: 24rpx;
	color: #ff8c2f;
	text-align: center;
	line-height: 46rpx;
}

.close {
	width: 60rpx;
	height: 60rpx;
	margin-top: 60rpx;
}
</style>
