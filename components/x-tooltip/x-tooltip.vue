<template>
	<view class="x-tooltip">
		<view class="x-tooltip__content" @click.stop="open">
			<slot></slot>
			<view
				class="x-tooltip__content__popper"
				:class="{ 'shake-ani': isShow }"
				:style="[
					tooltipStyle,
					{
						visibility: isShow ? 'visible' : 'hidden'
					}
				]"
			>
				<slot name="content">{{ content }}</slot>
				<view
					class="x-tooltip__content__popper__arrow"
					:style="[arrowStyle]"
					:class="[
						{
							'x-tooltip__content__popper__arrow--bottom': placement.indexOf('bottom') === 0,
							'x-tooltip__content__popper__arrow--top': placement.indexOf('top') === 0,
							'x-tooltip__content__popper__arrow--right': placement.indexOf('right') === 0,
							'x-tooltip__content__popper__arrow--left': placement.indexOf('left') === 0
						}
					]"
				></view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		// 箭头位置
		placement: {
			type: String,
			default: 'top'
		},
		// 提示内容
		content: {
			type: String,
			default: ''
		},
		// 提示窗距离四周间距 [单位px]
		sapce: {
			type: Number,
			default: 0
		},
		// 是否展示
		show: {
			type: Boolean,
			default: false
		}
	},

	data() {
		return {
			isShow: false,
			tooltipStyle: {},
			arrowStyle: {}
		};
	},
	watch: {
		show: {
			handler(newVal, oldVal) {
				this.$nextTick(() => {
					setTimeout(() => {
						if (newVal) {
							this.open();
						} else {
							this.close();
						}
					}, 2000);
				});
			},
			immediate: true,
			deep: true
		}
	},
	onLoad() {},
	mounted() {
		// #ifdef H5 || APP-VUE
		// window.addEventListener('click', () => {
		// 	this.isShow = false
		// })
		// #endif
	},
	methods: {
		// 关闭
		close() {
			this.isShow = false;
		},
		// 打开
		open() {
			uni.createSelectorQuery()
				.in(this)
				.selectAll('.x-tooltip__content,.x-tooltip__content__popper')
				.boundingClientRect(async (data) => {
					let { left, bottom, right, top, width, height } = data[0];
					let obj1 = data[1];

					let tmpTooltipStyle = {};
					let tmpArrowStyle = {};
					switch (this.placement) {
						case 'top':
							tmpTooltipStyle.left = `-${(obj1.width - width) / 2}px`;
							tmpTooltipStyle.bottom = `${height + 8}px`;
							tmpArrowStyle.left = obj1.width / 2 - 6 + 'px';

							break;
						case 'top-start':
							tmpTooltipStyle.left = `0px`;
							tmpTooltipStyle.bottom = `${height + 8}px`;
							break;

						case 'top-end':
							tmpTooltipStyle.right = `0px`;
							tmpTooltipStyle.bottom = `${height + 8}px`;
							tmpArrowStyle.right = `8px`;
							break;

						case 'bottom':
							tmpTooltipStyle.left = `-${(obj1.width - width) / 2}px`;
							tmpTooltipStyle.top = `${height + 2}px`;
							tmpArrowStyle.left = obj1.width / 2 - 6 + 'px';
							break;

						case 'bottom-start':
							tmpTooltipStyle.left = `${this.sapce}px`;
							tmpTooltipStyle.top = `${height + 2}px`;
							tmpArrowStyle.left = '15px';
							break;

						case 'bottom-end':
							tmpTooltipStyle.right = `0px`;
							tmpTooltipStyle.top = `${height + 2}px`;
							tmpArrowStyle.right = `8px`;
							break;

						case 'right':
							tmpTooltipStyle.left = `${width + 8}px`;
							if (obj1.height > height) {
								tmpTooltipStyle.top = `-${(obj1.height - height) / 2}px`;
							} else {
								tmpTooltipStyle.top = `${Math.abs((obj1.height - height) / 2)}px`;
							}

							tmpArrowStyle.top = `${obj1.height / 2 - 6}px`;
							break;
						case 'right-start':
							tmpTooltipStyle.left = `${width + 8}px`;
							tmpTooltipStyle.top = `0px`;
							tmpArrowStyle.top = `8px`;
							break;

						case 'right-end':
							tmpTooltipStyle.left = `${width + 8}px`;
							tmpTooltipStyle.bottom = `0px`;
							tmpArrowStyle.bottom = `8px`;
							break;

						case 'left':
							tmpTooltipStyle.right = `${width + 8}px`;
							if (obj1.height > height) {
								tmpTooltipStyle.top = `-${(obj1.height - height) / 2}px`;
							} else {
								tmpTooltipStyle.top = `${Math.abs((obj1.height - height) / 2)}px`;
							}

							tmpArrowStyle.top = `${obj1.height / 2 - 6}px`;
							break;

						case 'left-start':
							tmpTooltipStyle.right = `${width + 8}px`;
							tmpTooltipStyle.top = `0px`;
							tmpArrowStyle.top = `8px`;
							break;

						case 'left-end':
							tmpTooltipStyle.right = `${width + 8}px`;
							tmpTooltipStyle.bottom = `0px`;
							tmpArrowStyle.bottom = `8px`;
							break;
					}

					this.tooltipStyle = tmpTooltipStyle;
					// 三角形箭头
					this.arrowStyle = tmpArrowStyle;

					this.isShow = true;
				})
				.exec();
		}
	}
};
</script>

<style lang="scss" scoped>
$text-color: var(--text-color);

.x-tooltip {
	position: relative;

	&__content {
		/* float: left; */
		position: relative;
		display: inline-block;
		/* overflow: hidden; */

		/* overflow: hidden; */
		&__popper {
			/* transform-origin: center top; */
			background: #303133;
			visibility: hidden;
			color: #fff;
			position: absolute;
			border-radius: 4px;
			font-size: 12px;
			padding: 10px;
			min-width: 10px;
			word-wrap: break-word;
			display: inline-block;
			white-space: nowrap;
			z-index: 9;

			&__arrow {
				width: 0;
				height: 0;
				z-index: 9;
				position: absolute;

				&--right {
					border-top: 6px solid transparent;
					border-bottom: 6px solid transparent;
					border-right: 6px solid #303133;
					left: -5px;
				}

				&--left {
					border-top: 6px solid transparent;
					border-bottom: 6px solid transparent;
					border-left: 6px solid #303133;
					right: -5px;
				}

				&--top {
					bottom: -5px;
					/* transform-origin: center top; */
					border-left: 6px solid transparent;
					border-right: 6px solid transparent;
					border-top: 6px solid #303133;
				}

				&--bottom {
					border-left: 6px solid transparent;
					border-right: 6px solid transparent;
					border-bottom: 6px solid #303133;
					top: -5px;
				}
			}
		}
	}
}

.shake-ani {
	animation: shakeAni 0.5s ease-in-out;
}

@keyframes shakeAni {
	10%,
	90% {
		transform: translate3d(-1px, 0, 0);
	}

	20%,
	80% {
		transform: translate3d(+2px, 0, 0);
	}

	30%,
	70% {
		transform: translate3d(-4px, 0, 0);
	}

	40%,
	60% {
		transform: translate3d(+4px, 0, 0);
	}

	50% {
		transform: translate3d(-4px, 0, 0);
	}
}
</style>
