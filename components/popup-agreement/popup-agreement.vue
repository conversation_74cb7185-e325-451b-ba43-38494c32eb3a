<template>
	<view class="popup-content">
		<z-paging :fixed="false" :height="pagingHeight" width="690rpx">
			<view slot="top" class="title-view">
				<text class="title">{{ protocolTitle }}</text>
			</view>
			<view class="message">
				<u-parse :content="protocolContent"></u-parse>
			</view>
			<view slot="bottom" class="bottom-view">
				<view :class="[confirmEnable ? 'confirm' : 'confirm-disabled']" @click="onConfirm">{{ confirmText }}</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
export default {
	name: 'popup-agreement',
	props: {
		// 协议类型
		type: {
			type: String,
			default() {
				return '';
			}
		},
		// 是否已同意协议
		isAgree: {
			type: Boolean,
			default() {
				return false;
			}
		}
	},
	// watch: {
	// 	type: {
	// 		handler(newValue, oldValue) {
	// 			this.protocolTitle = this.cons.PROTOCOL_MAPS[newValue] || '';
	// 		},
	// 		immediate: true
	// 	}
	// },
	data() {
		return {
			/* 内容容器高度 */
			pagingHeight: '300px',
			/* 协议标题 */
			protocolTitle: '',
			/* 协议内容 */
			protocolContent: '',

			confirmText: '请阅读完本协议再同意',
			confirmEnable: false,

			timer: null
		};
	},
	mounted() {
		const sys = uni.getSystemInfoSync();
		this.pagingHeight = sys.windowHeight / 1.5 + 'px';
	},
	created() {
		let _that = this;
		_that.$nextTick(() => {
			setTimeout(() => {
				_that.getProcotolData();
			}, 150);
		});
	},
	methods: {
		// 关闭
		oncClose() {
			if (this.timer) {
				clearInterval(this.timer);
			}
			this.$emit('close');
		},
		// 确定
		onConfirm() {
			if (this.confirmEnable) {
				if (this.timer) {
					clearInterval(this.timer);
				}
				this.$emit('confirm');
			}
		},
		// 逻辑处理
		handleData() {
			const that = this;
			if (!that.isAgree) {
				// 开始倒计时 默认3秒
				that.confirmEnable = false;
				var s = 3;
				that.timer = setInterval(() => {
					s--;
					that.confirmText = '请阅读完本协议再同意' + s + 's';
					if (s <= 0) {
						clearInterval(that.timer);
						that.confirmText = '我已阅读并同意';
						that.confirmEnable = true;
					}
				}, 1000);
			} else {
				that.confirmText = '我已阅读并同意';
				that.confirmEnable = true;
			}
		},
		// 获取协议内容
		getProcotolData() {
			uni.$u.http
				.get('/client/app-content/get-by-contentKey', {
					params: { contentKey: this.type }
				})
				.then((res) => {
					this.protocolContent = res?.content || '';
					this.protocolTitle = res?.title || '';
					this.handleData();
				})
				.catch((err) => {});
		}
	}
};
</script>
<style lang="scss" scoped>
.popup-content {
	width: 100% !important;
	padding: 30rpx !important;
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
	background: #ffffff;
	border-top-left-radius: 20rpx;
	border-top-right-radius: 20rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.title-view {
	width: 100%;
	display: flex;
	flex-direction: row;
}

.title {
	color: #000000;
	font-size: 36rpx;
	margin-bottom: 30rpx;
	font-weight: bold;
}

.message {
	margin-top: 30rpx;
	text-align: justify;
	font-size: 30rpx;
	font-weight: 500;
	color: #333333;
	line-height: 55rpx;
}

.bottom-view {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
}

.confirm,
.confirm-disabled {
	width: 100%;
	height: 100rpx;
	line-height: 100rpx;
	background: #444bf1;
	color: #ffffff;
	border-radius: 10rpx;
	font-size: 32rpx;
	font-weight: bold;
	display: flex;
	flex-direction: row;
	justify-content: center;
}

.confirm-disabled {
	background: #c6c6c6;
}
</style>
