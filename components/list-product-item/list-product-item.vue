<template>
	<view class="flex wraper" :class="[`wraper--${styleType}`]" @click.stop="click">
		<image class="wraper__pin" :src="iImage('img_product_pin.png')" v-if="item.isPinned"></image>
		<view class="wraper__left">
			<image class="wraper__left__image" :src="item.picUrl" mode="aspectFill"></image>
		</view>
		<view class="flex wraper__right">
			<text class="wraper__right__title line1">{{ title }}</text>
			<text class="wraper__right__subtitle line1">{{ item.introduction }}</text>
			<rich-text class="wraper__right__price" :nodes="formatPrice"></rich-text>
			<view class="flex wraper__right__button">咨询</view>
		</view>
	</view>
</template>

<script>
import { fen2yuanSimple } from '@/common/tool.js';
export default {
	name: 'list-product-item',
	props: {
		item: {
			type: Object,
			default: () => {}
		},
		// 样式类型控制，支持 flat / card / rounded
		styleType: {
			type: String,
			default: 'rounded',
			validator: (val) => ['flat', 'card', 'rounded'].includes(val)
		}
	},
	computed: {
		// 标题
		title() {
			return [this.item.serviceAreaName, this.item.name].filter((str) => !uni.$u.test.isEmpty(str)).join('·');
		},
		// 格式化价格
		formatPrice() {
			const yuan = this.$options.filters.fen2yuanSimple(this.item.price);
			return this.$options.filters.formattedPrice({
				price: yuan,
				unit: '',
				highlightSize: '18px'
			});
		}
	},
	data() {
		return {};
	},
	methods: {
		click() {
			this.$emit('click', { detail: this.item });
		}
	}
};
</script>

<style lang="scss" scoped>
.wraper {
	padding: 20rpx;
	background: #ffffff;
	position: relative;

	// 样式类型：flat（无圆角无阴影）、card（阴影）、rounded（阴影 + 圆角）
	&--flat {
		// 不加任何装饰
	}
	&--card {
		box-shadow: 0rpx 0rpx 18rpx 0rpx rgba(126, 129, 137, 0.16);
	}
	&--rounded {
		box-shadow: 0rpx 0rpx 18rpx 0rpx rgba(126, 129, 137, 0.16);
		border-radius: 20rpx;
	}

	&__pin {
		width: 74rpx;
		height: 76rpx;
		position: absolute;
		top: 0;
		right: 0;
	}

	&__left {
		width: 200rpx;
		height: 150rpx;

		&__image {
			width: 100%;
			height: 100%;
			background: #f5f5f5;
			border-radius: 15rpx;
		}
	}

	&__right {
		flex: 1;
		height: 160rpx;
		margin-left: 20rpx;
		padding: 5rpx 0;
		flex-direction: column;
		align-items: flex-start;
		position: relative;

		&__title {
			font-weight: bold;
			font-size: 34rpx;
			color: #1e2134;
		}

		&__subtitle {
			font-size: 28rpx;
			color: #5f6069;
			line-height: 50rpx;
		}

		&__price {
			margin-top: auto;
			font-weight: 800;
			font-size: 36rpx;
			color: #f43a55;
		}

		&__button {
			width: 120rpx;
			height: 60rpx;
			background: #444bf1;
			border-radius: 30rpx;
			font-weight: bold;
			font-size: 30rpx;
			color: #ffffff;
			line-height: 56rpx;
			justify-content: center;
			position: absolute;
			right: 0;
			bottom: 5rpx;
		}
	}
}
</style>
