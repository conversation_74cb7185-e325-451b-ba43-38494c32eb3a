<template>
	<view class="col wrapper">
		<!-- #ifdef MP-WEIXIN -->
		<button class="col share-button" open-type="share" @click.stop="onShare('WxShare')">
			<slot />
		</button>
		<!-- #endif -->

		<!-- #ifdef APP-PLUS -->
		<view class="col share-box" @click.stop="onShare('AppShare')">
			<slot />
		</view>
		<!-- #endif -->
	</view>
</template>

<script>
	export default {
		name: "view-share",
		props: {},
		data() {
			return {

			};
		},
		methods: {
			// 分享
			onShare(type) {
				this.$emit('share', {
					type: type
				})
			}
		}
	}
</script>

<style>
	.row {
		display: flex;
		flex-direction: row;
	}

	.col {
		display: flex;
		flex-direction: column;
	}

	.wrapper {
		align-items: center;
		justify-content: center;
	}

	.share-button {
		/* flex: 1; */
		/* width: 100%;
		height: 100%; */
		padding: 0 !important;
		margin: 0 !important;
		background-color: transparent;
		border: none;
		align-items: center;
		justify-content: center;
	}

	.share-box {
		/* flex: 1; */
		/* width: 100%;
		height: 100%; */
		align-items: center;
		justify-content: center;
	}
</style>