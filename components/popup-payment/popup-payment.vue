<template>
	<view class="popup-content">
		<x-loading></x-loading>
		<view class="container">
			<view class="row container__price">
				<text class="container__price__unit">￥</text>
				<text class="container__price__text"> {{payInfo.productPrice}}</text>
			</view>
			<text class="container__descr">{{payInfo.productDesc || ''}}</text>
			<text class="container__title">选择支付方式</text>
			<view v-if="!$u.test.isEmpty(payTypes)" class="col container__channel-wrapper">
				<u-read-more :shadowStyle="{}" showHeight="100rpx" closeText="更多支付方式" color="#757575" fontSize="26rpx"
					textIndent="0">
					<view class="col" v-for="(item, index) in payChannels">
						<u-row :customStyle="{height:'100rpx'}" justify="space-between" @click="onSelectPayType(item)">
							<u-row :customStyle="{position:'relative'}">
								<u--image :src="item.icon" width="66rpx" height="66rpx"></u--image>
								<text class="container__channel-wrapper__channel-title">{{item.title}}</text>
								<text class="container__channel-wrapper__channel-subtitle"
									v-if="!$u.test.isEmpty(item.subtitle)">{{item.subtitle}}
								</text>
								<!-- <image class="container__channel-wrapper__channel-recomend" v-if="item.recommend" src="/static/image/ic_pay_recom.png"
										mode="aspectFit"></image> -->
							</u-row>
							<!-- 勾选或者激活码 -->
							<view v-if="item.type == 'private' && selectPayType == 'private'">
								<u--input :customStyle="{fontWeight:'400'}" v-model="codeText" placeholder="输入激活码"
									fontSize="30rpx" border="none" inputAlign="right" focus></u--input>
							</view>
							<u--image v-else
								:src="item.type == selectPayType ? '/static/image/ic_check_sel.png' : '/static/image/ic_check_nor.png'"
								:showLoading="false" :showError="false" width="36rpx" height="36rpx"></u--image>
						</u-row>
					</view>
				</u-read-more>
			</view>
			<view v-else>
				<u-loadmore marginTop="100rpx" marginBottom="100rpx" loadingText="" status="loading" />
			</view>
			<view class="bottom-view">
				<u-row v-if="!$u.test.isEmpty(protocolType)" :customStyle="{height: '80rpx'}">
					<u-checkbox-group v-model="protocolValues">
						<u-checkbox name="procotol" shape="circle" activeColor="#6C68F0" label="我已阅读并同意"
							labelSize="28rpx" />
					</u-checkbox-group>
					<text style="font-size: 28rpx;color: #6C68F0;"
						@click="onPageJump('/pagesMine/privacy/agreement',{type:protocolType})">{{`《${constant.PROTOCOL_MAPS[protocolType]}》`}}</text>
				</u-row>
				<view class="bottom-view__pay-btn" @click="onPreparePay()">
					<u--text text="确认支付" color="#FFFFFF" size="34rpx" align="center" />
				</view>
			</view>
		</view>
		<view class="close">
			<u-icon name="/static/image/ic_close_w.png" width="60rpx" height="60rpx" @click="onCancel"></u-icon>
		</view>

		<!-- 协议确认 -->
		<u-popup :show="showPrivacyAlertPopup" :zIndex="999999" bgColor="transparent" mode="bottom"
			@close="showPrivacyAlertPopup = false" :closeable="true">
			<popup-privacy-alert :protocolType="protocolType" @confirm="onAgreeProtocol()"></popup-privacy-alert>
		</u-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions,
		mapMutations
	} from 'vuex';
	// #ifdef APP-NVUE
	import GlobalMixin from '@/mixin/mixin.js';
	// #endif
	import {
		setPay
	} from '@/common/utils.js'
	export default {
		name: "popup-payment",
		// #ifdef APP-NVUE
		mixins: [uni.$u.mpMixin, uni.$u.mixin, GlobalMixin],
		// #endif
		props: {
			// 弹窗状态
			show: {
				type: Boolean,
				default: false
			},
			// 支付信息
			payInfo: {
				type: Object,
				default: {}
			},
			// 支付渠道黑名单
			blackList: {
				type: Array,
				default: () => [],
			},
			// 协议类型
			protocolType: {
				type: String,
				default: ''
			},
		},
		watch: {
			// 监听弹窗状态
			show: {
				handler(newVal, oldVal) {
					// #ifdef MP
					if (newVal) {
						this.init()
					}
					// #endif
				},
				immediate: true
			}
		},
		computed: {
			// 支付渠道
			payChannels() {
				let _that = this;
				return _that.payTypes.filter(function(e) {
					return !_that.blackList.includes(e.type);
				})
			}
		},
		data() {
			return {
				// 支付方式
				allPayTypes: [],
				payTypes: [],
				// 已选择的支付方式
				selectPayType: '',

				// 输入的激活码
				codeText: '',

				// 协议判定
				protocolValues: [],

				// 协议确认弹窗
				showPrivacyAlertPopup: false
			};
		},
		created() {
			// 初始化支付渠道
			this.allPayTypes = [{
				type: 'wxpay',
				icon: this.iImage('ic_pay_wx.png'),
				title: '微信支付',
				subtitle: '',
				sort: 1,
				recommend: false
			}, {
				type: 'alipay',
				icon: this.iImage('ic_pay_ali.png'),
				title: '支付宝支付',
				subtitle: '',
				sort: 5,
				recommend: false
			}, {
				type: 'transfer',
				icon: this.iImage('ic_pay_transfer.png'),
				title: '对公转账',
				subtitle: '',
				sort: 3,
				recommend: false
			}, {
				type: 'private',
				icon: this.iImage('ic_pay_code.png'),
				title: '激活码',
				subtitle: '',
				sort: 10,
				recommend: false
			}];

			// #ifdef APP-PLUS
			// IOS中由于支付宝和阿里移动推送的UTDID.framework冲突导致无法打包
			if (plus.os.name == 'iOS') {
				this.allPayTypes = this.allPayTypes.filter(e => e.type !== 'alipay')
			}
			// #endif
			// #ifdef MP
			this.allPayTypes = this.allPayTypes.filter(e => e.type !== 'alipay')
			// #endif
			this.init()
		},
		methods: {
			...mapActions(['submitSignedAgreement']),
			init() {
				const _that = this;
				// 获取支付渠道
				let params = {
					price: this.payInfo.productPrice,
					type: this.payInfo.productType
				}
				uni.$u.http.post('/system/getPayTypeList', params).then(res => {
					let payChannels = []
					for (let i = 0; i < _that.allPayTypes.length; i++) {
						let el = uni.$u.deepClone(_that.allPayTypes[i]);
						if (el.type in res.data) {
							el.sort = res.data[el.type]
							payChannels.push(el)
						}
					}
					_that.payTypes = payChannels
					_that.handlePayWaySort()
				}).catch(err => {})
			},
			// 对支付方式执行排序
			handlePayWaySort() {
				this.payTypes.sort((a, b) => a.sort - b.sort);
				this.selectPayType = this.payTypes[0].type
			},
			// 组装支付参数
			getPayParams() {
				var params = {
					payment_id: 1,
					channel_type: this.selectPayType, // 支付渠道
					cost_id: this.payInfo.productId || '', // 套餐编号
					order_type: this.payInfo.productType || '', // 订单类型
					from_id: this.payInfo.infoId || '' // 数据编号；比如案例推广/置顶 就是案例id
				};
				if (this.payInfo.productType == this.constant.kPay_Type_Works_Top) {
					// 置顶支付额外参数
					params['top_day'] = this.payInfo.topDay
					params['asyn_software'] = this.payInfo.topSyncPlatformIds
					params['city_id'] = this.payInfo.topCiytIds
				}
				return params
			},
			// 选择支付方式
			onSelectPayType(e) {
				this.selectPayType = e.type;
				if (e.type == 'transfer') {
					if (!uni.$u.test.isEmpty(this.protocolType)) {
						if (uni.$u.test.isEmpty(this.protocolValues)) {
							this.showPrivacyAlertPopup = true
							return;
						}
					}
					this.onJumpTransfer()
				}
			},
			// 进入转账支付
			onJumpTransfer() {
				const _that = this;
				let payParams = this.getPayParams()
				uni.navigateTo({
					url: '/pages/personal/transfer' + uni.$u.queryParams(payParams),
					events: {
						transferResult: function(data) {
							_that.$emit('success', payParams);
						},
					}
				})
			},
			// 取消
			onCancel() {
				this.$emit('cancel');
			},
			// 确认阅读协议
			onAgreeProtocol() {
				this.showPrivacyAlertPopup = false
				this.protocolValues = ['procotol']
				this.onPreparePay();
			},
			// 确定
			onPreparePay() {
				let _that = this;
				if (uni.$u.test.isEmpty(this.payInfo)) {
					uni.$u.toast('支付信息异常');
					this.onCancel()
					return;
				}
				if (uni.$u.test.isEmpty(this.selectPayType)) {
					uni.$u.toast('请选择支付方式');
					return;
				}
				if (!uni.$u.test.isEmpty(this.protocolType)) {
					if (uni.$u.test.isEmpty(this.protocolValues)) {
						this.showPrivacyAlertPopup = true
						return;
					}
				}

				// 协议状态提交服务器
				if (!uni.$u.test.isEmpty(this.protocolType)) {
					this.submitSignedAgreement(encodeURIComponent(this.constant.protocolType))
				}

				var params = this.getPayParams()
				console.error('支付参数', params)
				if (this.selectPayType == 'private') {
					// 激活码支付 【当下只针入驻服务商有效】
					if (uni.$u.test.isEmpty(this.codeText)) {
						uni.$u.toast('请输入激活码');
						return;
					}
					uni.$u.http.post('/Payment/createOrderByPrivateKey', {
						privatekey: _that.codeText,
						order_type: params.order_type,
						from_id: params.cost_id,
					}, {
						custom: {
							load: true
						}
					}).then(res => {
						_that.$emit('success', params);
					}).catch(err => {})
				} else if (this.selectPayType == 'transfer') {
					// 转账支付
					this.onJumpTransfer()
				} else {
					// 支付宝 | 微信
					setPay(params, res => {
						if (res.success) {
							// 充值成功
							_that.$emit('success', params);
						}else{
							uni.showToast({
								title:'支付失败',
								icon:'none'
							})
						}
					});
				}
			}
		}
	}
</script>

<style lang="scss" scoped>

	.row {
		display: flex;
		flex-direction: row;
	}

	.col {
		display: flex;
		flex-direction: column;
	}

	.popup-content {
		width: 560rpx !important;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
	}

	.container {
		width: 560rpx;
		padding: 30rpx 30rpx 40rpx 30rpx;
		border-radius: 20rpx;
		background-color: #FFFFFF;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;

		&__price {
			margin-top: 30rpx;
			align-items: center;
			justify-content: center;

			&__unit {
				font-size: 28rpx;
				font-weight: 500;
				color: #F76363;
			}

			&__text {
				font-size: 40rpx;
				font-weight: 800;
				color: #F76363;
			}
		}

		&__descr {
			margin-top: 10rpx;
			font-size: 24rpx;
			color: #999999;
		}

		&__title {
			margin-top: 35rpx;
			font-size: 30rpx;
			color: #666666;
		}

		&__channel-wrapper {
			width: 500rpx;
			margin-top: 15rpx;

			&__channel-title {
				margin-left: 15rpx;
				padding-left: 0;
				font-size: 32rpx;
				font-weight: 500;
				color: #333333;
				justify-content: flex-start;
				align-items: center;
			}

			&__channel-subtitle {
				font-size: 26rpx;
				color: #FF0000;
				justify-content: flex-start;
				align-items: center;
			}

			&__channel-recomend {
				width: 66rpx;
				height: 33rpx;
				position: absolute;
				right: -66rpx;
				top: 0rpx;
			}
		}
	}

	.bottom-view {
		width: 500rpx;
		margin-top: 40rpx;

		&__pay-btn {
			height: 100rpx;
			font-size: 36rpx;
			font-weight: 500;
			border-radius: 50rpx;
			background: #6C68F0;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}


	.close {
		margin-top: 60rpx;
	}
</style>