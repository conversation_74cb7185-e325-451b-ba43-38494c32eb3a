<template>
	<view>
		<u-popup :show="loginPopup.show" :safe-area-inset-bottom="false" mode="bottom" :round="10" @close="closeLogin"
			zIndex="999999">
			<view class="col login-wrap" @tap.stop="onClickDialog">
				<view class="row close" @click="closeLogin">
					<u-icon name="close" size="40rpx"></u-icon>
				</view>
				<!-- 1、验证码登录 -->
				<view class="col form-wrapper" v-if="loginWay == 'PhoneLogin'" @tap.stop>
					<text class="title">欢迎登录~</text>
					<text class="descr">登录后可预约各种服务</text>
					<view class="row form-row">
						<text class="form-title">手机号</text>
						<u-input :customStyle="{ fontWeight: '600' }" fontSize="30rpx" type="number" v-model="phone"
							placeholder="请输入手机号码" border="none"
							:placeholderStyle="{ fontWeight: 'normal', color: '#bbbbbb' }" :maxlength="11"
							:cursorSpacing="60"></u-input>
					</view>
					<view class="row form-row">
						<text class="form-title">验证码</text>
						<u-input :customStyle="{ maxWidth: '350rpx', fontWeight: '600' }" fontSize="30rpx" type="number"
							v-model="vCode" placeholder="请输入验证码" border="none"
							:placeholderStyle="{ fontWeight: 'normal', color: '#bbbbbb' }" :maxlength="6"
							:cursorSpacing="130"></u-input>
						<text class="form-code" :class="{ 'form-code-forhidden': readonly }"
							@click="getSmsCode">{{ codeText }}</text>
					</view>
					<view class="row login" :style="{ background: PrimaryColor }" @click="onPrepareLogin()">
						<text class="login-text">登录</text>
					</view>
					<!-- #ifdef APP-PLUS | H5 -->
					<view class="row other-login" @click="() => (loginWay = 'PasswordLogin')">
						<text class="other-login-text">密码登录</text>
					</view>
					<!-- #endif -->
					<!-- #ifdef MP -->
					<view class="row other-login" @click="() => (loginWay = 'MpPhoneLogin')">
						<text class="other-login-text">快捷登录</text>
					</view>
					<!-- #endif -->
				</view>
				<!-- 2、密码登录 -->
				<view class="col form-wrapper" v-if="loginWay == 'PasswordLogin'" @tap.stop>
					<text class="title">欢迎登录~</text>
					<text class="descr">登录后可预约各种家政服务</text>
					<view class="row form-row">
						<text class="form-title">手机号</text>
						<u-input :customStyle="{ fontWeight: '600' }" fontSize="30rpx" type="number" v-model="phone"
							placeholder="请输入手机号码" border="none"
							:placeholderStyle="{ fontWeight: 'normal', color: '#bbbbbb' }" :maxlength="11"
							:cursorSpacing="60"></u-input>
					</view>
					<view class="row form-row">
						<text class="form-title" decode>密&ensp;&ensp;码</text>
						<u-input :customStyle="{ fontWeight: '600' }" fontSize="30rpx" type="password"
							v-model="password" placeholder="请输入密码" border="none"
							:placeholderStyle="{ fontWeight: 'normal', color: '#bbbbbb' }" :maxlength="8"
							:cursorSpacing="130"></u-input>
					</view>
					<view class="row login" :style="{ background: PrimaryColor }" @click="onPrepareLogin()">
						<text class="login-text">登录</text>
					</view>
					<!-- #ifdef APP-PLUS | H5 -->
					<view class="row other-login" @click="() => (loginWay = 'PhoneLogin')">
						<text class="other-login-text">验证码登录</text>
					</view>
					<!-- #endif -->
					<!-- #ifdef MP -->
					<view class="row other-login" @click="() => (loginWay = 'MpPhoneLogin')">
						<text class="other-login-text">快捷登录</text>
					</view>
					<!-- #endif -->
				</view>
				<!-- 3、小程序手机号登录 -->
				<view class="col form-wrapper" v-else-if="loginWay == 'MpPhoneLogin'">
					<image class="mp-image" src="/static/image/img_mp_login.png" mode="widthFix"></image>
					<!-- #ifdef MP -->
					<view class="row button-view">
						<button v-if="isAgree" class="button" open-type="getPhoneNumber"
							@getphonenumber="decryptPhoneNumber" :style="{ background: PrimaryColor }">
							<text class="button-text">快捷登录</text>
						</button>
						<button v-else class="button" @click="onCheckProtocolState"
							:style="{ background: PrimaryColor }">
							<text class="button-text">快捷登录</text>
						</button>
					</view>
					<!-- #endif -->
					<view class="row other-login" @click="() => (loginWay = 'PhoneLogin')">
						<text class="other-login-text">验证码登录</text>
					</view>
				</view>
				<!-- 协议 -->
				<view class="row protocol-view">
					<u-checkbox-group v-model="protocolValues" activeColor="#6c68f0">
						<u-checkbox name="procotol" shape="circle" label="我已阅读并同意"></u-checkbox>
					</u-checkbox-group>
					<text style="font-size: 14px; color: #6c68f0"
						@click="onJump({ url: '/pagesMine/privacy/agreement', params: { type: `${cons.PROTOCOL_USER}` } })">《用户协议》</text>
					<text style="font-size: 14px; color: #666666">、</text>
					<text style="font-size: 14px; color: #6c68f0"
						@click="onJump({ url: '/pagesMine/privacy/agreement', params: { type: `${cons.PROTOCOL_PRIVACY}` } })">《隐私政策》</text>
				</view>
				<u-safe-bottom></u-safe-bottom>
			</view>
			<!-- 协议确认 -->
			<u-popup :show="showPrivacyAlertPopup" :zIndex="999999" :safe-area-inset-bottom="false"
				bgColor="transparent" mode="bottom" @close="showPrivacyAlertPopup = false" closeable>
				<popup-agreement-validation :next-type="loginWay == 'MpPhoneLogin' ? 'WxLogin' : ''"
					:protocol-type="[cons.PROTOCOL_USER, cons.PROTOCOL_PRIVACY]"
					@confirm="onAgreeProtocol()"></popup-agreement-validation>
			</u-popup>
		</u-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions,
		mapMutations
	} from 'vuex';
	// #ifdef MP
	import {
		getLoginCode,
		getPhoneInfo
	} from './x-login.js';
	// #endif
	import {
		aesEncrypt
	} from '@/common/aesHelper.js';
	import {
		getCurrentRouter
	} from '@/common/login.js';

	var clear;

	export default {
		name: 'x-login',
		data() {
			return {
				PrimaryColor: '#6c68f0',

				// 登录方式 【手机号+验证码登录-PhoneLogin，手机号+密码登录-PasswordLogin，小程序手机号登录-MpPhoneLogin】
				loginWay: 'PhoneLogin',
				// 验证码按钮状态
				readonly: false,
				codeText: '获取验证码',

				// 手机号
				phone: '',
				// 验证码
				vCode: '',
				// 密码
				password: '',

				// 小程序登录Code
				loginCode: '',
				// 当前路由
				currentRouterInPage: '',
				// 协议
				protocolValues: [],

				// 协议确认弹窗
				showPrivacyAlertPopup: false
			};
		},
		watch: {
			// 监听弹窗状态
			'loginPopup.show': {
				handler(newVal, oldVal) {
					if (newVal) {
						// 防止弹出登录弹窗之后 加载 loading还存在的问题
						uni.hideLoading();

						// #ifdef MP
						this.loginWay = 'MpPhoneLogin';
						// #endif
						// #ifdef APP-PLUS
						this.loginWay = 'PasswordLogin';
						// #endif
					}
					// 静默登录
					// #ifdef MP
					if (this.currentRouter == this.currentRouterInPage && newVal) {
						// 微信小程序需要提前获取登录Code
						getLoginCode((loginCode) => {
							console.log('小程序获取code', loginCode);
							this.loginCode = loginCode;
						});
					}
					// #endif
				}
			}
		},
		computed: {
			...mapState(['loginPopup', 'currentRouter']),
			// 协议勾选状态
			isAgree() {
				return this.protocolValues.length > 0;
			}
		},
		created() {
			this.currentRouterInPage = getCurrentRouter();
		},
		methods: {
			...mapMutations(['SET_LOGIN_POPUP', 'SET_USER_INFO', 'SET_TOKEN']),
			...mapActions(['agreementProtocol', 'fetchUser', 'updateUserProfile', 'registerPushDevice']),
			// 关闭登录
			closeLogin() {
				uni.hideKeyboard();
				this.SET_LOGIN_POPUP({
					show: false,
					callback: null
				});
			},
			// 点击弹窗
			onClickDialog() {
				uni.hideKeyboard();
			},
			// 检测协议状态
			onCheckProtocolState() {
				if (!this.isAgree) {
					this.showPrivacyAlertPopup = true;
				}
			},
			//授权手机号登录
			decryptPhoneNumber(e) {
				// #ifdef MP-WEIXIN
				if (e.detail.errMsg == 'getPhoneNumber:ok') {
					if (e.detail.iv) {
						let params = {
							code: this.loginCode,
							iv: encodeURIComponent(e.detail.iv),
							encryptedData: encodeURIComponent(e.detail.encryptedData)
						};
						getPhoneInfo(
							params,
							(res) => {
								this.SET_TOKEN(res);
								// 登录成功
								this.handleLogin();
							},
							(error) => {
								uni.hideLoading();
								setTimeout(() => {
									uni.showToast({
										title: '登录失败',
										icon: 'none'
									});
								});
								this.closeLogin();
							}
						);
					} else {
						// 授权失败请使用验证码登录
						this.loginWay == 'PhoneLogin';
						uni.showToast({
							title: '请使用手机号登录',
							icon: 'none'
						});
					}
				} else {
					// this.closeLogin()
				}
				// #endif
			},
			//验证码按钮文字状态
			getCodeState() {
				const _this = this;
				this.readonly = true;
				this.codeText = '60S后重新获取';
				var s = 60;
				clear = setInterval(() => {
					s--;
					_this.codeText = s + 'S后重新获取';
					if (s <= 0) {
						clearInterval(clear);
						_this.codeText = '获取验证码';
						_this.readonly = false;
					}
				}, 1000);
			},
			//获取验证码
			getSmsCode() {
				if (this.readonly) {
					uni.showToast({
						title: '验证码已发送~',
						icon: 'none'
					});
					return;
				}
				if (this.phone == '') {
					uni.showToast({
						title: '请输入手机号~',
						icon: 'none'
					});
					return;
				}
				const phoneRegular = /^1\d{10}$/;
				if (!phoneRegular.test(this.phone)) {
					uni.showToast({
						title: '手机号格式不正确~',
						icon: 'none'
					});
					return;
				}
				// 获取验证码接口
				const _that = this;
				let httpParams = {
					mobile: this.phone,
					scene: '1'
				};
				uni.showLoading({
					title: '发送中.'
				});
				uni.$u.http
					.post('/member/auth/send-sms-code', httpParams)
					.then((res) => {
						uni.hideLoading();
						_that.getCodeState();
						uni.$u.toast('验证码已发送成功,请注意查收');
					})
					.catch((err) => {
						uni.hideLoading();
					});
			},
			// 确认阅读协议
			onAgreeProtocol(e) {
				this.showPrivacyAlertPopup = false;
				this.protocolValues = ['procotol'];
				if (this.loginWay == 'MpPhoneLogin') {
					// 小程序获取手机号需要主动点击
					this.decryptPhoneNumber(e.detail);
				} else {
					this.onPrepareLogin();
				}
			},
			// 准备登录
			onPrepareLogin() {
				if (this.phone == '') {
					uni.showToast({
						title: '请输入手机号~',
						icon: 'none'
					});
					return;
				}
				const phoneRegular = /^1\d{10}$/;
				if (!phoneRegular.test(this.phone)) {
					uni.showToast({
						title: '手机号格式不正确~',
						icon: 'none'
					});
					return;
				}

				let httpUrl = '';
				let httpParams = {};
				if (this.loginWay == 'PhoneLogin') {
					if (this.vCode == '') {
						uni.showToast({
							title: '请输入验证码~',
							icon: 'none'
						});
						return;
					}
					httpParams.mobile = this.phone;
					httpParams.code = this.vCode;
					httpUrl = '/member/auth/sms-login';
				} else if (this.loginWay == 'PasswordLogin') {
					if (this.password == '') {
						uni.showToast({
							title: '请输入密码~',
							icon: 'none'
						});
						return;
					}
					httpParams.account = this.phone;
					httpParams.password = encodeURIComponent(this.password);
					httpUrl = '/member/auth/login';
				}

				if (uni.$u.test.isEmpty(this.protocolValues)) {
					this.showPrivacyAlertPopup = true;
					return;
				}

				// #ifdef MP
				httpParams.login_code = this.loginCode;
				this.onLogin(httpUrl, httpParams);
				// #endif

				// #ifdef APP-PLUS
				this.onLogin(httpUrl, httpParams);
				// #endif
			},
			// 手机号登录
			onLogin(url, httpParams) {
				uni.showLoading({
					title: '登录中.'
				});
				uni.$u.http
					.post(url, httpParams)
					.then((res) => {
						console.error("登录结果",res)
						this.SET_TOKEN(res);
						this.handleLogin();
					})
					.catch((err) => {
						uni.hideLoading();
						if (!uni.$u.test.isEmpty(err.msg)) {
							uni.showToast({
								title: err.msg,
								icon: 'none'
							});
						}
					});
			},
			handleLogin() {
				// 更新用户配置信息
				this.updateUserProfile();
				// #ifdef APP-PLUS
				// 注册设备编号
				this.registerPushDevice();
				// #endif
				// 协议状态提交服务器
				this.agreementProtocol(encodeURIComponent([this.cons.PROTOCOL_USER, this.cons.PROTOCOL_PRIVACY].join(
				',')));
				// 查询用户信息
				this.fetchUser()
					.then((res) => {
						uni.hideLoading();
						setTimeout(() => {
							uni.showToast({
								title: '登录成功~',
								icon: 'none'
							});
						}, 200);
						this.loginPopup.callback && this.loginPopup.callback();
						this.closeLogin();
					})
					.catch((err) => {
						uni.hideLoading();
						this.closeLogin();
					});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.row {
		display: flex;
		flex-direction: row;
	}

	.col {
		display: flex;
		flex-direction: column;
	}

	.login-wrap {
		width: 750rpx;
		padding: 48rpx 32rpx;
		background-color: #ffffff;
		border-radius: 18rpx 18rpx 0 0;
		z-index: 99;
		position: relative;
	}

	.title {
		font-size: 40rpx;
		font-weight: bold;
		margin-top: 24rpx;
	}

	.descr {
		font-size: 24rpx;
		color: #666;
		margin-top: 16rpx;
	}

	.close {
		width: 100rpx;
		height: 100rpx;
		align-items: center;
		justify-content: center;
		position: absolute;
		right: 0;
		top: 0;
	}

	/* ----- */
	.form-wrapper {
		flex: 1;
		margin-top: 30rpx;
	}

	.form-row {
		// flex: 1;
		height: 100rpx;
		// padding: 30rpx 0;
		margin-top: 24rpx;
		position: relative;
		border-bottom: 1rpx solid #e8e8e8;
		line-height: 1;
		align-items: center;
	}

	.form-title {
		margin-right: 20rpx;
		font-size: 30rpx;
		font-weight: 700;
		color: #333333;
		white-space: nowrap;
	}

	.form-code {
		z-index: 11;
		min-width: 188rpx;
		height: 100rpx;
		line-height: 100rpx;
		color: #6c68f0;
		font-size: 26rpx;
		text-align: center;
		position: absolute;
		right: 0;
	}

	.form-code-forhidden {
		color: #cccccc;
	}

	.login {
		/* #ifdef APP-NVUE */
		flex: 1;
		/* #endif */
		height: 100rpx;
		margin-top: 60rpx;
		background-color: #6c68f0;
		border-radius: 20rpx;
		align-items: center;
		justify-content: center;
	}

	.login-text {
		font-size: 32rpx;
		font-weight: 600;
		color: #ffffff;
	}

	.other-login {
		/* #ifdef APP-NVUE */
		flex: 1;
		/* #endif */
		height: 100rpx;
		margin-top: 40rpx;
		background: transparent;
		align-items: center;
		justify-content: space-around;
	}

	.other-login-text {
		font-size: 32rpx;
		color: #666666;
	}

	/* 小程序登录 */
	.mp-image {
		width: 570rpx;
		margin: 0 auto;
	}

	.button-view {
		margin-top: 80rpx;
	}

	.button {
		flex: 1;
		height: 100rpx;
		border-radius: 20rpx;
	}

	.button-text {
		line-height: 100rpx;
		font-size: 32rpx;
		font-weight: 600;
		color: #ffffff;
	}

	/* 协议 */
	.protocol-view {
		flex: 1;
		margin-top: 60rpx;
		justify-content: center;
		align-items: center;
	}
</style>