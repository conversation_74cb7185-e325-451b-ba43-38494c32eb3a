<template>
	<view class="flex wrapper animate__animated animate__slideInDown" v-if="show && !isFollow">
		<text class="wrapper__title">通过微信实时关注预约订单信息状态</text>
		<view class="flex wrapper__button" @click="onFollow">立即关注</view>
		<view class="flex wrapper__close" @click.stop="onClose">
			<u-icon name="close"></u-icon>
		</view>
	</view>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { openMiniWx, iWxMiniProgram } from '@/common/utils.js';
export default {
	name: 'x-official-account',
	data() {
		return {
			show: false
		};
	},
	computed: {
		...mapState(['userInfo']),
		...mapGetters(['getterIsLogin']),
		// 是否已关注公众号的状态
		isFollow() {
			return this.getterIsLogin && this.userInfo.wechat_bind === 1;
		}
	},
	mounted() {
		this.show = !this.isFollow;
	},
	methods: {
		onFollow() {
			openMiniWx(iWxMiniProgram('oa', this.$conf.miniProgramId));
		},
		onClose() {
			this.show = false;
		}
	}
};
</script>

<style lang="scss" scoped>
.wrapper {
	width: 100%;
	height: 88rpx;
	padding: 0 30rpx;
	background: #ffe8ec;

	&__title {
		font-size: 28rpx;
		font-weight: 500;
		color: #fc143a;
	}

	&__button {
		width: 130rpx;
		height: 46rpx;
		margin-left: auto;
		border: 0.8px solid #fc143a;
		border-radius: 24rpx;
		font-size: 24rpx;
		color: #fc143a;
		justify-content: center;
	}

	&__close {
		width: 50rpx;
		height: 88rpx;
		justify-content: center;
	}
}
</style>
