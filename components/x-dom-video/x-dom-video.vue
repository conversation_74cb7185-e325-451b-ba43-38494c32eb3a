<!-- eslint-disable -->
<template>
	<view class="wrap" @click="clickVideo">
		<!-- #ifdef APP-PLUS -->
		<view
			v-html="videoHtml"
			id="dom-video"
			class="dom-video"
			:style="{
				height: $u.addUnit(height)
			}"
			:eventDrive="eventDrive"
			:change:eventDrive="domVideo.eventHandle"
			:videoSrc="videoSrc"
			:change:videoSrc="domVideo.srcChange"
			:videoProps="videoProps"
			:change:videoProps="domVideo.propsChange"
			:randomNum="randomNum"
			:change:randomNum="domVideo.randomNumChange"
		/>
		<!-- #endif -->
		<!-- #ifdef MP -->
		<video
			class="mp-video"
			id="mp-video"
			:style="{
				height: $u.addUnit(height)
			}"
			:autoplay="videoProps.autoplay"
			:enable-progress-gesture="false"
			:loop="loop"
			:muted="muted"
			:src="videoSrc"
			:poster="poster"
			:object-fit="objectFit"
			:show-center-play-btn="false"
			:controls="controls"
			@play="videoEvent('play')"
			@pause="videoEvent('pause')"
			@ended="videoEvent('ended')"
			@error="videoEvent('error')"
			@loadstart="videoEvent('loadstart')"
			@loadedmetadata="videoEvent('loaded')"
		/>
		<!-- #endif -->
		<image class="poster animate__animated" :class="[showpPoster ? 'animate__fadeIn' : 'animate__fadeOut']" :src="poster" mode="objectFit" v-if="showpPoster"></image>

		<view class="error" v-if="videoState == 'error'">
			<text style="color: #ffffff">加载失败</text>
		</view>
		<view class="status" v-else>
			<view v-if="showLoading">
				<u-loading-icon mode="circle" size="80rpx" color="#FFFFFF" inactiveColor="#444BF1" vertical></u-loading-icon>
			</view>
			<view v-if="videoState == 'pause' || videoState == 'ended'">
				<u-icon name="play-right-fill" color="#FFFFFF" size="80rpx"></u-icon>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'x-dom-video',
	props: {
		src: {
			type: String,
			default: ''
		},
		autoplay: {
			type: Boolean,
			default: false
		},
		loop: {
			type: Boolean,
			default: false
		},
		controls: {
			type: Boolean,
			default: false
		},
		objectFit: {
			type: String,
			default: 'contain'
		},
		muted: {
			type: Boolean,
			default: false
		},
		poster: {
			type: String,
			default: ''
		},
		// 组件高度
		height: {
			type: [String, Number],
			default: '100%'
		}
	},

	// 数据状态
	data() {
		return {
			videoHtml: '',
			videoSrc: '',
			eventDrive: null,
			videoProps: {},
			randomNum: Math.floor(Math.random() * 100000000 + 1),
			videoState: '',
			showLoading: true,
			showpPoster: true
		};
	},
	watch: {
		// 监听视频资源文件更新
		src: {
			handler(val) {
				if (!val) return;
				// #ifdef APP-PLUS
				this.initVideoHtml();
				// #endif

				setTimeout(() => {
					this.videoSrc = val;
				}, 0);
			},
			immediate: true
		},
		// 监听首次加载
		autoplay: {
			handler(val) {
				this.videoProps.autoplay = val;
			},
			immediate: true
		}
	},
	// 生命周期
	mounted() {
		// #ifdef APP-PLUS
		this.initVideoHtml();
		// #endif
	},
	// 方法
	methods: {
		// 将video的事件传递给父组件
		videoEvent(data) {
			// console.warn('视频状态发生变化', data);
			if (data == 'loadstart') {
				this.showLoading = true;
			} else if (data == 'loaded') {
				this.showLoading = false;
				this.showpPoster = false;
			} else {
				if (data == 'error') {
					this.showLoading = false;
				}
				this.videoState = data;
			}
			this.$emit(data);
		},
		// 初始化视频
		initVideoHtml() {
			this.videoHtml = `<video
          src="${this.src}"
          id="dom-html-video_${this.randomNum}"
          class="dom-html-video"
          ${this.autoplay ? 'autoplay' : ''}
          ${this.loop ? 'loop' : ''}
          ${this.controls ? 'controls' : ''}
          ${this.muted ? 'muted' : ''}
          ${this.poster ? 'poster="' + this.poster + '"' : ''}
          preload="auto"
          playsinline
          webkit-playsinline
          width="100%"
          height="100%"
          style="object-fit: ${this.objectFit};padding:0;"
        >
          <source src="${this.src}" type="video/mp4">
          <source src="${this.src}" type="video/ogg">
          <source src="${this.src}" type="video/webm">
        </video>
      `;
			// console.log('视频html =>', this.videoHtml)
		},
		resetEventDrive() {
			this.eventDrive = null;
		},
		// 将service层的事件/数据 => 传递给renderjs层
		play() {
			// #ifdef APP-PLUS
			this.eventDrive = 'play';
			// #endif

			// #ifdef MP
			const video = uni.createVideoContext('mp-video', this);
			video.play();
			// #endif
		},
		pause() {
			// #ifdef APP-PLUS
			this.eventDrive = 'pause';
			// #endif

			// #ifdef MP
			const video = uni.createVideoContext('mp-video', this);
			video.pause();
			// #endif
		},
		stop() {
			// #ifdef APP-PLUS
			this.eventDrive = 'stop';
			// #endif

			// #ifdef MP
			const video = uni.createVideoContext('mp-video', this);
			video.stop();
			// #endif
		},
		clickVideo() {
			if (this.showLoading) {
				return;
			}
			console.warn('点击了视频', this.videoState);
			if (this.videoState == 'play') {
				this.pause();
			} else if (this.videoState == 'pause') {
				this.play();
			} else if (this.videoState == 'stop' || this.videoState == 'ended' || this.videoState == 'error') {
				this.play();
			}
		}
	}
};
</script>

<script module="domVideo" lang="renderjs">
export default {
  data() {
    return {
      video: null,
      num: '',
      options: {}
    }
  },
  mounted() {
    this.initVideoEvent()
  },
  methods: {
    initVideoEvent() {
      setTimeout(() => {
        let video = document.getElementById(`dom-html-video_${this.num}`)
        this.video = video

        // 监听视频事件
        video.addEventListener('play', () => {
          this.$ownerInstance.callMethod('videoEvent', 'play')
        })
        video.addEventListener('pause', () => {
          this.$ownerInstance.callMethod('videoEvent', 'pause')
        })
        video.addEventListener('ended', () => {
          this.$ownerInstance.callMethod('videoEvent', 'ended')
          this.$ownerInstance.callMethod('resetEventDrive')
        })
		video.addEventListener('error', () => {
		  this.$ownerInstance.callMethod('videoEvent', 'error')
		})
		video.addEventListener('loadstart', () => {
			 this.$ownerInstance.callMethod('videoEvent', 'loadstart')
		});
		video.addEventListener('canplaythrough', () => {
			 this.$ownerInstance.callMethod('videoEvent', 'loaded')
		});
      }, 100)
    },
    eventHandle(eventType) {
      if (eventType) {
        this.video = document.getElementById(`dom-html-video_${this.num}`)
        if (eventType === 'play') {
          this.video.play()
        } else if (eventType === 'pause') {
          this.video.pause()
        } else if (eventType === 'stop') {
          this.video.stop()
        }
      }
    },
    srcChange(val) {
      // 实现视频的第一帧作为封面，避免视频展示黑屏
      this.initVideoEvent()
      setTimeout(() => {
        let video = document.getElementById(`dom-html-video_${this.num}`)

        video.addEventListener('loadedmetadata', () => {
          let { autoplay } = this.options
          video.play()
          if (!autoplay) {
            video.pause()
          }
        })
      }, 0)
    },
    propsChange(obj) {
      this.options = obj
    },
    randomNumChange(val) {
      this.num = val
    },
  }
}
</script>

<style lang="scss" scoped>
.wrap {
	width: 100%;
	height: 100%;
	position: relative;
	background-color: #000000;
}
.dom-video {
	overflow: hidden;
	height: 100%;
	padding: 0;
	&-height {
		height: 100%;
	}
}

.mp-video {
	width: 100%;
	height: 100%;
	padding: 0;
}

.poster {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
}

.error,
.status {
	// width: 80rpx;
	// height: 80rpx;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
</style>
