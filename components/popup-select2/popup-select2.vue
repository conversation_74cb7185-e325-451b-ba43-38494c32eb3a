<template>
	<view class="col popup-content">
		<view class="col container">
			<view class="row title-view">
				<text class="title-view__title">{{ title }}</text>
			</view>
			<scroll-view scroll-y class="list-scroll">
				<view :id="`list-cell${index}`" class="row list-scroll__cell" :class="{ 'list-scroll__active-cell': selectIndexs.includes(index) }" v-for="(item, index) in list" :key="index" @click="onClickItem(index)">
					<text class="list-scroll__cell__text" :class="{ 'list-scroll__active-cell__text': selectIndexs.includes(index) }">{{ item[keyName] }}</text>
					<u-icon :name="selectIndexs.includes(index) ? 'checkmark-circle-fill' : 'checkmark-circle'" :color="selectIndexs.includes(index) ? '#444BF1' : '#CCCCCC'" size="36rpx"></u-icon>
				</view>
			</scroll-view>
			<view class="col bottom-wrapper">
				<view class="row bottom-wrapper__confirm" :class="[selectIndexs.length == 0 ? 'disable' : '']" @click="onConfirm">提交</view>
			</view>
			<u-safe-bottom></u-safe-bottom>
		</view>
	</view>
</template>

<script>
export default {
	name: 'popup-select2',
	props: {
		show: {
			type: Boolean,
			default: false
		},
		// 弹窗标识
		ident: {
			type: String,
			default: 'select'
		},
		// 标题
		title: {
			type: String,
			default: '请选择'
		},
		// 数据
		list: {
			type: Array,
			default: () => {}
		},
		// 默认读取的键名
		keyName: {
			type: String,
			default: 'name'
		},
		// 最大选择的数量(默认1为单选，大于1为多选)
		maxNum: {
			type: Number,
			default: 1
		},
		// 确认事件
		confirm: {
			type: Function,
			default: null
		}
	},
	data() {
		return {
			// 选中的索引
			selectIndexs: []
		};
	},
	methods: {
		// 点击条目Item
		onClickItem(i) {
			if (this.maxNum == 1) {
				// 单选
				this.selectIndexs = [i];
			} else {
				// 多选
				if (this.selectIndexs.includes(i)) {
					this.selectIndexs = this.selectIndexs.filter((index) => index !== i);
				} else {
					if (this.selectIndexs.length >= this.maxNum) {
						uni.showToast({
							title: `最多只能选择${this.maxNum}个`,
							icon: 'none'
						});
						return;
					}
					this.selectIndexs.push(i);
				}
			}
		},
		// 确认
		onConfirm() {
			const res = {
				ident: this.ident,
				indexs: this.selectIndexs,
				result: this.selectIndexs.map((index) => this.list[index])
			};
			this.confirm && this.confirm(res);
			this.$emit('confirm', res);
			this.onCancel();
		},
		// 取消
		onCancel() {
			this.$emit('close', {
				ident: this.ident
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.row {
	display: flex;
	flex-direction: row;
}

.col {
	display: flex;
	flex-direction: column;
}

.container {
	width: 750rpx;
	padding-bottom: 40rpx;
	background: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
}

.title-view {
	width: 100%;
	height: 120rpx;
	border-bottom: 5px solid #f8f8f8;
	justify-content: center;
	align-items: center;

	&__title {
		font-size: 36rpx;
		font-weight: 500;
		color: #333333;
	}
}

.list-scroll {
	width: 100%;
	// height: 400rpx;
	max-height: 800rpx;
	overflow: auto;
	justify-content: center;
	align-items: center;

	&__cell {
		width: 100%;
		height: 100rpx;
		padding: 0 30rpx;
		justify-content: space-between;
		align-items: center;

		&__text {
			font-size: 30rpx;
			font-weight: 500;
			color: #333333;
		}

		&__check {
			width: 36rpx;
			height: 36rpx;
		}
	}

	&__active-cell {
		&__text {
			color: #444BF1;
		}
	}
}

.bottom-wrapper {
	padding: 30rpx 30rpx 30rpx 30rpx;
	background: #ffffff;

	&__confirm {
		height: 100rpx;
		justify-content: center;
		align-items: center;
		font-size: 34rpx;
		font-weight: 600;
		color: #ffffff;
		border-radius: 20rpx;
		background: #444BF1;
	}
}
</style>
