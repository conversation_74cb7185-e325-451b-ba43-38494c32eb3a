<template>
	<view>
		<view class="flex-col container">
			<view class="flex-col wrap-app">
				<image class="wrap-app__bg" src="/static/image/img_popup_sfjd_app_bg.png" mode="aspectFill"></image>
				<view class="flex-row wrap-app__btn" @click="onAppClick">
					<image class="wrap-app__btn__img" src="/static/image/img_popup_sfjd_app_open.png" mode="aspectFill"></image>
				</view>
			</view>
			<image class="close" src="/static/image/ic_close_w.png" mode="aspectFill" @click="onClose"></image>
		</view>
	</view>
</template>

<script>
import { openMiniWx, openOtherApp } from '@/common/utils';
import { mapState } from 'vuex';
export default {
	name: 'popup-app-download',
	props: {},
	data() {
		return {};
	},
	computed: {
		...mapState(['sysConf']),
		appData() {
			// #ifdef APP
			return {
				name: this.sysConf?.relatedApp?.app_name || '',
				icon: this.sysConf?.relatedApp?.app_icon || '',
				package: this.sysConf?.relatedApp?.package || '',
				appUrl: this.sysConf?.relatedApp?.download || '',
				show: this.sysConf?.relatedApp?.is_disabled == 0
			};
			// #endif

			// #ifdef MP
			return {
				name: this.sysConf?.relatedMiniProgram?.app_name || '',
				icon: this.sysConf?.relatedMiniProgram?.app_icon || '',
				miniId: this.sysConf?.relatedMiniProgram?.program_id || '',
				show: this.sysConf?.relatedMiniProgram?.is_disabled == 0
			};
			// #endif
			return {};
		}
	},
	methods: {
		onClose() {
			this.$emit('close'), {};
		},
		onAppClick() {
			// #ifdef APP
			openOtherApp({
				package: this.appData?.package,
				appUrl: this.appData?.download
			});
			// #endif

			// #ifdef MP
			openMiniWx({ miniId: this.appData?.miniId, path: '/pages/home/<USER>' });
			// #endif
			
			this.onClose()
		}
	}
};
</script>

<style lang="scss" scoped>
.container {
	align-items: center;
}

.wrap-app {
	width: 560rpx;
	height: 740rpx;
	overflow: hidden;
	position: relative;
	&__bg {
		width: 560rpx;
		height: 740rpx;
	}
	&__btn {
		width: 420rpx;
		height: 90rpx;
		position: absolute;
		bottom: 40rpx;
		left: 50%;
		transform: translateX(-50%);
		&__img {
			width: 420rpx;
			height: 90rpx;
		}
	}
}

.close {
	width: 50rpx;
	height: 50rpx;
	margin-top: 60rpx;
}
</style>
