<template>
	<view class="flex wraper" @click="click">
		<text class="wraper__time">{{ $u.timeFormat(item.create_time, 'yyyy年mm月dd日 hh:MM') }}</text>
		<view class="wraper__container">
			<view class="flex wraper__container__box1">
				<view class="flex wraper__container__box1__service-time">
					<image class="wraper__container__box1__service-time__ic" src="/static/image/ic_order_service_time.png"></image>
					{{ item.user_datetime_text }}
				</view>
				<text class="wraper__container__box1__service-status">{{ orderState.name }}</text>
			</view>
			<view class="flex wraper__container__box2">
				<image class="wraper__container__box2__cover" :src="item.category && item.category.cover_image_text" mode="aspectFill"></image>
				<view class="flex wraper__container__box2__right">
					<text class="wraper__container__box2__right__name line1">{{ item.category && item.category.nickname }}</text>
					<view class="flex wraper__container__box2__right__address">
						<image class="wraper__container__box2__right__address__ic" src="/static/image/ic_order_address.png"></image>
						<text class="wraper__container__box2__right__address__txt line1">{{ item.user_addr_text }}</text>
					</view>
					<rich-text class="wraper__container__box2__right__price line1" :nodes="formattedPrice({ price: item.category && item.category.price, unit: item.category && item.category.unit, highlightSize: '16px' })"></rich-text>
				</view>
			</view>
			<view class="flex wraper__container__box3">
				<rich-text class="wraper__container__box3__fee" :nodes="`共${item.number}项服务，合计：<span style='color:#F95986;font-weight:500;'>￥${item.number * item.price}</span>`"></rich-text>
			</view>
			<u-line color="#EBEBEB" :customStyle="{ width: '630rpx' }" margin="0 30rpx"></u-line>
			<view class="flex wraper__container__box4">
				<!-- <view class="flex wraper__container__box4__feedback" @click.stop="childClick({ type: 'feedback' })" v-if="[cons.ORDER_STATUS_SERVING, cons.ORDER_STATUS_FINISHED].includes(item.status)">建议反馈</view> -->
				<view class="flex wraper__container__box4__feedback" @click.stop="childClick({ type: 'feedback' })" v-if="item.is_show_feedback">建议反馈</view>
				<view class="flex wraper__container__box4__actions">
					<view v-for="(btn, index) in buttons" :key="index" :class="['flex wraper__container__box4__actions__btn', btn.class]" @click.stop="childClick(btn)" v-if="btn.condition">
						{{ btn.label }}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'list-order-item',
	props: {
		// 订单状态
		type: {
			type: String,
			default: ''
		},
		item: {
			type: Object,
			default: () => {}
		}
	},
	computed: {
		/**
		 * 根据 item.status 映射出相应的状态描述
		 * [status]常量定义见：`@/common/constant.js` 中的 `ORDER_STATUS_*`
		 * @returns {Object} 返回包含 name 和 desc 的对象
		 */
		orderState() {
			const isComment = this.item ? this.item.is_comment : false;
			const statusMapping = {
				[this.cons.ORDER_STATUS_CANCELLED]: { name: '已取消', desc: '已取消订单' },
				[this.cons.ORDER_STATUS_PENDING]: { name: '待接单', desc: '您已下单，请等待师傅接单' },
				[this.cons.ORDER_STATUS_VISIT]: { name: '待上门', desc: '师傅已接单，请等待师傅上门' },
				[this.cons.ORDER_STATUS_SERVING]: { name: '服务中', desc: '师傅正在服务中' },
				[this.cons.ORDER_STATUS_FINISHED]: isComment ? { name: '已完成', desc: '' } : { name: '待评价', desc: '快给师傅评价了' },
				default: { name: '-', desc: '-' }
			};
			return statusMapping[this.item?.status] || statusMapping.default;
		},
		buttons() {
			const status = this.item?.status;
			return [
				{
					type: 'cancel',
					label: '取消订单',
					class: 'wraper__container__box4__actions__btn--cancel',
					condition: status == this.cons.ORDER_STATUS_PENDING || status == this.cons.ORDER_STATUS_VISIT
				},
				{
					type: 'reminder-order',
					label: '催接单',
					class: 'wraper__container__box4__actions__btn--reminder-order',
					condition: status == this.cons.ORDER_STATUS_PENDING
				},
				{
					type: 'contact',
					label: '联系师傅',
					class: 'wraper__container__box4__actions__btn--contact',
					condition: status == this.cons.ORDER_STATUS_VISIT || status == this.cons.ORDER_STATUS_SERVING
				},
				{
					type: 'reminder-visit',
					label: '催上门',
					class: 'wraper__container__box4__actions__btn--reminder-visit',
					condition: status == this.cons.ORDER_STATUS_VISIT
				},
				{
					type: 'again',
					label: '再次下单',
					class: 'wraper__container__box4__actions__btn--again',
					condition: status == this.cons.ORDER_STATUS_FINISHED || status == this.cons.ORDER_STATUS_CANCELLED
				},
				{
					type: 'comment',
					label: '评价',
					class: 'wraper__container__box4__actions__btn--comment',
					condition: status == this.cons.ORDER_STATUS_FINISHED && this.item.is_comment == 0
				},
				{
					type: 'republish',
					label: '重新发布',
					class: 'wraper__container__box4__actions__btn--republish',
					condition: false
				}
			].filter((e) => e.condition);
		}
	},
	data() {
		return {};
	},
	methods: {
		click() {
			this.$emit('click', { detail: this.item });
		},
		childClick(e) {
			this.$emit('childClick', { operationType: e.type, detail: this.item });
		}
	}
};
</script>

<style lang="scss" scoped>
.wraper {
	flex: 1;
	// width: 100%;
	flex-direction: column;
	justify-content: center;

	&__time {
		padding: 30rpx 0 20rpx 0;
		font-weight: 500;
		font-size: 24rpx;
		color: #666666;
	}

	&__container {
		flex: 1;
		background: #ffffff;
		border-radius: 20rpx;

		&__box1 {
			padding: 30rpx;

			&__service-time {
				font-weight: bold;
				font-size: 28rpx;
				color: #333333;
				&__ic {
					width: 30rpx;
					height: 30rpx;
					margin-right: 10rpx;
				}
			}

			&__service-status {
				margin-left: auto;
				font-weight: bold;
				font-size: 28rpx;
				color: #444BF1;
			}
		}

		&__box2 {
			height: 160rpx;
			padding: 0 30rpx;

			&__cover {
				width: 160rpx;
				min-width: 160rpx;
				height: 160rpx;
				border-radius: 15rpx;
				background: #f7f7f7;
			}

			&__right {
				height: 100%;
				margin-left: 15rpx;
				flex-direction: column;
				align-items: flex-start;

				&__name {
					font-weight: bold;
					font-size: 30rpx;
					color: #333333;
				}

				&__address {
					margin-top: 20rpx;

					&__ic {
						width: 22rpx;
						min-width: 22rpx;
						height: 26rpx;
						margin-right: 8rpx;
					}

					&__txt {
						font-size: 24rpx;
						color: #999999;
					}
				}

				&__price {
					margin-top: auto;
					font-weight: 800;
					font-size: 28rpx;
					color: #333333;
				}
			}
		}

		&__box3 {
			padding: 18rpx 30rpx;
			justify-content: flex-end;

			&__fee {
				font-size: 24rpx;
				color: #999999;
			}
		}

		&__box4 {
			height: 100rpx;
			padding: 0 30rpx;

			&__feedback {
				font-size: 24rpx;
				color: #999999;
			}

			&__actions {
				margin-left: auto;
				&__btn {
					width: 160rpx;
					height: 60rpx;
					margin-right: 15rpx;
					border-radius: 30rpx;
					justify-content: center;
					font-weight: bold;
					font-size: 24rpx;

					&:last-child {
						margin-right: 0;
					}

					&--cancel {
						background: #ffffff;
						border: 0.8px solid #444BF1;
						color: #444BF1;
					}

					&--reminder-visit,
					&--reminder-order,
					&--comment {
						background: #444BF1;
						color: #ffffff;
					}

					&--cancel,
					&--contact,
					&--again,
					&--republish {
						background: #ffffff;
						border: 0.8px solid #444BF1;
						color: #444BF1;
					}
				}
			}
		}
	}
}
</style>
