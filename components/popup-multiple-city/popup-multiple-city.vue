<template>
	<view class="col popup-multiple-city">
		<view class="flex-col wrap">
			<view class="flex header">
				<rich-text :nodes="title"></rich-text>
			</view>
			<!-- 一级 -->
			<view v-if="level == 1" class="flex list">
				<scroll-view scroll-y class="flex-col normal-list" :scroll-into-view="scrollFirstView">
					<view :id="`p-cell${item.id}`" class="flex cell" :class="{ active: isSelected(item.id, 1) || firstSelectedIndex == index }" v-for="(item, index) in firstList" :key="index" @click="onItemClick(index, item, 1)">
						<text>{{ item.name }}</text>
						<image class="check" :src="isSelected(item.id, 1) ? '/static/image/ic_checkbox_sel.png' : '/static/image/ic_checkbox_nor.png'" mode="aspectFit"></image>
					</view>
				</scroll-view>
			</view>
			<!-- 二级联动 -->
			<view v-else-if="level == 2" class="flex list">
				<scroll-view scroll-y class="flex-col linkage-list1" :scroll-into-view="scrollFirstView">
					<view :id="`p-cell${item.id}`" class="flex cell" :class="{ active: isSelected(item.id, 1) || firstSelectedIndex == index }" v-for="(item, index) in firstList" :key="index" @click="onItemClick(index, item, 1)">
						<text>{{ `${item.name}${countById(item.id, 1) !== 0 ? '(' + countById(item.id, 1) + ')' : ''}` }}</text>
					</view>
				</scroll-view>
				<scroll-view scroll-y class="flex-col linkage-list2" :scroll-into-view="scrollSecondView">
					<view v-if="mode == 'list'" class="flex-col">
						<view :id="`c-cell${item.id}`" class="flex cell" :class="{ active: isSelected(item.id, 2) }" v-for="(item, index) in secondList" :key="index" @click="onItemClick(index, item, 2)">
							<text>{{ item.name }}</text>
							<image class="check" :src="isSelected(item.id, 2) ? '/static/image/ic_checkbox_sel.png' : '/static/image/ic_checkbox_nor.png'" mode="aspectFit"></image>
						</view>
					</view>
					<view v-else-if="mode == 'grid'" class="flex" style="padding: 15rpx 25rpx; flex-wrap: wrap">
						<view :id="`c-cell${item.id}`" class="flex grid-cell" :class="{ active: isSelected(item.id, 2) }" v-for="(item, index) in secondList" :key="index" @click="onItemClick(index, item, 2)">
							<text>{{ item.name }}</text>
						</view>
					</view>
				</scroll-view>
			</view>

			<view class="flex-col bottom" v-if="maxNum > 1">
				<view class="flex wrap">
					<view class="item animate__animated animate__zoomIn animate__faster" v-for="item in selectedAllItems" :key="item.id">
						{{ item.name }}
						<image src="/static/image/ic_tag_delete.png" @click="removeSelectedItem(item)" mode="aspectFit"></image>
					</view>
				</view>
				<view class="flex confirm" @click="onConfirm">{{ confimText }}</view>
			</view>
		</view>
		<u-safe-bottom :customStyle="{ background: '#FFF' }"></u-safe-bottom>
	</view>
</template>

<script>
export default {
	name: 'popup-multiple-city',
	props: {
		//  弹窗表示
		ident: {
			type: String,
			default: 'link'
		},
		// 弹窗状态
		show: {
			type: Boolean,
			default: false
		},
		// 标题
		title: {
			type: String,
			default: '请选择'
		},
		// 联动层级
		level: {
			type: Number,
			default: 2
		},
		// 二级联动时第二级的排列方式  (list/grid)
		mode: {
			type: String,
			default: 'list'
		},
		//  最大选择的数量
		maxNum: {
			type: Number,
			default: 2
		},
		// 默认读取的键名
		keyName: {
			type: String,
			default: 'name'
		},
		// 选择的数据
		// [id:'',name:'',child:[{id:'',name:''}]]
		value: {
			type: Array,
			default: () => []
		},
		// 完成事件
		complete: {
			type: Function,
			default: () => {}
		}
	},
	watch: {
		// 监听弹窗状态
		show(val) {
			if (val) {
				this.init();
			}
		}
	},
	data() {
		return {
			// 联动数据
			dataList: [],
			// 一级数据集合
			firstList: [],
			// 二级数据集合
			secondList: [],

			// 选中的数据 格式 [id:'',name:'',child:[{id:'',name:''}]]
			selectedItems: [],
			// 当前选中一级的索引
			firstSelectedIndex: -1,
			// 默认滚动到指定位置
			scrollFirstView: '',
			scrollSecondView: ''
		};
	},
	computed: {
		/**
		 * 将不同层级的选中项合并为一个扁平化的数组
		 * @returns {Array} - 扁平化的选中项数组
		 */
		selectedAllItems() {
			if (this.level == 1) {
				return this.selectedItems;
			} else if (this.level == 2) {
				return this.selectedItems.flatMap((item) => item.child.map((child) => ({ id: child.id, parentId: item.id, name: child.name })));
			}
			return [];
		},
		// 确认按钮文本
		confimText() {
			if (this.maxNum > 1) {
				return `确认选择（${this.selectedAllItems.length}/${this.maxNum}）`;
			}
			return '确认选择';
		}
	},
	created() {
		this.init();
	},
	methods: {
		async loadCitys() {
			const cacheData = uni.getStorageSync('a-citys-link-list') ?? [];
			if (uni.$u.test.isEmpty(cacheData)) {
				try {
					const data = await uni.$u.http.get('/system/area/tree');
					this.firstList = data ?? [];
					uni.setStorage({
						key: 'a-citys-link-list',
						data: this.firstList
					});
				} catch (error) {
					console.error('获取城市数据时发生错误：', error);
					this.firstList = [];
				}
			} else {
				this.firstList = cacheData;
			}
		},
		// 初始化数据
		async init() {
			// 异步加载城市数据
			await this.loadCitys();

			// 检查 firstList 是否为空
			if (this.firstList.length === 0) return;

			if (this.level === 2 && this.firstList.length) {
				this.secondList = this.firstList[0].children;
			}

			// 初始化选中项
			this.selectedItems = this.value;

			// 更新视图的 scroll 位置
			this.$nextTick(() => {
				if (this.selectedItems.length > 0) {
					const firstItem = this.selectedItems[0];

					// 设置 scrollFirstView
					this.scrollFirstView = `p-cell${firstItem.id}`;

					if (this.level === 2) {
						// 找到对应的一级项
						const selectedFirstItem = this.firstList.find((item) => String(item.id) === String(firstItem.id));
						if (selectedFirstItem && selectedFirstItem.children) {
							this.secondList = selectedFirstItem.children;
						}

						// 设置二级视图的 scroll 位置
						if (firstItem.child && firstItem.child.length > 0) {
							this.scrollSecondView = `c-cell${firstItem.child[0].id}`;
						}
					}
				} else if (this.firstList.length > 0) {
					// 如果没有选中项，则将 scrollView 设置到第一个元素
					// this.scrollFirstView = `p-cell${this.firstList[0].id}`;
				}
			});
		},
		/**
		 * 计算指定级别下指定 ID 的选中项的数量
		 * @param {number} id - 要查找的项的 ID
		 * @param {number} level - 要查找的项的级别（1 或 2）
		 * @param {Array} [items=this.selectedItems] - 当前级别的选中项列表（默认为 this.selectedItems）
		 * @returns {number} - 指定级别下指定 ID 的选中项的数量
		 */
		countSelectedByLevel(id, level, items = this.selectedItems) {
			let count = 0;

			items.forEach((item) => {
				if (item.level === level && item.id === id) {
					count++;
				}
				if (item.child) {
					count += this.countSelectedByLevel(item.parentId, level, item.child);
				}
			});

			return count;
		},
		/**
		 * 处理列表项点击事件
		 * @param {number} index - 点击项的索引
		 * @param {Object} item - 点击项的对象
		 * @param {number} level - 点击项的级别（1、2 或 3）
		 */
		onItemClick(index, item, level) {
			if (level === 1) {
				this.firstSelectedIndex = index;
				this.secondList = item.children || [];

				if (this.level === 1) {
					// 检查该项是否已经存在于 selectedItems 中
					const existingIndex = this.selectedItems.findIndex((selectedItem) => String(selectedItem.id) === String(item.id));
					if (existingIndex !== -1) {
						// 如果该项已存在，则从 selectedItems 中删除
						this.selectedItems.splice(existingIndex, 1);
					} else {
						const selectedItemsCount = this.selectedItems.length;
						if (selectedItemsCount >= this.maxNum) {
							uni.showToast({
								title: `最多允许选择 ${this.maxNum} 项`,
								icon: 'none'
							});
							return; // 超过最大限制
						}
						// 如果该项不存在，则添加到 selectedItems 中
						this.selectedItems.push({
							// ...item,
							id: item.id,
							name: item.name,
							level: 1,
							count: 0,
							child: []
						});
					}
				}
			} else {
				// 获取父项的 ID 和当前级别的选中项
				const parentId = item.parentId;
				const parentLevel = level - 1;
				const currentLevelItems = this.selectedItems.filter((i) => i.level === parentLevel);

				// 查找或创建父项
				let parentItem = currentLevelItems.find((i) => String(i.id) === String(parentId));
				if (!parentItem) {
					parentItem = {
						id: parentId,
						level: parentLevel,
						name: this.firstList.find((listItem) => String(listItem.id) === String(parentId))?.name || '', // 确保有 name 字段
						count: 0,
						child: []
					};
					this.selectedItems.push(parentItem);
				}

				// 更新子项
				const existingItem = parentItem.child.find((i) => String(i.id) === String(item.id));
				if (existingItem) {
					parentItem.child = parentItem.child.filter((i) => String(i.id) !== String(item.id));
				} else {
					// 检查选择数量
					const selectedItemsCount = this.selectedItems.flatMap((item) => item.child).length;
					if (selectedItemsCount >= this.maxNum) {
						uni.showToast({
							title: `最多允许选择 ${this.maxNum} 项`,
							icon: 'none'
						});
						return; // 超过最大限制
					}
					parentItem.child.push({
						// ...item,
						id: item.id,
						parentId: item.parentId,
						name: item.name,
						level: level
					});
				}
				// 如果父项的子项被清空，删除父项
				if (parentItem.child.length === 0) {
					this.selectedItems = this.selectedItems.filter((i) => String(i.id) !== String(parentItem.id));
				} else {
					// 更新父项的子项数量
					parentItem.count = parentItem.child.length;
				}
				// 确保所有祖先节点的 count 都得到更新
				this.updateParentCount(parentItem);
			}
			console.error('选中的结果', this.selectedItems);
		},
		/**
		 * 更新指定项的所有祖先节点的 count
		 * @param {Object} item - 当前处理的项
		 */
		updateParentCount(item) {
			let currentItem = item;
			while (currentItem) {
				// 更新当前项的 count
				const parentItem = this.selectedItems.find((i) => String(i.id) === String(currentItem.id));
				if (parentItem) {
					parentItem.count = parentItem.child.length;
				}

				// 向上查找父项
				currentItem = this.selectedItems.find((i) => String(i.id) === String(currentItem.parentId));
			}
		},
		/**
		 * 移除已选中的类别
		 * @param {Object} item - 需要移除的项
		 */
		removeSelectedItem(item) {
			const removeItemRecursively = (items, itemId) => {
				return items.reduce((acc, item) => {
					if (item.id === itemId) {
						// 找到要删除的项，直接跳过
						return acc;
					}

					// 处理子项
					if (item.child) {
						item.child = removeItemRecursively(item.child, itemId);
						// 如果子项删除后没有子项，则删除当前项
						if (item.child.length === 0 && item.parentId !== null) {
							return acc;
						}

						// 更新 count
						item.count = item.child.length;
					}

					acc.push(item);
					return acc;
				}, []);
			};

			// 执行删除操作
			this.selectedItems = removeItemRecursively(this.selectedItems, item.id);

			// 输出删除后的结果
			console.error('删除后的结果', this.selectedItems);
			console.error('删除元素', this.selectedItems);
		},
		/**
		 * 判断项是否被选中
		 * @param {number} id - 项的 ID
		 * @param {number} level - 联动级别（1、2 或 3）
		 * @returns {boolean} - 返回是否选中
		 */
		isSelected(id, level) {
			const checkLevel = (items, targetId, targetLevel) => {
				for (const item of items) {
					if (String(item.id) === String(targetId) && item.level === targetLevel) {
						return true;
					}
					if (item.child && item.child.length > 0) {
						const found = checkLevel(item.child, targetId, targetLevel);
						if (found) return true;
					}
				}
				return false;
			};

			return checkLevel(this.selectedItems, id, level);
		},
		/**
		 * 根据 ID 和级别获取对应项的 count
		 * @param {number} id - 要查找的项的 ID
		 * @param {number} level - 要查找的项的级别（1、2 或 3）
		 * @returns {number} - 对应项的 count
		 */
		countById(id, level) {
			// 查找指定 ID 和级别的项
			const item = this.selectedItems.find((i) => String(i.id) === String(id) && i.level === level);
			// 如果找到，返回其 count，否则返回 0
			return item?.count || 0;
		},

		// 确认
		onConfirm() {
			const res = { ident: this.ident, detail: this.selectedItems };
			this.complete && this.complete(res);
			this.$emit('confirm', res);
			this.onCancel();
		},
		// 取消
		onCancel() {
			this.$emit('close', { ident: this.ident });
		}
	}
};
</script>

<style lang="scss" scoped>
.popup-multiple-city {
	width: 750rpx;
	background-color: #ffffff;
	display: flex;
	flex-direction: column;
	border-radius: 20rpx 20rpx 0 0;
}

.wrap {
	background: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
}

.header {
	width: 100%;
	height: 120rpx;
	padding: 0 30rpx;
	justify-content: flex-start;
	align-items: center;
	border-bottom: 12rpx solid #f8f8f8;
	font-size: 36rpx;
	font-weight: 500;
	color: #333333;
}

.list {
	width: 100%;
	height: 800rpx;
	background: #ffffff;
	justify-content: space-between;
	align-items: center;

	.normal-list,
	.linkage-list1,
	.linkage-list2 {
		width: 100%;
		height: 100%;
		background: #ffffff;
		.cell {
			height: 100rpx;
			padding: 0 30rpx;
			justify-content: space-between;
			align-items: center;
			font-size: 30rpx;
			font-weight: 500;
			color: #333333;

			.check {
				width: 36rpx;
				height: 36rpx;
			}

			&.active {
				color: #444bf1;
			}
		}
	}

	.linkage-list1 {
		width: 250rpx;
		background: #f5f5f5;
		&.active {
			color: #444bf1;
			background: #ffffff;
		}
	}

	.linkage-list2 {
		width: 500rpx;
		flex-wrap: wrap;
		.grid-cell {
			width: 215rpx;
			height: 60rpx;
			margin: 10rpx 0rpx;
			margin-right: 20rpx;
			background: #f5f5f5;
			border-radius: 10rpx;
			justify-content: center;
			font-weight: 400;
			font-size: 24rpx;
			color: #666666;

			&:nth-child(2n) {
				margin-right: 0;
			}

			&.active {
				color: #444bf1;
				background: #f3f4ff;
				border: 0.8px solid #444bf1;
			}
		}
	}
}

.bottom {
	padding: 30rpx;
	background: #ffffff;
	box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(181, 181, 181, 0.38);

	.wrap {
		flex-wrap: wrap;

		.item {
			padding: 5rpx 10rpx;
			margin-right: 30rpx;
			margin-bottom: 15rpx;
			// border: 0.8px solid #444BF1;
			// color: #444BF1;
			// background: #F5F5F5;

			border: 1px solid #cccccc;
			background: #f5f5f5;
			color: #333333;
			font-size: 24rpx;
			border-radius: 7rpx;
			position: relative;

			image {
				width: 20rpx;
				height: 20rpx;
				position: absolute;
				right: -10rpx;
				top: -10rpx;
			}
		}
	}

	.confirm {
		height: 100rpx;
		background: #444bf1;
		justify-content: center;
		border-radius: 20rpx;
		font-size: 34rpx;
		font-weight: 600;
		color: #ffffff;
	}
}
</style>
