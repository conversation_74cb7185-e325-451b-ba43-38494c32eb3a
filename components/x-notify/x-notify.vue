<template>
	<u-transition mode="fade-up" :customStyle="containerStyle" :show="open">
		<view class="notify">
			<view class="notify__warpper" :style="[backgroundColor]" @click="onClick">
				<text class="notify__warpper__title">{{notifyPopup.title || ''}}</text>
				<view class="notify__warpper__bottom">
					<text class="notify__warpper__bottom__subtitle">{{notifyPopup.content || ''}}</text>
					<text class="notify__warpper__bottom__btn">{{notifyPopup.btn_name || '立即查看'}}</text>
				</view>
				<view class="notify__close" @click="close">
					<image class="notify__close__image" src="/static/image/ic_close.png" mode="widthFix"></image>
				</view>
			</view>
			<u-safe-bottom></u-safe-bottom>
		</view>
	</u-transition>
</template>

<script>
	import {
		mapState,
		mapMutations,
	} from 'vuex';
	// #ifdef APP-PLUS
	import aliyunPushHelper from '@/common/aliPush.js';
	// #endif
	export default {
		name: "x-notify",
		props: {
			// 背景颜色
			bgColor: {
				type: String,
				default: '#FFFFFF'
			},
			// 展示时长，为0时不消失，单位ms
			duration: {
				type: [String, Number],
				default: 0
			}
		},
		data() {
			return {
				timer: null,
				config: {
					// 背景颜色
					bgColor: '#FFFFFF',
					// 展示时长，为0时不消失，单位ms 
					duration: 20000,
				},
				// 合并后的配置，避免多次调用组件后，可能会复用之前使用的配置参数
				tmpConfig: {}
			}
		},
		computed: {
			...mapState(['notifyPopup']),

			containerStyle() {
				let top = 0
				const style = {
					background: 'transparent',
					bottom: uni.$u.addUnit(60),
					// 因为组件底层为u-transition组件，必须将其设置为fixed定位
					// 让其出现在导航栏底部
					position: 'fixed',
					left: 0,
					right: 0,
					zIndex: 10076
				}
				return style
			},
			// 组件背景颜色
			backgroundColor() {
				const style = {}
				if (this.tmpConfig.bgColor) {
					style.backgroundColor = this.tmpConfig.bgColor
				}
				return style
			},
			// 是否打开
			open() {
				if (this.notifyPopup.show && !uni.$u.test.isEmpty(this.notifyPopup.title)) {
					return this.notifyPopup.show
				}
				return false
			}
		},
		watch: {
			'notifyPopup.show': {
				handler(newVal, oldVal) {
					if (newVal && !uni.$u.test.isEmpty(this.notifyPopup.title)) {
						this.show({})
					} else {
						this.close()
					}
				},
				immediate: true
			},
		},
		beforeDestroy() {
			this.clearTimer()
		},
		methods: {
			...mapMutations(['SET_NOTIFY_POPUP']),
			show(options) {
				// 不将结果合并到this.config变量，避免多次调用u-toast，前后的配置造成混乱
				this.tmpConfig = uni.$u.deepMerge(this.config, options)
				// 任何定时器初始化之前，都要执行清除操作，否则可能会造成混乱
				this.clearTimer()

				if (this.tmpConfig.duration > 0) {
					this.timer = setTimeout(() => {
						this.SET_NOTIFY_POPUP({
							show: false
						})
						// 倒计时结束，清除定时器，隐藏toast组件
						this.clearTimer()
						// 判断是否存在callback方法，如果存在就执行
						typeof(this.tmpConfig.complete) === 'function' && this.tmpConfig.complete()
					}, this.tmpConfig.duration)
				}
			},
			// 关闭notify
			close() {
				this.SET_NOTIFY_POPUP({
					show: false
				})
				this.clearTimer()
			},
			// 清除定时器
			clearTimer() {
				clearTimeout(this.timer)
				this.timer = null
			},
			// 通知点击
			onClick() {
				// #ifdef APP-PLUS
				let params = this.notifyPopup
				aliyunPushHelper.jumpPage(params)
				// #endif
				this.close()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.notify {
		padding: 0 30rpx;
		position: relative;

		&__close {
			width: 35rpx;
			height: 35rpx;
			display: flex;
			flex-direction: column;
			align-items: flex-end;
			justify-content: flex-start;
			position: absolute;
			right: 50rpx;
			top: 20rpx;

			&__image {
				width: 20rpx;
				height: 20rpx;
			}
		}

		&__warpper {
			padding: 20rpx;
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			text-align: center;
			justify-content: center;
			box-shadow: 0rpx 2rpx 35rpx 0rpx rgba(56, 56, 56, 0.29);
			border-radius: 10rpx;
			background-color: #FFFFFF;

			&__title {
				font-size: 30rpx;
				font-weight: 700;
				color: #333333;
			}

			&__bottom {
				width: 650rpx;
				margin-top: 10rpx;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;

				&__subtitle {
					max-width: 550rpx;
					font-size: 28rpx;
					font-weight: 500;
					color: #999999;
					overflow: hidden;
					text-align: left;
				}

				&__btn {
					font-size: 28rpx;
					font-weight: 500;
					color: #6C68F0;
					white-space: nowrap;
				}
			}
		}
	}
</style>