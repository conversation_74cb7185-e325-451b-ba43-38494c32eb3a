<template>
	<view :class="['m-tabbar', isFixed ? 'x-fixed' : '']">
		<!-- 二次封装tabbar -->
		<u-tabbar :value="tabIndex" @change="onTabbar" :fixed="false" :placeholder="isFillHeight" :safeAreaInsetBottom="true" :activeColor="activeColor" :inactiveColor="inactiveColor" :border="border">
			<block v-for="(item, index) in tabbarList" :key="index">
				<u-tabbar-item :customStyle="{}" :text="item.text" :badge="item.count" :dot="item.isDot" :badgeStyle="item.badgeStyle">
					<view slot="active-icon">
						<image :class="[item.mid ? 'mid-icon' : 'icon']" :src="item.selectedIconPath" mode="aspectFill"></image>
					</view>
					<view slot="inactive-icon">
						<image :class="[item.mid ? 'mid-icon' : 'icon']" :src="item.iconPath" mode="aspectFill"></image>
					</view>
				</u-tabbar-item>
			</block>
		</u-tabbar>
		<!-- #ifdef H5 -->
		<u-safe-bottom></u-safe-bottom>
		<!-- #endif -->
	</view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
export default {
	name: 'x-tabbar',
	props: {
		// 是否固定在底部
		isFixed: {
			type: Boolean,
			default: false
		},
		// 是否设置防止塌陷高度
		isFillHeight: {
			type: Boolean,
			default: false
		},
		// 选中的颜色--为空显示主题色
		activeColor: {
			type: String,
			default: ''
		},
		// 未选中颜色
		inactiveColor: {
			type: String,
			default: '#606266'
		},
		// 是否显示边框色
		border: {
			type: Boolean,
			default: function () {
				return false;
			}
		}
	},
	computed: {
		...mapState(['tabbarList'])
	},
	data() {
		return {
			// 选中Tab索引
			tabIndex: 0,
			// 当前路径
			path: ''
		};
	},
	created() {
		//获取页面路径
		let currentPages = getCurrentPages();
		let page = currentPages[currentPages.length - 1];
		this.path = page.route;
		//获取页面路径
		this.tabbarList.forEach((item, index) => {
			if (this.path == item.pagePath) {
				this.tabIndex = index;
			}
		});
	},
	methods: {
		...mapMutations(['SET_PUBLISH_POPUP_SHOW']),
		onTabbar(index) {
			
			const el = this.tabbarList[index];
			// 发布弹窗
			if (el.ident == 'publish') {
				this.SET_PUBLISH_POPUP_SHOW(true);
				return;
			}
			// 跳转页面
			if (this.path !== el.pagePath) {
				uni.switchTab({
					url: '/' + el.pagePath
				});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.m-tabbar {
	box-shadow: 0rpx -3rpx 8rpx 0rpx rgba(198, 198, 198, 0.3);
}

.x-fixed {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 1000;
}

.icon {
	width: 44rpx;
	height: 44rpx;
}

.mid-icon {
	width: 100rpx;
	height: 80rpx;
}
</style>
