<template>
	<view>
		<public-module></public-module>
		<z-paging ref="paging" v-model="dataList" use-chat-record-mode use-virtual-list cell-height-mode="dynamic"
			:auto-clean-list-when-reload="false" :default-page-size="10" :local-paging-loading-time="0"
			safe-area-inset-bottom bottom-bg-color="#f8f8f8" :auto-show-back-to-top="showNewMessageTip"
			@backToTopClick="onBackToTopClick" @scrolltoupper="onScrollToUpper"
			@keyboardHeightChange="keyboardHeightChange" @hidedKeyboard="hidedKeyboard" @query="queryList">
			<!-- 顶部导航 -->
			<template #top>
				<u-navbar bgColor="#FFFFFF" title="消息列表" :statusBar="true" :placeholder="true" :autoBack="true" />
			</template>


			<!-- style="transform: scaleY(-1)"必须写，否则会导致列表倒置！！！ -->
			<!-- 注意不要直接在chat-item组件标签上设置style，因为在微信小程序中是无效的，请包一层view -->
			<template #cell="{ item, index }">
				<view style="transform: scaleY(-1)">
					<ChatTextCell :ref="`z-paging-${index}`" v-if="item.contentType === chatCons.CONTENT_TYPE.TEXT"
						:key="index" :message="item" />
					<ChatQuestionCell :ref="`z-paging-${index}`"
						v-if="item.contentType === chatCons.CONTENT_TYPE.FAQ_QUESTION" :message="item" />
					<ChatQuestionOptionCell :ref="`z-paging-${index}`"
						v-if="item.contentType === chatCons.CONTENT_TYPE.FAQ_OPTION" :message="item" />
				</view>
			</template>

			<!-- 底部聊天输入框 -->
			<template #bottom>
				<ChatInputBar ref="inputBar" @send="onSendMessage"></ChatInputBar>
			</template>
			<!-- 查看最新消息 -->
			<template #backToTop>
				<text>有新消息</text>
			</template>
		</z-paging>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions,
		mapMutations
	} from 'vuex';
	import {
		webSocketService
	} from '@/common/webscoket.js';
	import chatCons from '@/pagesChat/utils/constants.js';
	import {
		generateConversationNo
	} from '@/pagesChat/utils/tool.js';

	import ChatInputBar from '../component/chat-input-bar/chat-input-bar.vue';
	import ChatTextCell from '../component/chat-text-cell/chat-text-cell.vue';
	import ChatImageCell from '../component/chat-image-cell/chat-image-cell.vue';
	import ChatQuestionCell from '../component/chat-question-cell/chat-question-cell.vue';
	import ChatQuestionOptionCell from '../component/chat-question-option-cell/chat-question-option-cell.vue';
	import ChatOpenVipCell from '../component/chat-open-vip-cell/chat-open-vip-cell.vue';
	import ChatProviderCardCell from '../component/chat-provider-card-cell/chat-provider-card-cell.vue';
	import ChatProductCell from '../component/chat-product-cell/chat-product-cell.vue';
	export default {
		components: {
			ChatInputBar,
			ChatTextCell,
			ChatQuestionCell,
			ChatQuestionOptionCell,
			ChatOpenVipCell,
			ChatProviderCardCell,
			ChatProductCell
		},
		data() {
			return {
				chatCons: chatCons,
				// 小程序胶囊
				mpMenuRect: {},
				// 状态栏高度
				statusBarHeight: 0,
				// 导航栏高度
				navBarHeight: 44,

				// 消息列表
				queryParams: {
					pageNo: 1,
					pageSize: 10,
					createTime: undefined
				},
				dataList: [],

				// 显示有新消息提示
				showNewMessageTip: false,
				// 更新消息列表
				refreshMessage: false
			};
		},
		computed: {
			...mapState(['userInfo', 'conversationNo']),
			...mapGetters(['getterIsLogin'])
		},
		watch: {
			// 监听弹窗状态
			// msgListLength: {
			// 	handler(newLength, oldLength) {
			// 		if (newLength > oldLength) {
			// 			this.scrollToBottom();
			// 		}
			// 	}
			// },
		},
		onLoad(options) {
			// 进入会话聊天
			this.enterConversation({
				targetId: options.targetId,
				type: options.conversationType
			})

			// 设置当前会话
			this.SET_CURRENT_CONVERSATION_NO(options.conversationNo)

			this.queryParams = {
				...this.queryParams,
				conversationNo: options.conversationNo,
				targetId: options.targetId,
				conversationType: options.conversationType
			};

			console.error("打印", this.queryParams)

			webSocketService.on(this.chatCons.WEBSOCKET_MESSAGE_TYPE_ENUM.IM_MESSAGE_RECEIVE, async (data) => {

				const content = JSON.parse(data.content);

				console.error("聊天页面的消息", content)

				if (generateConversationNo(content.senderId, content.receiverId, content.conversationType) !==
					this.queryParams.conversationNo) {
					// 不处理非当前会话的消息
					return;
				}

				await this.refreshMessageList(JSON.parse(data.content));
			});
		},
		onUnload() {
			// 清除当前会话
			this.SET_CURRENT_CONVERSATION_NO('')
		},
		onShow() {},
		methods: {
			...mapMutations(['SET_CURRENT_CONVERSATION_NO']),
			...mapActions(['fetchUser', 'fetchSysConf', 'enterConversation', 'sendMessage',
				'getLocalMessagesByConversationNo'
			]),

			scrollToBottom() {},

			async onSendMessage(value) {
				if (!value) return;
				try {
					// 先添加 再发送
					const message = {
						conversationNo: this.queryParams.conversationNo,
						clientMessageId: uni.$u.guid(10),
						avatar: this.userInfo.avatar,
						nickname: this.userInfo.nickname,
						createTime: new Date().getTime(),
						isRead: false,
						content: value,
						sendStatus: this.chatCons.SEND_STATUS.SENDING,
						senderId: this.userInfo.Id,
						receiverId: this.queryParams.targetId,
						conversationType: this.queryParams.conversationType,
						// conversationUserId: this.queryParams.userId || '',
						contentType: this.chatCons.CONTENT_TYPE.TEXT
					};

					// 1. 添加到 UI 列表中（如 dataList）
					this.$refs.paging.addChatRecordData([message]);

					// 2. 通过 store 发送
					const updatedMsg = await this.sendMessage(message);

					// 3. 替换 UI 中的状态
					const idx = this.dataList.findIndex((m) => m.clientMessageId === updatedMsg.clientMessageId);
					if (idx !== -1) {
						this.dataList.splice(idx, 1, updatedMsg);
					}

					console.error('接收到的数据--', this.dataList);

					// chat.msg = '';
				} finally {
					// chat.showTools = false;
				}
			},

			async queryList(pageNo, pageSize) {
				this.queryParams.pageNo = pageNo;
				this.queryParams.pageSize = pageSize;
				await this.getMessageList();
			},

			// 刷新消息列表
			async refreshMessageList(msgData = undefined) {
				if (typeof msgData !== 'undefined') {
					// 追加数据
					const message = {
						id: msgData.id.toString(), // 服务端也应该返回一个clientMessageId
						clientMessageId: msgData.id.toString(),
						avatar: msgData.senderAvatar,
						nickname: msgData.senderNickname,
						sendTime: msgData.sendTime,
						isRead: false,
						content: msgData.content,
						contentType: msgData.contentType,
						sendStatus: chatCons.SEND_STATUS.SUCCESS,
						conversationId: '',
						senderId: msgData.senderId,
						receiverId: msgData.receiverId,
						conversationType: msgData.conversationType,
						conversationUserId: 0
					};

					this.$refs.paging.addChatRecordData([message]);
				} else {
					this.queryParams.createTime = undefined;
					this.refreshMessage = true;
					await this.getMessageList();
				}

				// 若已是第一页则不做处理
				// if (this.queryParams.pageNo > 1) {
				// 	this.showNewMessageTip = true;
				// } else {
				// 	this.onScrollToUpper();
				// }
			},

			// 滚动到最新消息
			onBackToTopClick(event) {
				event(false); // 禁用默认操作
				this.$refs.paging.value.scrollToBottom();
			},
			// 监听滚动到底部的事件 （因为scroll翻转了 顶就是底）
			onScrollToUpper() {
				// 若已是第一页则不做处理
				if (this.queryParams.pageNo === 1) {
					return;
				}
				this.showNewMessageTip = false;
			},
			// 监听键盘高度改变，请不要直接通过uni.onKeyboardHeightChange监听，否则可能导致z-paging内置的键盘高度改变监听失效（如果不需要切换表情面板则不用写）
			keyboardHeightChange(res) {
				this.$refs.inputBar.updateKeyboardHeightChange(res);
			},
			// 用户尝试隐藏键盘，此时如果表情面板在展示中，应当通知chatInputBar隐藏表情面板（如果不需要切换表情面板则不用写）
			hidedKeyboard() {
				this.$refs.inputBar.hidedKeyboard();
			},

			// 获得消息分页列表
			async getMessageList() {
				const list = await this.getLocalMessagesByConversationNo(this.queryParams.conversationNo);

				this.$refs.paging.setLocalPaging(list, true);
				// this.$refs.paging.completeByNoMore(data, false);
				console.error('是否有更多数据', list);

				return Promise.resolve();

				const data = await uni.$u.http
					.get('/im/message/list', {
						params: this.queryParams
					})
					.catch((e) => {
						throw new Error(`请求失败: ${e.message}`);
					});
				console.error('请求得到的数据1', data);
				if (uni.$u.test.isEmpty(data)) {
					this.$refs.paging.completeByNoMore([], true);
					return Promise.resolve();
				}
				if (this.queryParams.pageNo > 1 && this.refreshMessage) {
					const newMessageList = [];
					for (const message of data) {
						if (this.messageList.some((val) => val.id === message.id)) {
							continue;
						}
						newMessageList.push(message);
					}
					// 新消息追加到开头
					this.messageList = [...newMessageList, ...this.messageList];
					this.$refs.paging.updateCache(); // 更新缓存
					this.refreshMessage = false; // 更新好后重置状态
					return Promise.resolve();
				}
				console.error('请求得到的数据', data);
				if (data.slice(-1).length > 0) {
					// 记录最后一次查询的最后一条消息的 createTime
					this.queryParams.createTime = uni.$u.timeFormat(data.slice(-1)[0].sendTime, 'yyyy-mm-dd');
				}

				await this.$refs.paging.setLocalPaging(data, true);
				// this.$refs.paging.completeByNoMore(data, false);
				return Promise.resolve();
			}
		}
	};
</script>

<style lang="scss" scoped>
	/* 导航栏 */
	.nav-wraper {
		z-index: 999;
		width: 750rpx;
		background-color: #444bf1;

		&__content {
			flex: 1;
			justify-content: space-between;

			&__left {
				width: 220rpx;
				max-width: 220rpx;
				height: 80rpx;
				// padding: 0 30rpx;
			}

			&__right {
				width: 220rpx;
				height: 80rpx;
				padding: 0 30rpx;
				justify-content: flex-end;

				&__text {
					font-weight: bold;
					font-size: 30rpx;
					color: #ffffff;
				}
			}

			&__search {
				flex: 1;
				height: 65rpx;
				padding: 0 30rpx;
				background: #ffffff;
				box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(205, 205, 205, 0.36);
				border-radius: 40rpx;

				&__icon {
					width: 36rpx;
					height: 36rpx;
				}

				&__input {
					flex: 1;
					margin-left: 10rpx;
					font-size: 26rpx;
					color: #b3b2b2;
				}
			}
		}
	}

	/* 搜索框 */
	.search-wraper {
		width: 750rpx;
		padding: 20rpx 30rpx;
		background-color: #444bf1;
		justify-content: center;

		&__box {
			flex: 1;
			height: 80rpx;
			padding: 0 30rpx;
			background-color: #ffffff;
			border-radius: 10rpx;

			&__icon {
				width: 36rpx;
				height: 36rpx;
			}

			&__input {
				flex: 1;
				margin-left: 10rpx;
				font-size: 26rpx;
				color: #b3b2b2;
			}
		}
	}

	/* 分类 */
	.catgory-wraper {
		width: 750rpx;
		padding: 10rpx 0;
		background-color: #444bf1;

		&__scroll {
			flex: 1;
			white-space: nowrap;

			&__item {
				display: inline-flex;

				&__content {
					width: 160rpx;
					align-items: center;
					justify-content: center;

					&__icon {
						width: 96rpx;
						height: 96rpx;
					}

					&__text {
						line-height: 60rpx;
						font-size: 28rpx;
						color: #ffffff;
						white-space: nowrap;
					}
				}
			}
		}
	}

	/* 起名 */
	.naming-wraper {
		width: 750rpx;
		padding: 15rpx 30rpx;
		background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);

		&__swiper {
			flex: 1;
			height: 162rpx;

			swiper-item image {
				width: 100%;
				height: 100%;
			}
		}
	}

	/* 列表 */
	.wrap-list {
		padding: 0 30rpx;
		background-color: #f7f7f7;
		flex-direction: column;

		&__item {
			width: 100%;
			margin-top: 20rpx;
		}
	}
</style>