<template>
	<ChatBaseCell :message="message">
		<template #content>
			<view class="flex-col option-wrap">
				<view class="option-wrap__title">您是不是要咨询下面的问题</view>
				<view class="flex option-wrap__tags">
					<view class="option-wrap__tags__item" v-for="(item, index) in [1, 2, 3, 4, 5, 7, 5]" :key="index">
						<text>公司注册</text>
					</view>
				</view>
			</view>
		</template>
	</ChatBaseCell>
</template>

<script>
import ChatBaseCell from '../chat-base-cell/chat-base-cell.vue';

export default {
	name: 'chat-question-option-cell',
	components: {
		ChatBaseCell
	},
	props: {
		message: {
			type: Object,
			default: () => ({})
		}
	}
};
</script>

<style lang="scss" scoped>
.option-wrap {
	font-size: 32rpx;
	line-height: 1.5;
	color: #333;
	word-break: break-word;
	white-space: pre-wrap;

	&__title {
		margin-bottom: 20rpx;
		font-weight: 500;
		font-size: 28rpx;
		color: #1e2134;
		line-height: 60rpx;
		border-bottom: 0.5px solid #e6e6e6;
	}

	&__tags {
		flex-wrap: wrap;
		margin: -8rpx 0 0 -8rpx; // 负 margin 实现间距塌陷对齐
		&__item {
			margin: 8rpx 0 0 8rpx; // 每个标签之间上下左右间距
			padding: 5rpx 12rpx;
			background: #f1f1f3;
			border-radius: 4rpx;
			font-weight: 400;
			font-size: 28rpx;
			color: #1e2134;
		}
	}
}
</style>
