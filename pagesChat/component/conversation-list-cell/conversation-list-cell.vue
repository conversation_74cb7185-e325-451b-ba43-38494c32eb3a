<template>
	<view class="flex wraper" @click.stop="click">
		<view class="wraper__avatar">
			<image :src="item.avatar" mode="aspectFill"></image>
		</view>
		<view class="flex-col wraper__content">
			<text class="wraper__content__title line1">{{ item.nickname }}</text>
			<text class="wraper__content__subtitle line1" v-if="item.lastMsg">{{ item.lastMsg.content }}</text>
		<!-- 	<text class="wraper__content__title line1">系统消息</text>
			<text class="wraper__content__subtitle line1">恭喜您，已成功入驻本平台</text> -->
		</view>
		<view class="flex wraper__date">
			<text>{{item.sendTime | timeFrom}}</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'conversation-list-cell',
	props: {
		item: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {};
	},
	methods: {
		click() {
			this.$emit('click', { detail: this.item });
		}
	}
};
</script>

<style lang="scss" scoped>
.wraper {
	padding: 30rpx;

	&__avatar {
		width: 80rpx;
		height: 80rpx;
		background: #f5f5f5;
		border-radius: 10rpx;
		overflow: hidden;

		image {
			width: 100%;
			height: 100%;
		}
	}

	&__content {
		flex: 1;
		height: 80rpx;
		margin-left: 20rpx;
		justify-content: space-between;

		&__title {
			font-weight: 600;
			font-size: 30rpx;
			color: #1e2134;
		}

		&__subtitle {
			font-weight: 400;
			font-size: 24rpx;
			color: #757575;
		}
	}

	&__date {
		margin-left: auto;
		margin-bottom: auto;
		font-weight: 400;
		font-size: 24rpx;
		color: #909099;
	}
}
</style>
