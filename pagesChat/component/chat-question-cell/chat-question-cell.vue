<template>
	<ChatBaseCell :message="message">
		<template #content>
			<view class="flex-col question-wrap">
				<view class="question-wrap__title">您是不是要咨询下面的问题</view>
				<view class="question-wrap__item" v-for="(item, index) in questionList" :key="index" @click="onItemClick(item)">
					<view class="flex question-wrap__item__name">{{ `${index + 1}.${item.questionText}` }}</view>
				</view>
			</view>
		</template>
	</ChatBaseCell>
</template>

<script>
import ChatBaseCell from '../chat-base-cell/chat-base-cell.vue';

export default {
	name: 'chat-question-cell',
	components: {
		ChatBaseCell
	},
	props: {
		message: {
			type: Object,
			default: () => ({})
		}
	},
	computed: {
		questionList() {
			if (uni.$u.test.jsonString(this.message.content)) {
				return JSON.parse(this.message.content);
			}
			return [];
		}
	},
	methods: {
		onItemClick(item) {
			// 触发点击问题事件
			this.$emit('question', { message: this.message, question: item });
		}
	}
};
</script>

<style lang="scss" scoped>
.question-wrap {
	font-size: 32rpx;
	line-height: 1.5;
	color: #333;
	word-break: break-word;
	white-space: pre-wrap;

	&__title {
		font-weight: 500;
		font-size: 28rpx;
		color: #1e2134;
		line-height: 60rpx;
		border-bottom: 0.5px solid #e6e6e6;
	}

	&__item {
		font-weight: 400;
		font-size: 28rpx;
		color: #444bf1;
		line-height: 60rpx;
	}
}
</style>
