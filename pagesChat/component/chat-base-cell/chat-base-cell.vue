<template>
	<view class="chat-item" :class="isSelf ? 'reverse' : 'row'">
		<!-- 头像 -->
		<image class="avatar" :src="message.avatar || ''" mode="aspectFill" />

		<!-- 消息内容区域 -->
		<view class="content">
			<!-- 昵称 -->
			<text class="nickname">{{ message.nickname || '' }}</text>

			<!-- 消息气泡区域 -->
			<view class="bubble-wrapper" :class="{ self: isSelf }">
				<!-- 气泡和状态容器 -->
				<view class="bubble-container">
					<!-- 气泡内容 -->
					<view class="bubble">
						<!-- 气泡箭头 -->
						<view class="arrow" :class="isSelf ? 'right' : 'left'" />
						<!-- 消息内容插槽 -->
						<slot name="content" />
					</view>

					<!-- 消息状态指示器 -->
					<view class="status">
						<!-- 发送中状态 -->
						<view v-if="message.sendStatus === chatCons.SEND_STATUS.SENDING" class="sending" />
						<!-- 发送失败状态 -->
						<view v-else-if="message.sendStatus === chatCons.SEND_STATUS.FAILURE" class="failure" @click="handleResend" />
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState } from 'vuex';
import chatCons from '@/pagesChat/utils/constants.js';

export default {
	name: 'ChatBaseCell',
	props: {
		message: {
			type: Object,
			default: () => ({})
			// validator: (val) => {
			// 	return ['avatar', 'nickname', 'senderId', 'sendStatus'].every((key) => key in val);
			// }
		}
	},
	data() {
		return {
			chatCons
		};
	},
	computed: {
		...mapState(['userInfo']),
		isSelf() {
			// 判断是否是当前用户发送的消息
			return String(this.message.receiverId) !== String(this.userInfo.id);
		}
	},
	methods: {
		handleResend() {
			// 触发重新发送事件
			this.$emit('resend', this.message);
		}
	}
};
</script>

<style lang="scss" scoped>
/* 基础消息项样式 */
.chat-item {
	width: 100%;
	padding: 20rpx 14rpx;
	display: flex;
	box-sizing: border-box;

	/* 消息方向 - 他人消息（默认从左到右排列） */
	&.row {
		flex-direction: row;

		/* 内容对齐方式 */
		.content {
			align-items: flex-start;
		}
	}

	/* 消息方向 - 自己消息（从右到左排列） */
	&.reverse {
		flex-direction: row-reverse;

		/* 内容对齐方式 */
		.content {
			align-items: flex-end;
		}
	}
}

/* 头像样式 */
.avatar {
	width: 88rpx;
	height: 88rpx;
	border-radius: 8rpx;
	margin: 0 16rpx;
	background-color: #cccccc;
	flex-shrink: 0;
}

/* 内容区域 */
.content {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-width: 0;
}

/* 昵称样式 */
.nickname {
	font-size: 24rpx;
	color: #999999;
	margin-bottom: 8rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* 气泡容器 */
.bubble-wrapper {
	max-width: 80%;

	/* 自己消息的特殊样式 */
	&.self {
		.bubble-container {
			flex-direction: row-reverse;

			.status {
				margin-right: 8rpx;
				margin-left: 0;
			}
		}
	}
}

/* 气泡和状态容器 */
.bubble-container {
	display: flex;
	align-items: center; /* 关键：确保垂直居中 */
}

/* 消息气泡 */
.bubble {
	max-width: 100%;
	min-height: 48rpx; /* 箭头高度24rpx + 上下间距 */
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 16rpx 24rpx; /* 上下16rpx让文字居中 */
	font-size: 32rpx;
	color: #333333;
	position: relative;
	word-break: break-word;
	display: inline-block; /* 改为inline-block更符合气泡特性 */

	/* 气泡箭头 */
	.arrow {
		width: 0;
		height: 0;
		position: absolute;
		top: 16rpx; /* 与padding-top保持一致 */
		border: 12rpx solid transparent;

		&.left {
			left: -24rpx;
			border-right-color: #ffffff;
			// margin-top: -2rpx; /* 微调视觉对齐 */
		}
		&.right {
			right: -24rpx;
			border-left-color: #ffffff;
			// margin-top: -2rpx; /* 微调视觉对齐 */
		}
	}
}

/* 状态指示器容器 */
.status {
	flex-shrink: 0;
	margin-left: 8rpx;
	display: flex;
	align-items: center;
}

/* 发送中状态 */
.sending {
	width: 28rpx;
	height: 28rpx;
	border: 2rpx solid #cccccc;
	border-top-color: #333333;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

/* 发送失败状态 */
.failure {
	width: 28rpx;
	height: 28rpx;
	padding: 8rpx;
	border-radius: 50%;
	background-color: #f44336;
	position: relative;

	/* 感叹号 */
	&::after {
		content: '!';
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		color: white;
		font-size: 24rpx;
		font-weight: bold;
		pointer-events: none;
	}
}

/* 旋转动画 */
@keyframes spin {
	to {
		transform: rotate(360deg);
		filter: hue-rotate(0deg);
	}
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
	.bubble {
		background-color: #2a2a2a;

		.arrow.left {
			border-right-color: #2a2a2a;
		}
		.arrow.right {
			border-left-color: #2a2a2a;
		}
	}
}
</style>
