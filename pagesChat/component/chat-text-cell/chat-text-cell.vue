<template>
	<ChatBaseCell :message="message">
		<template #content>
			<view class="text-content">
				{{ message.content }}
			</view>
		</template>
	</ChatBaseCell>
</template>

<script>
import ChatBaseCell from '../chat-base-cell/chat-base-cell.vue';

export default {
	name: 'chat-question-cell',
	components: {
		ChatBaseCell
	},
	props: {
		message: {
			type: Object,
			default: () => ({})
		}
	}
};
</script>

<style lang="scss" scoped> 
.text-content {
	font-size: 32rpx;
	line-height: 1.4;
	color: #333;
	word-break: break-word;
	white-space: pre-wrap;
}
</style>
