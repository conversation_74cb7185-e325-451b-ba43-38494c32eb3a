<template>
	<ChatBaseCell :message="message">
		<template #content>
			<view class="image-wrap">
				<image></image>
			</view>
		</template>
	</ChatBaseCell>
</template>

<script>
import ChatBaseCell from '../chat-base-cell/chat-base-cell.vue';

export default {
	name: 'chat-open-vip-cell',
	components: {
		ChatBaseCell
	},
	props: {
		message: {
			type: Object,
			default: () => ({})
		}
	}
};
</script>

<style lang="scss" scoped>
.image-wrap {
	width: 200rpx;
	height: 200rpx;
}
</style>
