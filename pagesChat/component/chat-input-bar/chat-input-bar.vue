<!-- z-paging聊天输入框 -->
<template>
	<view class="wrap">
		<view class="flex wrap__bar">
			<view class="flex wrap__bar__input-box">
				<!-- :adjust-position="false"必须设置，防止键盘弹窗自动上顶，交由z-paging内部处理 -->
				<input :focus="focus" class="input" v-model="msg" :adjust-position="false" confirm-type="send" type="text" placeholder="请输入内容" @confirm="sendClick" />
			</view>
			<!-- 表情图标（如果不需要切换表情面板则不用写） -->
			<view class="wrap__bar__emoji">
				<image class="emoji-img" :src="`/static/${emojiType || 'emoji'}.png`" @click.stop="emojiChange"></image>
			</view>
			<view class="flex" :class="{ wrap__bar__send: true, 'wrap__bar__send-disabled': !sendEnabled }" @click.stop="sendClick">
				<text class="wrap__bar__send__text">发送</text>
			</view>
		</view>
		<!--  表情面板，这里使用height控制隐藏显示是为了有高度变化的动画效果（如果不需要切换表情面板则不用写） -->
		<view class="flex wrap__panel">
			<view class="flex wrap__panel__emoji" :style="[{ height: emojiType === 'keyboard' ? '400rpx' : '0px' }]">
				<scroll-view scroll-y style="height: 100%; flex: 1">
					<view class="emoji-content">
						<text class="text" v-for="(item, index) in emojisArr" :key="index" @click.stop="emojiClick(item)">
							{{ item }}
						</text>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'chat-input-bar',
	props: {
		disabled: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			msg: '',

			// 表情数组（如果不需要切换表情面板则不用写）
			emojisArr: [
				'😊',
				'😁',
				'😀',
				'😃',
				'😣',
				'😞',
				'😩',
				'😫',
				'😲',
				'😟',
				'😦',
				'😜',
				'😳',
				'😋',
				'😥',
				'😰',
				'🤠',
				'😎',
				'😇',
				'😉',
				'😭',
				'😈',
				'😕',
				'😏',
				'😘',
				'😤',
				'😡',
				'😅',
				'😬',
				'😺',
				'😻',
				'😽',
				'😼',
				'🙈',
				'🙉',
				'🙊',
				'🔥',
				'👍',
				'👎',
				'👌',
				'✌️',
				'🙏',
				'💪',
				'👻'
			],
			// 当前input focus（如果不需要切换表情面板则不用写）
			focus: false,
			// 当前表情/键盘点击后的切换类型，为空字符串代表展示表情logo但是不展示不展示表情面板（如果不需要切换表情面板则不用写）
			emojiType: ''
		};
	},
	computed: {
		sendEnabled() {
			return !this.disabled && this.msg.length;
		}
	},
	methods: {
		// 更新了键盘高度（如果不需要切换表情面板则不用写）
		updateKeyboardHeightChange(res) {
			if (res.height > 0) {
				// 键盘展开，将emojiType设置为emoji
				this.emojiType = 'emoji';
			}
		},
		// 用户尝试隐藏键盘，此时如果表情面板在展示中，应当隐藏表情面板，如果是键盘在展示中不用处理，z-paging内部已经处理（如果不需要切换表情面板则不用写）
		hidedKeyboard() {
			if (this.emojiType === 'keyboard') {
				this.emojiType = '';
			}
		},
		// 点击了切换表情面板/键盘（如果不需要切换表情面板则不用写）
		emojiChange() {
			this.$emit('emojiTypeChange', this.emojiType);
			if (this.emojiType === 'keyboard') {
				// 点击了键盘，展示键盘
				this.focus = true;
			} else {
				// 点击了切换表情面板
				this.focus = false;
				// 隐藏键盘
				uni.hideKeyboard();
			}
			this.emojiType = !this.emojiType || this.emojiType === 'emoji' ? 'keyboard' : 'emoji';
		},
		// 点击了某个表情，将其插入输入内容中（如果不需要切换表情面板则不用写）
		emojiClick(text) {
			this.msg += text;
		},

		// 点击了发送按钮
		sendClick() {
			if (!this.sendEnabled) return;
			this.$emit('send', this.msg);
			this.msg = '';
		}
	}
};
</script>

<style lang="scss" scoped>
/* Bar */
.wrap__bar {
	padding: 20rpx;
	align-items: center;
	border-top: solid 1px #f5f5f5;
	background-color: #ffffff;

	&__input-box {
		flex: 1;
		height: 80rpx;
		padding: 0 20rpx;
		background-color: #f1f1f3;
		border-radius: 10rpx;

		.input {
			flex: 1;
			font-size: 30rpx;
		}
	}

	&__emoji {
		width: 54rpx;
		height: 54rpx;
		margin: 10rpx 0rpx 10rpx 20rpx;
		.emoji-img {
			width: 54rpx;
			height: 54rpx;
		}
	}

	&__send {
		width: 120rpx;
		height: 80rpx;
		margin-left: 15rpx;
		justify-content: center;
		background-color: #007aff;
		border-radius: 10rpx;
		&__text {
			color: white;
			font-size: 30rpx;
			font-weight: 500;
		}
	}

	&__send-disabled {
		background-color: #bbbbbb;
	}
}

/* 面板 */
.wrap__panel {
	&__emoji {
		background-color: #f8f8f8;
		overflow: hidden;
		transition-property: height;
		transition-duration: 0.15s;
		/* #ifndef APP-NVUE */
		will-change: height;
		/* #endif */

		&__emoji-content {
			font-size: 30rpx;
			flex-wrap: wrap;
			padding-right: 10rpx;
			padding-left: 15rpx;
			padding-bottom: 10rpx;
			//
			.text {
				ont-size: 50rpx;
				margin-left: 15rpx;
				margin-top: 20rpx;
			}
		}
	}
}
</style>
