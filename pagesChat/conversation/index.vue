<template>
	<view>
		<public-module></public-module>
		<z-paging ref="paging" :auto-clean-list-when-reload="false" safe-area-inset-bottom bottom-bg-color="#f8f8f8">
			<!-- 顶部导航 -->
			<template #top>
				<u-navbar bgColor="#FFFFFF" title="消息列表" :statusBar="true" :placeholder="true" :autoBack="true" />
			</template>

			<view v-for="(item, index) in conversationList" :key="index">
				<conversation-list-cell :item="item" @click="onListItemClick(index, $event)"></conversation-list-cell>
			</view>
		</z-paging>
	</view>
</template>

<script>
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';
import chatCons from '@/pagesChat/utils/constants.js';
import ConversationListCell from '../component/conversation-list-cell/conversation-list-cell.vue';
export default {
	components: {
		ConversationListCell
	},
	data() {
		return {
			// 小程序胶囊
			mpMenuRect: {},
			// 状态栏高度
			statusBarHeight: 0,
			// 导航栏高度
			navBarHeight: 44,

			// 消息列表
			queryParams: { pageNo: 1, pageSize: 10, createTime: undefined },
			dataList: [],

			// 显示有新消息提示
			showNewMessageTip: false,
			// 更新消息列表
			refreshMessage: false
		};
	},
	computed: {
		...mapState(['userInfo', 'conversationList']),
		...mapGetters(['getterIsLogin']),

		msgList() {
			return this.currentSession ? this.currentSession?.msgList : [];
		}
		// msgListLength() {
		// 	return this.msgList.length;
		// }
	},
	watch: {
		// 监听弹窗状态
		conversationList: {
			handler(newVal, oldVal) {
				console.error('会话列表-----', newVal);
			}
		}
	},
	onLoad(options) {
		uni.hideTabBar();
		const sys = uni.getSystemInfoSync();
		this.statusBarHeight = sys.statusBarHeight;
		// #ifdef MP
		this.mpMenuRect = uni.getMenuButtonBoundingClientRect();
		// #endif

		this.getConversationList();
	},
	onShow() {},
	methods: {
		...mapActions(['fetchUser', 'fetchSysConf', 'getConversationList', 'addMessageToCurrentSession']),

		onListItemClick(index, item) {
			console.error("===",item)
			// let targetId = null;
			// if (this.userInfo.id == item.detail.userId) {
			// 	// 表示这个会话是自己发起的
			// 	targetId = item.detail.targetId;
			// } else {
			// 	// 表示这个会话是对方发起的
			// 	targetId = item.detail.userId; 
			// }

			this.onJump({
				url: '/pagesChat/chat/index',
				params: { conversationNo: item.detail.no, targetId: item.detail.targetId, conversationType: item.detail.type }
			});
		},

		scrollToBottom() {},

		async queryList(pageNo, pageSize) {
			this.queryParams.pageNo = pageNo;
			this.queryParams.pageSize = pageSize;
			await this.getMessageList();
		},

		// 获得消息分页列表
		async getMessageList() {
			// console.error("会话列表",this.currentSession?.msgList || [])
			// this.$refs.paging.complete(this.currentSession?.msgList || []);
			// const { data } = await uni.$u.http.get('', { params: this.queryParams }).catch((e) => {
			// 	throw new Error(`请求失败: ${e.message}`);
			// });
			// if (isEmpty(data)) {
			// 	this.$refs.paging.completeByNoMore([], true);
			// 	return Promise.resolve();
			// }
			// if (this.queryParams.pageNo > 1 && this.refreshMessage) {
			// 	const newMessageList = [];
			// 	for (const message of data) {
			// 		if (this.messageList.some((val) => val.id === message.id)) {
			// 			continue;
			// 		}
			// 		newMessageList.push(message);
			// 	}
			// 	// 新消息追加到开头
			// 	this.messageList = [...newMessageList, ...this.messageList];
			// 	this.$refs.paging.value.updateCache(); // 更新缓存
			// 	this.refreshMessage = false; // 更新好后重置状态
			// 	return Promise.resolve();
			// }
			// if (data.slice(-1).length > 0) {
			// 	// 记录最后一次查询的最后一条消息的 createTime
			// 	this.queryParams.createTime = formatDate(data.slice(-1)[0].createTime);
			// }
			// tis.$refs.paging.completeByNoMore(data, false);
			// return Promise.resolve();
		}
	}
};
</script>

<style>
page {
	background-color: #ffffff;
}
</style>

<style lang="scss" scoped>
/* 导航栏 */
.nav-wraper {
	z-index: 999;
	width: 750rpx;
	background-color: #ffffff;

	&__content {
		flex: 1;
		justify-content: space-between;

		&__left {
			width: 220rpx;
			max-width: 220rpx;
			height: 80rpx;
			// padding: 0 30rpx;
		}

		&__right {
			width: 220rpx;
			height: 80rpx;
			padding: 0 30rpx;
			justify-content: flex-end;

			&__text {
				font-weight: bold;
				font-size: 30rpx;
				color: #ffffff;
			}
		}

		&__search {
			flex: 1;
			height: 65rpx;
			padding: 0 30rpx;
			background: #ffffff;
			box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(205, 205, 205, 0.36);
			border-radius: 40rpx;

			&__icon {
				width: 36rpx;
				height: 36rpx;
			}

			&__input {
				flex: 1;
				margin-left: 10rpx;
				font-size: 26rpx;
				color: #b3b2b2;
			}
		}
	}
}

/* 搜索框 */
.search-wraper {
	width: 750rpx;
	padding: 20rpx 30rpx;
	background-color: #444bf1;
	justify-content: center;

	&__box {
		flex: 1;
		height: 80rpx;
		padding: 0 30rpx;
		background-color: #ffffff;
		border-radius: 10rpx;

		&__icon {
			width: 36rpx;
			height: 36rpx;
		}

		&__input {
			flex: 1;
			margin-left: 10rpx;
			font-size: 26rpx;
			color: #b3b2b2;
		}
	}
}

/* 分类 */
.catgory-wraper {
	width: 750rpx;
	padding: 10rpx 0;
	background-color: #444bf1;

	&__scroll {
		flex: 1;
		white-space: nowrap;

		&__item {
			display: inline-flex;

			&__content {
				width: 160rpx;
				align-items: center;
				justify-content: center;

				&__icon {
					width: 96rpx;
					height: 96rpx;
				}

				&__text {
					line-height: 60rpx;
					font-size: 28rpx;
					color: #ffffff;
					white-space: nowrap;
				}
			}
		}
	}
}

/* 起名 */
.naming-wraper {
	width: 750rpx;
	padding: 15rpx 30rpx;
	background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);

	&__swiper {
		flex: 1;
		height: 162rpx;

		swiper-item image {
			width: 100%;
			height: 100%;
		}
	}
}

/* 列表 */
.wrap-list {
	padding: 0 30rpx;
	background-color: #f7f7f7;
	flex-direction: column;
	&__item {
		width: 100%;
		margin-top: 20rpx;
	}
}
</style>
