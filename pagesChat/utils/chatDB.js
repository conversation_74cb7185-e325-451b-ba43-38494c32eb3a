// chatDBLimit.js
// 聊天本地存储模块，支持 App（SQLite）和小程序（Storage）端

import store from '@/store';
const storagePrefix = 'IM_CONVERSATION_LIST';
const conversationTable = 'conversations';
const messageTable = 'messages';
const platform = uni.getSystemInfoSync().platform;

/** 获取当前平台 */
function getPlatform() {
	return platform === 'android' || platform === 'ios' ? 'app' : 'miniapp';
}

/** 获取当前用户 ID */
function getCurrentUserId() {
	return store.state.userInfo.id;
}

/** 获取 App SQLite 数据库配置 */
function getDBConfig() {
	const userId = getCurrentUserId();
	const data = {
		name: `conversation_db_${userId}`,
		path: `_doc/conversation_db_${userId}.db`
	}
	// if (platform === 'android'){
	// 	data.path = `${plus.io.DOCUMENTS}/conversation_db_${userId}.db`
	// }else if (platform === 'ios'){
	// 	data.path = `${plus.io.LIBRARY}/conversation_db_${userId}.db`
	// }
	return data;
}

/** 获取小程序本地存储 key */
function getMiniAppStorageKey() {
	return `${storagePrefix}_${getCurrentUserId()}`;
}

/** 初始化 SQLite 表结构 */
function ensureAppTablesReady() {
	return new Promise((resolve, reject) => {
		const {
			name,
			path
		} = getDBConfig();
		if (plus.sqlite.isOpenDatabase({
				name,
				path
			})) return resolve();

		plus.sqlite.openDatabase({
			name,
			path,
			success: () => {
				plus.sqlite.executeSql({
					name,
					sql: `CREATE TABLE IF NOT EXISTS ${conversationTable} (
              no TEXT PRIMARY KEY,
              data TEXT
            )`,
					success: () => {
						plus.sqlite.executeSql({
							name,
							sql: `CREATE TABLE IF NOT EXISTS ${messageTable} (
                id TEXT PRIMARY KEY,
                conversationNo TEXT,
                data TEXT,
                sendTime TEXT
              )`,
							success: resolve,
							fail: reject
						});
					},
					fail: reject
				});
			},
			fail: reject
		});
	});
}

/** 初始化数据库 */
export async function initLocalChatDB() {
	if (getPlatform() === 'app') {
		await ensureAppTablesReady();
	}
}

/** 新增或更新会话记录 */
export async function saveLocalConversation(conversation) {
	try {
		if (getPlatform() === 'app') {
			await ensureAppTablesReady();
			const {
				name
			} = getDBConfig();
			const json = JSON.stringify(conversation);
			plus.sqlite.executeSql({
				name,
				sql: `INSERT OR REPLACE INTO ${conversationTable} (no, data) VALUES ('${conversation.no}', '${json}')`,
				success(e) {
					resolve(e);
					console.error("【========保存本地会话成功========")
				},
				fail(e) {
					console.error("【***********保存本地会话失败***********",e)
					console.error(
						`INSERT OR REPLACE INTO ${conversationTable} (no, data) VALUES ('${conversation.no}', '${json}')`,
						)
					console.error("***********保存本地会话失败***********】")
				}
			});
		} else {
			const key = getStorageKey(conversationTable)
			const list = uni.getStorageSync(key) || []
			const index = list.findIndex(item => item.no === conversation.no)
			if (index > -1) {
				list[index] = conversation
			} else {
				list.push(conversation)
			}
			uni.setStorageSync(key, list)
		}
	} catch (err) {
		console.error('[addConversation] error:', err);
	}
}

/** 获取单个会话记录 */
export async function getLocalConversationByNo(conversationNo) {
	try {
		if (getPlatform() === 'app') {
			await ensureAppTablesReady();
			const {
				name
			} = getDBConfig();
			return new Promise((resolve, reject) => {
				plus.sqlite.selectSql({
					name,
					sql: `SELECT data FROM ${conversationTable} WHERE no =  '${conversationNo}'`,
					success(res) {
						if (res.length > 0) resolve(JSON.parse(res[0].data));
						else resolve(undefined);
					},
					fail: reject
				});
			});
		} else {
			const key = getStorageKey(conversationTable)
			const list = uni.getStorageSync(key) || []
			return list.find(item => item.conversationNo === conversationNo)
		}
	} catch (err) {
		console.error('[getConversation] error:', err);
		return undefined;
	}
}

/** 获取全部会话 */
export async function getAllLocalConversations() {
	try {
		if (getPlatform() === 'app') {
			await ensureAppTablesReady();
			const {
				name
			} = getDBConfig();
			return new Promise((resolve, reject) => {
				plus.sqlite.selectSql({
					name,
					sql: `SELECT data FROM ${conversationTable}`,
					success(res) {
						resolve(res.map(row => JSON.parse(row.data)));
					},
					fail: reject
				});
			});
		} else {
			const key = getStorageKey(conversationTable)
			return uni.getStorageSync(key) || []
		}
	} catch (err) {
		console.error('[getAllConversations] error:', err);
		return [];
	}
}

/** 添加或更新单条消息记录 */
export async function saveLocalMessage(message) {
	try {
		if (getPlatform() === 'app') {
			await ensureAppTablesReady();
			const {
				name
			} = getDBConfig();
			const json = JSON.stringify(message);
			const sendTime = message.sendTime || Date.now();
			plus.sqlite.executeSql({
				name,
				sql: `INSERT OR REPLACE INTO ${messageTable} (id, conversationNo, data, sendTime) VALUES ('${message.clientMessageId || ''}', '${message.conversationNo}', '${json}', '${sendTime}')`,
			});
		} else {
			const key = getStorageKey(`${messageTable}_${message.conversationNo}`)
			const list = uni.getStorageSync(key) || []
			const index = list.findIndex(msg => msg.clientMessageId === message.clientMessageId)
			if (index > -1) {
				list[index] = message
			} else {
				list.push(message)
			}
			list.sort((a, b) => (a.sendTime || 0) - (b.sendTime || 0))
			uni.setStorageSync(key, list)
		}
	} catch (err) {
		console.error('[addMessage] error:', err);
	}
}

/** 获取指定会话的所有消息（按发送时间升序） */
export async function getLocalMessagesByConversationNo(conversationNo) {
	try {
		if (getPlatform() === 'app') {
			await ensureAppTablesReady();
			const {
				name
			} = getDBConfig();
			return new Promise((resolve, reject) => {
				plus.sqlite.selectSql({
					name,
					sql: `SELECT data FROM ${messageTable} WHERE conversationNo = '${conversationNo}' ORDER BY sendTime DESC`,
					success(res) {
						resolve(res.map(row => JSON.parse(row.data)));
					},
					fail: reject
				});
			});
		} else {
			const key = getStorageKey(`${messageTable}_${conversationNo}`)
			return uni.getStorageSync(key) || []
		}
	} catch (err) {
		console.error('[getMessagesByConversationNo] error:', err);
		return [];
	}
}