export default {
	MESSAGE_ROLE: {
		SELF: 1,
		SY<PERSON><PERSON>: 2,
		<PERSON><PERSON><PERSON>: 3
	},
	MESSAGE_SOURCE:{
		USER_SEND: 100,
		SYSTEM_SEND: 200,
	},
	SEND_STATUS: {
		FAILURE: 1,
		SENDING: 2,
		SUCCESS: 3
	},
	CONTENT_TYPE: {
		TEXT: 101,
		IMAGE: 102,
		AUDIO: 103,
		SYSTEM: 1400,
		FAQ_QUESTION: 1801,
		FAQ_OPTION: 1802,
		FAQ_ANSWER: 1803,
		FAQ_COMPLETE: 1804
	},
	CONVERSATION_TYPE: {
		SINGLE: 1,
		GROUP: 3,
		NOTIFICATION: 4
	},
	WEBSOCKET_MESSAGE_TYPE_ENUM: {
		IM_MESSAGE_RECEIVE: 'im-message-receive',
		IM_CONVERSATION_ADD: 'im-conversation-add'
	}
}