import chatConstants from '@/pagesChat/utils/constants.js';

/**
 * @desc 构造会话编号
 */
export const generateConversationNo = (id1, id2, conversationType) => {
	const SINGLE_PREFIX = 's_'
	const GROUP_PREFIX = 'g_'
	const [smallId, largeId] = id1 < id2 ? [id1, id2] : [id2, id1];

	if (conversationType === chatConstants.CONVERSATION_TYPE.SINGLE) {
		return SINGLE_PREFIX + smallId + "_" + largeId
	} else if (conversationType === chatConstants.CONVERSATION_TYPE.GROUP) {
		return GROUP_PREFIX + smallId + "_" + largeId
	}
	return ''
}