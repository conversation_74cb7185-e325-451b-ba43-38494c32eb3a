<script>
import store from '@/store';
import intercept from '@/common/intercept.js';
import { webSocketService } from '@/common/webscoket.js';
// #ifdef APP-PLUS
import { signVerify } from '@/common/safetyVerif.js';
import appUpdate from '@/common/appUpdate.js';
import aliyunPushHelper from '@/common/aliPush.js';
import { setupLogin } from '@/common/login.js';
// #endif
// #ifdef MP
import { mpUpdate, mpScene } from '@/common/applet.js';
// #endif
const dialog = require('@/components/dialog/dialogUtil');
export default {
	data() {
		return {
			// 计时器
			msgTimer: null,
			unregisterSystemHandler: null
		};
	},
	globalData: {
		// 首页点击的分类编号（包含一级和二级，格式：{firstId:'',secondId:''}）（首页点击分类跳转服务Tab）
		category: null,
		// 订单Tab页面tab索引（个人中心点击跳转订单TabBar）
		orderTabIndex: null
	},
	onLaunch: async function (e) {
		// #ifdef APP-PLUS
		plus.screen.lockOrientation('portrait-primary');
		// 签名校验
		// signVerify();
		// 苹果端默认为上架审核中，否则初次打开由于网络延迟造成UI提前显示
		// store.commit('setIOSAppUnderReview', plus.os.name === 'iOS');
		// #endif

		// 取出缓存数据
		store.commit('SET_CACHE_DATA');
		// 设置使用程序的唯一标识【每次重新打开app会改变】
		store.commit('SET_USE_IDENT', uni.$u.guid(32));
		// 全局弹窗组件
		uni['dialog'] = dialog;
		// 添加拦截器
		intercept();

		// #ifdef APP-PLUS
		// 初始化号码认证
		setupLogin();
		// 初始化推送
		aliyunPushHelper.init();

		// // 2. ??????
		this.unregisterSystemHandler = webSocketService.on('im-message-receive', (msg) => {
			// uni.showModal({
			// 	title: '????',
			// 	content: msg.content,
			// 	showCancel: false
			// });
		});

		// if (plus.os.name == 'iOS') {
		// 	if (!plus.runtime.isAgreePrivacy()) {
		// 		uni.reLaunch({
		// 			url: '/pages/welcome/ios-privacy',
		// 			success() {
		// 				plus.navigator.closeSplashscreen();
		// 			}
		// 		});
		// 	} else {
		// 		await this.delayCloseSplashscreen();
		// 	}
		// } else {
			await this.delayCloseSplashscreen();
		// }
		// #endif

		// 隐藏 TabBar
		uni.hideTabBar();
	},
	onShow: function (e) {
		// 解决与开微信的时候点击取消 loading 还存在问题
		uni.hideLoading();
		// #ifdef APP-PLUS
		// 清空通知角标【角标仅仅小米|华为设备有效】
		plus.runtime.setBadgeNumber(0);
		// appUpdate();
		// #endif

		// #ifdef MP
		mpScene(e); //解析场景参数
		mpUpdate(); //检测小程序更新
		// #endif

		// 轮询请求 消息红点
		// this.msgTimer = setInterval(() => {
		// 	if (store.getters.getterIsLogin) {
		// 		store.dispatch('updateBadge');
		// 	}
		// }, 10000); // 间隔10秒轮询一
		// // 登录状态重新拉取用户信息
		// if (store.getters.getterIsLogin) {
		// 	store.dispatch('fetchUser');
		// }
	},
	onHide: function () {
		// uni.hideLoading()
		console.log('APP进入后台');
		// 停止定时器
		this.msgTimer && clearInterval(this.msgTimer);
	},
	onError: function (error) {
		console.log('---------------------全局异常捕获---------------------');
		console.log(error);
		console.log('---------------------全局异常捕获---------------------');
	},
	onUnhandledRejection: function (e) {
		console.log('---------------------对未处理的 Promise 拒绝事件监听函数---------------------');
		console.log(JSON.stringify(e));
		console.log('---------------------对未处理的 Promise 拒绝事件监听函数---------------------');
	},
	//全局函数
	methods: {
		// 延时关闭启动界面
		async delayCloseSplashscreen() {
			const delayTime = 1000;
			await new Promise((resolve) => setTimeout(resolve, delayTime));
			plus.navigator.closeSplashscreen();
		}
	}
};
</script>

<style lang="scss">
/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
@import '@/uni_modules/uview-ui/index.scss';
@import './style/common.scss';
@import './style/animate.css';

/* #ifndef APP-NVUE */
page {
	background-color: #F3F3F5;
}

// 隐藏滚动条
::-webkit-scrollbar {
	width: 0;
	height: 0;
	color: transparent;
}

/* #endif */
</style>
