import Vue from 'vue'
import App from './App'
//数据管理中心
import store from '@/store'
Vue.prototype.$store = store;
//混入mixin
import iMixin from '@/mixin/mixin.js'
Vue.mixin(iMixin);
//数据配置中心
import conf from '@/common/config.js'
Vue.prototype.$conf = conf;
// 过滤器
import '@/common/filters.js'
//判断是否登录
import {
	judgeLogin
} from '@/common/login';
Vue.prototype.judgeLogin = judgeLogin;
// 判断路由跳转
import {
	onJump,
	onBack
} from '@/common/route.js';
Vue.prototype.onJump = onJump;
Vue.prototype.onBack = onBack;
// #ifdef MP-WEIXIN
import {
	myPatch
} from "@/common/extendWeixin.js"
Vue.prototype.__patch__ = myPatch;
// #endif

// uView
import uView from '@/uni_modules/uview-ui'
Vue.use(uView)
uni.$u.setConfig({
	// 修改$u.props对象的属性
	props: {
		// 修改radio组件的size参数的默认值，相当于执行 uni.$u.props.radio.size = 30
		navbar: {
			leftIconSize: 18,
			titleStyle: 'fontSize:33rpx;fontWeight:700'
		}
	}
})

// 公共组件
import publicModule from "@/components/public-module/public-module.vue";
Vue.component("public-module", publicModule);
import popupPrompt from "@/components/popup-prompt/popup-prompt.vue";
Vue.component("popup-prompt", popupPrompt);

// 线上环境去除日志
if (process.env.NODE_ENV === 'production') {
	console.log = () => {}
	console.error = () => {}
	console.warn = () => {}
}

Vue.config.productionTip = false;

App.mpType = 'app'

const app = new Vue({
	store,
	...App
})
// 引入请求封装
import '@/common/request.js';
app.$mount();