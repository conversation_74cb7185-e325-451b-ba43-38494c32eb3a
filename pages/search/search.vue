<template>
	<view>
		<public-module></public-module>
		<z-paging ref="paging" bgColor="#f7f7f7" :auto-clean-list-when-reload="false" :refresher-end-bounce-enabled="false" :auto="false" v-model="dataList" @query="queryList">
			<u-navbar slot="top" :fixed="true" :statusBar="true" :placeholder="true" :autoBack="true">
				<view slot="left">
					<u-icon name="close" size="43rpx"></u-icon>
				</view>
				<view slot="center" :style="{ width: '100%', marginLeft: '100rpx', marginRight: searchBarRightSpacing }">
					<u-search v-model="searchKeyword" placeholder="输入关键词进行搜索" bgColor="#FFFFFF" borderColor="#DBDBDB" focus :showAction="true" actionText="搜索" @search="onSearch" @custom="onSearch"></u-search>
				</view>
			</u-navbar>
			<custom-empty slot="empty"></custom-empty>
			<view class="flex-col wrap-list">
				<list-product-item class="wrap-list__item" :item="item" v-for="(item, index) in dataList" :key="index" @click="onListItemClick"></list-product-item>
			</view>
		</z-paging>
	</view>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
export default {
	data() {
		return {
			// 页面参数【其它页面传递】
			pageParams: {},
			// 搜索栏距离右边的间距 单位 px
			searchBarRightSpacing: '',
			// 搜索关键词
			searchKeyword: '',
			// 数据列表
			dataList: [],
			// 提示弹窗
			promptPopup: {
				show: false,
				ident: 'prompt',
				title: '温馨提示',
				message: '',
				tipText: '',
				actions: [],
				showClose: true
			}
		};
	},
	computed: {
		...mapState(['userInfo', 'selectedAddress']),
		...mapGetters(['getterIsLogin'])
	},
	onReady() {
		// #ifdef MP
		const res = uni.getMenuButtonBoundingClientRect();
		this.searchBarRightSpacing = uni.upx2px(30) + res.width + 'px';
		// #endif
		// #ifdef APP-PLUS
		this.searchBarRightSpacing = uni.upx2px(30) + 'px';
		// #endif
	},
	onLoad(options) {
		this.pageParams = options;
		this.searchKeyword = options.keyword || '';

		// this.$nextTick(() => {
		// 	this.$refs.paging.reload(true);
		// });
	},
	//方法
	methods: {
		onSearch(e) {
			this.$refs.paging.reload(true);
		},
		onListItemClick(item) {
			this.onJump({
				url: '/pagesHome/detail/service-detail',
				params: { id: item.detail.id }
			});
		},
		// 查询数据列表
		queryList(pageNo, pageSize) {
			const params = { page: pageNo, limit: pageSize, category_id: '0', keywords: encodeURIComponent(this.searchKeyword) };
			uni.$u.http
				.post('/category/getServiceList', params)
				.then((res) => {
					this.$refs.paging.complete(res.data);
				})
				.catch((err) => {
					this.$refs.paging.complete(false);
				});
		}
	}
};
</script>
<style>
page {
	background-color: #ffffff;
}
</style>
<style lang="scss" scoped>
.wrap-list {
	padding: 0 30rpx;
	&__item {
		// flex: 1;
		width: 690rpx; // 此处写不写690的话会导致列表item有点在滑动的时候会多余出来一点
		margin-top: 20rpx;
	}
}
</style>
