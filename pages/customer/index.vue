<template>
	<view>
		<public-module></public-module>
		<z-paging ref="paging" bgColor="linear-gradient(to bottom, #FFFFFF 2%, #F6F5F7 100%)" :refresher-out-rate="0.9"
			v-model="dataList" @query="getList" @onRefresh="onRefresh">
			<template #top>
				<!-- 顶部导航 -->
				<view class="flex-col">
					<view class="flex nav-wraper">
						<view class="flex nav-wraper__content"
							:style="{ height: `${navBarHeight}px`, marginTop: `${statusBarHeight}px` }">
							<view class="flex nav-wraper__content__left">
								<image :src="iImage('img_hall_title.png')"></image>
							</view>
							<view class="flex nav-wraper__content__right" @click.stop="onSelectAddress">
								<text>我的客源</text>
							</view>
						</view>
					</view>
					<!-- 服务分类 -->
					<view class="catgory-wraper">
						<scroll-view class="flex catgory-wraper__scroll" scroll-x>
							<view class="flex catgory-wraper__scroll__item" v-for="(item, index) in cateList"
								:key="index" @click="onCateItemClick(index)">
								<view class="flex-col catgory-wraper__scroll__item__content">
									<image class="catgory-wraper__scroll__item__content__icon" :src="item.mediaUrl"
										mode="aspectFill"></image>
									<text
										class="catgory-wraper__scroll__item__content__text">{{ item.displayName }}</text>
								</view>
								<view
									class="catgory-wraper__scroll__item__indicator animate__animated animate__bounceIn'"
									v-if="cateIndex == index"></view>
							</view>
						</scroll-view>
					</view>
					<!-- 新线索 -->
					<view class="flex new-wraper">
						<view class="flex new-wraper__prefix">新单</view>
						<u-notice-bar bg-color="transparent" color="#252B50" icon=""
							text="'uView UI众多组件覆盖开发过程的各个需求，组件功能丰富，多端兼容。让您快速集成，开箱即用" mode="link" speed="80"
							url="/pages/componentsB/tag/tag"></u-notice-bar>
					</view>
				</view>
			</template>

			<!-- 列表状态 -->
			<custom-empty slot="empty"></custom-empty>
			<custom-loading :status="loadingStatus"></custom-loading>
			<!-- 列表 -->
			<view class="flex wrap-list">
				<list-hall-demand-item class="wrap-list__item" :item="item" v-for="(item, index) in dataList"
					:key="index" @click="onListItemClick"></list-hall-demand-item>
			</view>
			<!-- 【底部TabBar】 -->
			<view slot="bottom">
				<x-tabbar ref="xTabbar"></x-tabbar>
			</view>
		</z-paging>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions,
		mapMutations
	} from 'vuex';
	export default {
		data() {
			return {
				// 小程序胶囊
				mpMenuRect: {},
				// 状态栏高度
				statusBarHeight: 0,
				// 导航栏高度
				navBarHeight: 44,

				// 分类
				cateList: [],
				cateIndex: -1,

				// 列表加载状态
				loadingStatus: 'LOADING',
				// 数据列表
				dataList: []
			};
		},
		computed: {
			...mapState(['selectedAddress', 'sysConf']),
			...mapGetters(['getterIsLogin'])
		},
		onLoad(options) {
			uni.hideTabBar();
			const sys = uni.getSystemInfoSync();
			this.statusBarHeight = sys.statusBarHeight;
			// #ifdef MP
			this.mpMenuRect = uni.getMenuButtonBoundingClientRect();
			// #endif
		},
		onShow() {
			this.getCategory();

			this.$refs.paging && this.$refs.paging.updateFixedLayout();
		},
		methods: {
			// 下拉刷新
			onRefresh() {
				this.getCategory();
			},

			// 服务分类点击
			onCateItemClick(index) {
				this.cateIndex = index;
				this.$refs.paging.reload(false);
			},

			// 列表条目点击
			onListItemClick(item) {
				this.onJump({
					url: '/pagesCustomer/customer/detail',
					params: {
						id: item.detail.id
					}
				});
			},
			// 查询分类
			getCategory() {
				uni.$u.http.get('/category/display/list-by-scene', {
					params: {
						displayScene: this.cons.CATEGORY_DISPLAY_SCENE_HALL
					}
				}).then((res) => {
					this.cateList = res;
				});
			},
			// 查询数据列表
			getList(pageNo, pageSize) { 
				const categoryGroupId = this.cateList?.[this.cateIndex]?.ownerId;
				uni.$u.http
					.get('/demand/hall/page', {
						params: {
							pageNo: pageNo,
							pageSize: pageSize,
							categoryGroupId: categoryGroupId,
							cityCode: ''
						}
					})
					.then((res) => {
						this.loadingStatus = 'SUCCESS';
						this.$refs.paging.complete(res.list);
					})
					.catch((err) => {
						this.loadingStatus = 'FAIL';
						this.$refs.paging.complete(false);
					});
			}
		}
	};
</script>

<style lang="scss" scoped>
	/* 导航栏 */
	.nav-wraper {
		z-index: 999;
		width: 750rpx;

		&__content {
			flex: 1;
			justify-content: space-between;

			&__left {
				height: 80rpx;
				padding: 0 30rpx;

				image {
					width: 220rpx;
					height: 44rpx;
				}
			}

			&__right {
				height: 80rpx;
				padding: 0 30rpx;
				justify-content: flex-end;

				text {
					font-weight: 500;
					font-size: 30rpx;
					color: #1e2134;
				}
			}
		}
	}

	/* 分类 */
	.catgory-wraper {
		width: 750rpx;
		padding: 10rpx 0;

		&__scroll {
			flex: 1;
			white-space: nowrap;

			&__item {
				display: inline-flex;
				position: relative;

				&__content {
					width: 160rpx;
					align-items: center;
					justify-content: center;

					&__icon {
						width: 96rpx;
						height: 96rpx;
					}

					&__text {
						line-height: 60rpx;
						font-size: 28rpx;
						color: #252b50;
						white-space: nowrap;
					}
				}

				&__indicator {
					width: 80rpx;
					height: 16rpx;
					background: linear-gradient(to top, #f54f67 0%, transparent 100%);
					position: absolute;
					bottom: 20rpx;
					left: 50%;
					transform: translateX(-50%);
				}
			}
		}
	}

	/* 跑马灯 */
	.new-wraper {
		flex: 1;
		margin: 0 30rpx;
		height: 80rpx;
		background: #fff8ef;
		border-radius: 10rpx;
		border: 1px solid #f59320;

		&__prefix {
			width: 66rpx;
			height: 46rpx;
			margin-left: 15rpx;
			background-color: #f59320;
			justify-content: center;
			border-radius: 4rpx;
			font-weight: 500;
			font-size: 24rpx;
			color: #ffffff;
		}
	}

	/* 列表 */
	.wrap-list {
		padding: 0 30rpx;
		flex-direction: column;

		&__item {
			width: 100%;
			margin-top: 20rpx;
		}
	}
</style>