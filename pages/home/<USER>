<template>
	<view>
		<public-module></public-module>
		<z-paging
			ref="paging"
			bgColor="transparent"
			:refresher-out-rate="0.9"
			refresher-theme-style="white"
			:refresher-title-style="{ color: 'white' }"
			refresher-fixed-bac-height="200rpx"
			refresher-background="#444BF1"
			refresher-fixed-background="#444BF1"
			v-model="dataList"
			@query="getList"
			@onRefresh="onRefresh"
		>
			<!-- 顶部导航 -->
			<view slot="top" class="flex nav-wraper">
				<!-- 吸顶展示 -->
				<view class="flex nav-wraper__content" :style="{ height: `${navBarHeight}px`, marginTop: `${statusBarHeight}px` }">
					<view class="flex nav-wraper__content__left">
						<u-tabs
							:current="tabIndex"
							:list="tabs"
							lineWidth="50rpx"
							lineHeight="10rpx"
							lineColor="url(/static/image/img_home_tab_indicator.png) 100% 100%"
							:activeStyle="{ color: '#FFFFFF', fontSize: '36rpx', fontWeight: '800', transform: 'scale(1.05)' }"
							:inactiveStyle="{ color: '#BEC0F7', fontSize: '36rpx', ransform: 'scale(1)' }"
							itemStyle="padding:0 30rpx;height:80rpx;text-align:center;"
							@click="onTabClick"
						></u-tabs>
					</view>
					<view class="flex nav-wraper__content__right" @click.stop="onSelectAddress">
						<text class="nav-wraper__content__right__text line1">{{ addressName }}</text>
						<u-icon name="arrow-down-fill" color="#FFFFFF" size="22rpx"></u-icon>
					</view>
				</view>
			</view>
			<!-- 搜索 -->
			<view class="flex search-wraper">
				<view class="flex search-wraper__box" @click="onSearchGoods">
					<image class="search-wraper__box__icon" src="/static/image/ic_search.png" mode="aspectFit" />
					<input
						class="search-wraper__box__input"
						:style="{ fontSize: '30rpx' }"
						type="text"
						placeholder="搜索您想要的服务"
						cursor="30"
						disabled
						v-model="searchWord"
						readonly="readonly"
						confirm-type="search"
						@confirm="onSearchGoods"
					/>
				</view>
			</view>
			<!-- 服务分类 -->
			<view class="flex-col catgory-wraper">
				<scroll-view class="flex catgory-wraper__scroll" scroll-x>
					<view class="flex catgory-wraper__scroll__item" v-for="(item, index) in cateList" :key="index" @click="onCateItemClick(index)">
						<image class="catgory-wraper__scroll__item__active-bg" :src="iImage('img_home_cate_active.png')" v-if="cateIndex == index"></image>
						<view class="flex-col catgory-wraper__scroll__item__content" :class="[cateIndex == index ? 'animate__animated animate__bounceIn' : 'animate__animated animate__pulse']">
							<image class="catgory-wraper__scroll__item__content__icon" :src="item.mediaUrl" mode="aspectFill"></image>
							<text class="catgory-wraper__scroll__item__content__text">{{ item.displayName }}</text>
						</view>
						<view class="catgory-wraper__scroll__item__indicator animate__animated animate__lightSpeedInLeft" v-if="cateIndex == index"></view>
					</view>
				</scroll-view>
				<view class="flex catgory-wraper__second" v-if="secondCateList.length > 0">
					<view class="flex-col catgory-wraper__second__item" v-for="(item, index) in secondCateList" :key="index" @click="onSecondCateItemClick(item)">
						<image class="catgory-wraper__second__item__icon" :src="item.mediaUrl" mode="aspectFit"></image>
						<text class="catgory-wraper__second__item__text">{{ item.displayName }}</text>
					</view>
				</view>
			</view>
			<!-- 公司起名 -->
			<view class="flex naming-wraper">
				<!-- <uni-swiper-dot :info="[{}, {}]" :current="namingSwiperIndex" mode="round"> -->
				<swiper
					class="naming-wraper__swiper"
					circular
					:autoplay="true"
					:interval="3000"
					@change="
						(e) => {
							namingSwiperIndex = e.detail.current;
						}
					"
				>
					<swiper-item @click="onCompanyNamingItemlick">
						<image src="/static/image/img_naming_test.png" mode="aspectFill" />
					</swiper-item>
					<swiper-item @click="onCompanyNamingItemlick">
						<image src="/static/image/img_naming_test.png" mode="aspectFill" />
					</swiper-item>
				</swiper>
				<!-- </uni-swiper-dot> -->
			</view>

			<!-- 列表状态 -->
			<custom-empty slot="empty"></custom-empty>
			<custom-loading :status="loadingStatus"></custom-loading>
			<!-- 产品列表 -->
			<view class="flex wrap-list" v-if="tabs[tabIndex].id == 1">
				<list-product-item class="wrap-list__item" :item="item" v-for="(item, index) in dataList" :key="index" @click="onProductListItemClick"></list-product-item>
			</view>
			<!-- 服务商列表 -->
			<view class="flex wrap-list" v-if="tabs[tabIndex].id == 2">
				<list-provider-item class="wrap-list__item" :item="item" v-for="(item, index) in dataList" :key="index" @click="onProviderListItemClick"></list-provider-item>
			</view>
			<!-- 【底部TabBar】 -->
			<view slot="bottom">
				<x-tabbar ref="xTabbar"></x-tabbar>
			</view>
		</z-paging>
	</view>
</template>

<script>
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';
import { getLocationByIp } from '@/common/location.js';
import { checkOpenGPS, openGPS } from '@/common/sysPermission.js';
import { parseUrlAndParams } from '@/common/utils.js';
import { generateConversationNo } from '@/pagesChat/utils/tool.js';
import { handleTree } from '@/common/tool.js';
export default {
	data() {
		return {
			// 小程序胶囊
			mpMenuRect: {},
			// 状态栏高度
			statusBarHeight: 0,
			// 导航栏高度
			navBarHeight: 44,
			// 导航栏透明度
			navOpacity: 1,
			// 导航栏内容透明度
			navConOpacity: 1,
			// 是否提示引导打开定位(1、APP没有打开设备的GPS提示，2、小程序没有授权位置提示)
			locPrompt: false,
			closeLocTip: false,
			// 检索输入关键词
			searchWord: '',
			// 分类
			cateList: [],
			cateIndex: -1,
			// 推荐分类
			recommendCateList: [],
			// 起名轮播
			namingSwiperIndex: 0,
			// 列表加载状态
			loadingStatus: 'LOADING',
			// 数据列表
			dataList: [],
			// Tabs分类
			tabs: [
				{
					id: 1,
					name: '本地业务',
					url: '/product/spu/page'
				},
				{
					id: 2,
					name: '本地商家',
					url: '/provider/user/page'
				}
			],
			tabIndex: 0
		};
	},
	computed: {
		...mapState(['currentAddress', 'selectedAddress', 'userInfo', 'sysConf', 'badgeData']),
		...mapGetters(['getterIsLogin']),
		addressName() {
			return this.selectedAddress.cityName || '选择城市';
		},
		// 二级分类列表（点击一级分类选中的和默认推荐的二级分类）
		secondCateList() {
			if (this.cateIndex >= 0) {
				return this.cateList[this.cateIndex].children || [];
			}
			return this.recommendCateList;
		}
	},
	onLoad(options) {
		uni.hideTabBar();
		const sys = uni.getSystemInfoSync();
		this.statusBarHeight = sys.statusBarHeight;
		// #ifdef MP
		this.mpMenuRect = uni.getMenuButtonBoundingClientRect();
		// #endif

		// // #ifdef APP-PLUS
		// // 解决IOS初次安装首先进入此页面再加载隐私页面而导致会进行网络请求
		// if (!plus.runtime.isAgreePrivacy()) {
		// 	return;
		// }
		// // #endif

		// 初始化数据
		this.getPageData();

		this.fetchLocationInfo();
	},
	onShow() {
		// // #ifdef APP-PLUS
		// // 解决IOS初次安装首先进入此页面再加载隐私页面而导致会进行网络请求
		// if (!plus.runtime.isAgreePrivacy()) {
		// 	return;
		// }
		// // #endif
		console.error('----', this.userInfo);
		this.fetchUser();
		this.fetchSysConf();
		// this.getPageData();
		// this.updateUserProfile();

		this.$refs.paging && this.$refs.paging.updateFixedLayout();
	},
	methods: {
		...mapActions(['fetchUser', 'fetchSysConf', 'updateUserProfile']),
		// 滚动检测
		onScroll(e) {
			const maxDistance = this.navBarHeight + 20;
			this.navConOpacity = 1 - Math.min(e.detail.scrollTop / maxDistance, 1);
			console.log(this.navConOpacity);
		},
		// 下拉刷新
		onRefresh() {
			this.getPageData();
		},
		// 获取定位信息
		fetchLocationInfo() {
			getLocationByIp((_) => {
				this.$refs.paging.reload();
			});
		},
		// 选择地址
		async onSelectAddress() {
			this.onJump({
				url: '/pages/home/<USER>',
				events: {
					didSelectedAddress: (res) => {
						this.loadingStatus = 'LOADING';
						this.$refs.paging.reload();
					}
				}
			});
		},
		// 搜索
		onSearchGoods(e) {
			this.onJump({
				url: '/pages/search/search',
				params: {
					keyword: e.detail.value
				}
			});
		},
		// 切换分类
		onTabClick(tab) {
			this.tabIndex = tab.index;
			this.loadingStatus = 'LOADING';
			this.$refs.paging.reload();
		},
		// 服务分类点击
		onCateItemClick(index) {
			if (this.cateIndex == index) {
				this.cateIndex = -1;
			} else {
				this.cateIndex = index;
			}

			this.$refs.paging.reload();
		},
		// 服务分类点击【二级分类】
		onSecondCateItemClick(item) {
			this.onJump({
				url: '/pagesHome/list/index',
				params: {
					ownerType: item.ownerType,
					ownerId: item.ownerId,
					parentOwnerId: item.parentOwnerId
				}
			});
		},
		/**
		 * 公司起名
		 */
		onCompanyNamingItemlick() {
			this.judgeLogin(() => {});
		},
		// 产品列表条目点击
		onProductListItemClick(item) {
			this.onJump({
				url: '/pagesHome/product/detail',
				params: {
					id: item.detail.id
				}
			});
		},
		// 服务商列表条目点击
		onProviderListItemClick(item) {
			this.onJump({
				url: '/pagesHome/provider/detail',
				params: {
					id: item.detail.id
				}
			});
		},
		// 查询主页数据
		getPageData() {
			uni.$u.http
				.get('/category/display/list-by-scene', {
					params: {
						displayScene: this.cons.CATEGORY_DISPLAY_SCENE_HOME
					}
				})
				.then((res) => {
					this.cateList = handleTree(res, 'ownerId', 'parentOwnerId');
				});

			uni.$u.http
				.get('/category/display/list-by-scene', {
					params: {
						displayScene: this.cons.CATEGORY_DISPLAY_SCENE_HOME_RECOMMEND
					}
				})
				.then((res) => {
					this.recommendCateList = res;
				});
		},
		// 查询数据列表
		getList(pageNo, pageSize) {
			const reqUrl = this.tabs[this.tabIndex || 0].url;
			// TODO 此处默认使用的是普通分类id，ownerType有普通分类和聚合分类区分
			const serviceCategoryId = this.cateIndex >= 0 ? this.cateList?.[this.cateIndex]?.ownerId || '' : '';
			const reqParams = {
				pageNo: pageNo,
				pageSize: pageSize,
				serviceCategoryId: serviceCategoryId,
				cityCode: this.selectedAddress?.cityId || ''
			};
			uni.$u.http
				.get(reqUrl, {
					params: reqParams
				})
				.then((res) => {
					this.loadingStatus = 'SUCCESS';
					this.$refs.paging.complete(res.list);
				})
				.catch((err) => {
					this.loadingStatus = 'FAIL';
					this.$refs.paging.complete(false);
				});
		}
	}
};
</script>

<style lang="scss" scoped>
/* 导航栏 */
.nav-wraper {
	z-index: 999;
	width: 750rpx;
	background-color: #444bf1;

	&__content {
		flex: 1;
		justify-content: space-between;

		&__left {
			width: 220rpx;
			max-width: 220rpx;
			height: 80rpx;
			// padding: 0 30rpx;
		}

		&__right {
			width: 220rpx;
			height: 80rpx;
			padding: 0 30rpx;
			justify-content: flex-end;

			&__text {
				font-weight: bold;
				font-size: 30rpx;
				color: #ffffff;
			}
		}

		&__search {
			flex: 1;
			height: 65rpx;
			padding: 0 30rpx;
			background: #ffffff;
			box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(205, 205, 205, 0.36);
			border-radius: 40rpx;

			&__icon {
				width: 36rpx;
				height: 36rpx;
			}

			&__input {
				flex: 1;
				margin-left: 10rpx;
				font-size: 26rpx;
				color: #b3b2b2;
			}
		}
	}
}

/* 搜索框 */
.search-wraper {
	width: 750rpx;
	padding: 20rpx 30rpx;
	background-color: #444bf1;
	justify-content: center;

	&__box {
		flex: 1;
		height: 80rpx;
		padding: 0 30rpx;
		background-color: #ffffff;
		border-radius: 10rpx;

		&__icon {
			width: 36rpx;
			height: 36rpx;
		}

		&__input {
			flex: 1;
			margin-left: 10rpx;
			font-size: 26rpx;
			color: #b3b2b2;
		}
	}
}

/* 分类 */
.catgory-wraper {
	flex: 1;

	&__scroll {
		flex: 1;
		padding: 0 5rpx;
		background-color: #444bf1;
		white-space: nowrap;
		position: relative;

		&__item {
			padding: 10rpx 0;
			display: inline-flex;
			position: relative;
			--animate-duration: 0.3s;

			&__active-bg {
				width: 142rpx;
				height: 100%;
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
			}

			&__content {
				width: 160rpx;
				align-items: center;
				justify-content: center;
				position: relative;

				&__icon {
					width: 96rpx;
					height: 96rpx;
				}

				&__text {
					line-height: 60rpx;
					font-size: 28rpx;
					color: #ffffff;
					white-space: nowrap;
				}
			}

			&__indicator {
				z-index: 5;
				width: 0;
				height: 0;
				border-left: 15rpx solid transparent;
				border-right: 15rpx solid transparent;
				border-bottom: 15rpx solid #ffffff;
				position: absolute;
				left: 50%;
				transform: translateX(-50%);
				bottom: 0;
			}
		}
	}

	&__second {
		padding: 30rpx;
		background-color: #ffffff;
		justify-content: space-between;
		flex-wrap: wrap;

		&__item {
			width: 160rpx;
			margin-bottom: 20rpx;
			align-items: center;
			justify-content: center;

			// 第45个及之后的 item 底边距为0
			&:nth-child(n + 5) {
				margin-bottom: 0;
			}

			&__icon {
				width: 60rpx;
				height: 60rpx;
			}

			&__text {
				margin-top: 15rpx;
				font-weight: 400;
				font-size: 28rpx;
				color: #1e2134;
				white-space: nowrap;
			}
		}
	}
}

/* 起名 */
.naming-wraper {
	width: 750rpx;
	padding: 15rpx 30rpx;
	background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);

	&__swiper {
		flex: 1;
		height: 162rpx;

		swiper-item image {
			width: 100%;
			height: 100%;
		}
	}
}

/* 列表 */
.wrap-list {
	padding: 0 30rpx;
	background-color: #f7f7f7;
	flex-direction: column;

	&__item {
		width: 100%;
		margin-top: 20rpx;
	}
}
</style>