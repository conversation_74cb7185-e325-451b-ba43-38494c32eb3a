<template>
	<view>
		<public-module></public-module>
		<u-navbar title="城市列表" :titleStyle="{ fontSize: '36rpx' }" :fixed="true" :placeholder="true" :autoBack="true">
			<view slot="left">
				<u-icon name="close" width="100rpx" size="40rpx"></u-icon>
			</view>
			<view slot="center" :style="{ width: '100%', marginLeft: '100rpx', marginRight: searchBarRightSpacing }">
				<u-search v-model="searchText" placeholder="输入城市名" bgColor="#F7F7F7" borderColor="#F7F7F7" :showAction="true" actionText="搜索" :animation="true" @change="textChange"></u-search>
			</view>
		</u-navbar>

		<!-- 城市列表 -->
		<!-- <u-index-list :indexList="indexList"> -->
		<!-- 使用了自己的:indexList="indexList"会导致有时候点击的索引列表错位 -->
		<u-index-list :customNavHeight="customNavHeight" :indexList="indexList">
			<view slot="header">
				<!-- 当前定位城市 -->
				<view class="flex loc">
					<image class="loc__ic" src="/static/image/ic_address.png" mode="aspectFit"></image>
					<text class="loc__name">当前城市</text>
					<text class="loc__value" @click="onSelectedCity(currentAddress)">{{ currentCity }}</text>
				</view>
				<!-- 历史访问城市 -->
				<view class="flex grid" v-if="!$u.test.isEmpty(historyCitys)">
					<view class="grid__title">历史访问城市</view>
					<view class="flex grid__content">
						<view class="flex grid__content__city" v-for="(item, index) in historyCitys" :key="index" @click="onSelectedCity(item)">
							<text class="line1">{{ item.cityName }}</text>
						</view>
					</view>
				</view>
				<!-- 热门城市 -->
				<view class="flex grid" v-if="!$u.test.isEmpty(hotCitys)">
					<view class="grid__title">热门城市</view>
					<view class="flex grid__content">
						<view class="flex grid__content__city" v-for="(item, index) in hotCitys" :key="index" @click="onSelectedCity(item)">
							<text class="line1">{{ item.title }}</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 城市列表 -->
			<template v-for="(group, groupIndex) in cityList">
				<u-index-item :key="groupIndex">
					<u-index-anchor :text="indexList[groupIndex]" bgColor="#f7f7f7" height="60rpx"></u-index-anchor>
					<view class="list-item" v-for="(item, index) in group" :key="index" @click="onSelectedCity(item)">
						<text class="list-item__name">{{ item.title }}</text>
						<u-line color="#EFEFEF"></u-line>
					</view>
				</u-index-item>
			</template>
		</u-index-list>

		<!-- 搜索结果 -->
		<view class="wrap-search" v-if="$u.test.isEmpty(searchText) == false">
			<z-paging :fixed="false">
				<view slot="top" class="wrap-search__title">
					<u-status-bar></u-status-bar>
					搜索结果
				</view>
				<view class="list-item" v-for="(item, index) in searchResults" :key="index" @click="onSelectedCity(item)">
					<text class="list-item__name">{{ item.title }}</text>
					<u-line color="#EFEFEF"></u-line>
				</view>
			</z-paging>
		</view>
	</view>
</template>

<script>
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';
import { getLocationByIp } from '@/common/location.js';
export default {
	data() {
		return {
			// 搜索栏距离右边的间距 单位 px
			searchBarRightSpacing: '100px',
			// 自定义导航栏高度
			customNavHeight: 0,
			// 字母索引
			indexList: [],
			// 城市列表
			cityList: [],
			// 热门城市
			hotCitys: [],
			// 历史城市
			historyCitys: [],
			// 搜索关键词
			searchText: ''
		};
	},
	computed: {
		...mapState(['currentAddress']),
		currentCity() {
			return this.currentAddress.cityName || '定位中';
		},
		searchResults() {
			var results = [];
			if (!uni.$u.test.isEmpty(this.searchText)) {
				let newAry = this.cityList.reduce((arr1, arr2) => arr1.concat(arr2));
				newAry.forEach(function (item) {
					if (item.title.indexOf(this.searchText) !== -1) {
						results.push(item);
					}
				});
			}
			return results;
		}
	},
	onLoad() {
		// #ifdef MP
		const res = uni.getMenuButtonBoundingClientRect();
		this.searchBarRightSpacing = uni.upx2px(30) + res.width + 'px';
		// #endif
		// #ifdef APP-PLUS
		this.searchBarRightSpacing = uni.upx2px(30) + 'px';
		// #endif
		const sys = uni.getSystemInfoSync();
		this.customNavHeight = 44 + sys.statusBarHeight;

		if (uni.$u.test.isEmpty(this.currentAddress?.cityName)) {
			getLocationByIp();
		}
		// 判断本地是否存在缓存的城市信息
		this.queryCitys();
		// 查询历史选择的城市
		this.queryHistory();
	},
	//方法
	methods: {
		...mapMutations(['SET_SELECTED_ADDRESS']),
		// 输入内容发生改变
		textChange(e) {
			this.searchText = e;
		},
		// 将选择的城市格式化为同一结构
		formatCity(e) {
			const res = {
				provinceName: '',
				provinceId: '',
				cityName: '',
				cityId: '',
				districtName: '',
				districtId: ''
			};
			// 定义键名映射关系
			const mappings = {
				provinceName: ['province_name', 'provinceName'],
				provinceId: ['province_code', 'provinceId'],
				cityName: ['title', 'cityName'],
				cityId: ['city_code', 'cityId'],
				districtName: ['area_name', 'areaName'],
				districtId: ['area_code', 'areaId'],
				name: ['name'],
				lat: ['lat'],
				lng: ['lng']
			};

			// 遍历映射关系，根据实际存在的键进行赋值
			for (const [resKey, possibleKeys] of Object.entries(mappings)) {
				for (const key of possibleKeys) {
					if (key in e) {
						res[resKey] = e[key];
						break;
					}
				}
			}
			return res;
		},
		// 选择城市的回调
		onSelectedCity(e) {
			if (uni.$u.test.isEmpty(e)) {
				uni.$u.toast('未查询到选择的城市');
				return;
			}
			let res = this.formatCity(e);
			this.selectFinish(res);
		},
		selectFinish(e) {
			this.recordHistory(e);
			this.getOpenerEventChannel().emit('didSelectedCity', e);
			this.SET_SELECTED_ADDRESS(e);
			this.onBack();
		},
		// 渲染城市列表
		displayCity(res) {
			// 热门城市
			this.hotCitys = res.hotCity || [];
			// 所有城市
			const indexs = [];
			const citys = [];
			(res.cityList || []).forEach(function (item) {
				indexs.push(item.letter);
				citys.push(item.city);
			});
			this.indexList = indexs || '#';
			this.cityList = citys || [];
		},
		queryCitys() {
			// const _that = this
			uni.getStorage({
				key: 'all-citys',
				success: (res) => {
					if (uni.$u.test.isEmpty(res.data)) {
						// 查询城市列表
						this.fetchCitys();
					} else {
						// 加载本地缓存
						this.displayCity(res.data);
					}
				},
				fail: (e) => {
					this.fetchCitys();
				}
			});
		},
		recordHistory(item) {
			// 查找城市是否已经存在
			const findIndex = this.historyCitys.findIndex((element) => element.cityId === item.cityId);
			if (findIndex !== -1) {
				// 如果城市存在，将其移动到数组开头
				const [element] = this.historyCitys.splice(findIndex, 1);
				this.historyCitys.unshift(element);
			} else {
				// 如果城市不存在，将其添加到数组开头
				this.historyCitys.unshift(item);
				// 如果数组长度超过6个，删除最后一个元素
				if (this.historyCitys.length > 6) {
					this.historyCitys.pop();
				}
			}
			// 更新本地存储
			uni.setStorage({
				key: 'history-citys',
				data: this.historyCitys
			});
		},
		queryHistory() {
			uni.getStorage({
				key: 'history-citys',
				success: (res) => {
					this.historyCitys = res.data || [];
					console.log('历史访问的城市', res);
				},
				fail: (e) => {
					console.log('查询历史选择的城市：失败', e);
				}
			});
		},
		// 从服务器拉取城市列表
		fetchCitys() {
			uni.$u.http
				.post('/common/getCityList')
				.then((res) => {
					// 城市信息存储在本地
					uni.setStorage({
						key: 'all-citys',
						data: res
					});
					this.displayCity(res);
				})
				.catch((err) => {});
		}
	}
};
</script>
<style>
page {
	background: #ffffff;
}
</style>
<style lang="scss" scoped>
.loc {
	padding: 30rpx 30rpx;
	&__ic {
		width: 30rpx;
		height: 30rpx;
	}
	&__name {
		margin: 0 10rpx;
		font-size: 30rpx;
		color: #333333;
	}
	&__value {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
	}
}

.grid {
	width: 100%;
	align-items: flex-start;
	flex-direction: column;
	&__title {
		width: 100%;
		padding: 20rpx 30rpx;
		background-color: #f7f7f7;
		font-size: 30rpx;
	}
	&__content {
		padding: 15rpx 40rpx 15rpx 30rpx;
		align-content: flex-end;
		flex-wrap: wrap;
		&__city {
			width: 200rpx;
			height: 60rpx;
			margin: 10rpx 40rpx 10rpx 0;
			padding: 0 15rpx;
			border: 0.7px solid #cccccc;
			border-radius: 6rpx;
			justify-content: center;
			&:nth-child(3n) {
				margin-right: 0;
			}
		}
	}
}

/* 搜索结果 */
.wrap-search {
	z-index: 10;
	background: #ffffff;
	position: fixed;
	top: 88rpx;
	left: 0;
	right: 0;
	bottom: 0;
	&__title {
		padding: 30rpx 30rpx 20rpx 30rpx;
		color: #000000;
		font-weight: 500;
		font-size: 30rpx;
	}
}

/* 城市列表 */
.list-item {
	width: 100%;
	padding: 0 30rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: flex-start;
}

.list-item__name {
	height: 100rpx;
	line-height: 100rpx;
	font-size: 30rpx;
	color: #333333;
	text-align: center;
}
</style>
