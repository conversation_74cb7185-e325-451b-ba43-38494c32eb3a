<template>
	<view>
		<z-paging ref="paging" bgColor="#f5f5f5" :auto="false" :auto-hide-loading-after-first-loaded="false" :refresher-enabled="false" v-model="nearbyList" @query="queryAddress">
			<view slot="top">
				<u-navbar bgColor="#FFFFFF" title="选择地址" :statusBar="true" :placeholder="true" :autoBack="true" />
				<view class="flex wrap-search">
					<view class="flex wrap-search__left" @click.stop="onSelectCity">
						<text class="wrap-search__left__text line1">{{ address.name || '定位中' }}</text>
						<u-icon name="arrow-down" color="#757575" size="22rpx"></u-icon>
					</view>
					<view class="wrap-search__input">
						<u-search
							v-model="searchValue"
							placeholder="搜索小区名/大厦名"
							bgColor="#F7F7F7"
							borderColor="#F7F7F7"
							:showAction="true"
							actionText="搜索"
							:clearabled="true"
							:animation="true"
							@change="onSearchTextChange"
							@search="onStartSearch"
							@custom="onStartSearch"
						></u-search>
					</view>
				</view>
			</view>
			<!-- <map style="width: 100%; height: 300rpx"></map> -->
			<view class="flex loading" slot="loading">
				<view class="loading__pulse-bubble loading__pulse-bubble-1"></view>
				<view class="loading__pulse-bubble loading__pulse-bubble-2"></view>
				<view class="loading__pulse-bubble loading__pulse-bubble-3"></view>
			</view>

			<z-paging-cell class="flex list-section">
				<view class="flex list-section__header" v-if="!$u.test.isEmpty(commonList)">
					<text class="list-section__header__name">常用地址</text>
				</view>
				<view class="flex list-section__item1 animate__animated animate__zoomIn animate__faster" v-for="(item, index) in commonList" :key="index">
					<view class="flex list-section__item1__wrap">
						<image class="list-section__item1__wrap__ic" src="/pagesMine/static/ic_address_loc.png" mode="aspectFill"></image>
						<text class="list-section__item1__wrap__name">王女士 15245625666</text>
					</view>
					<text class="list-section__item1__detail">银川 兴庆区中房-富力城-A座 #203号</text>
				</view>
			</z-paging-cell>
			<z-paging-cell class="flex list-section">
				<view class="flex list-section__header" v-if="!$u.test.isEmpty(nearbyList)">
					<text class="list-section__header__name">附近地址</text>
				</view>
				<view class="flex list-section__item2" v-for="(item, index) in nearbyList" :key="index" @click="onNearbyListItemClick(item)">
					<view class="flex list-section__item2__wrap">
						<image class="list-section__item2__wrap__ic" src="/pagesMine/static/ic_address_loc.png" mode="aspectFill"></image>
						<text class="list-section__item2__wrap__mark" v-if="parseFloat(item.distance) == 0">[当前位置]</text>
						<text class="list-section__item2__wrap__name">{{ item.name }}</text>
					</view>
					<view class="flex list-section__item2__detail">
						<text class="list-section__item2__detail__text line1">{{ item.address }}</text>
						<text class="list-section__item2__detail__distance" v-if="parseFloat(item.distance) > 0">{{ parseFloat(item.distance) }}m</text>
					</view>
				</view>
			</z-paging-cell>
		</z-paging>
	</view>
</template>

<script>
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';
import { GPSLocation, placeSearch, placeAround } from '@/common/location.js';
export default {
	data() {
		return {
			address: {},
			searchValue: '',
			commonList: [],
			nearbyList: []
		};
	},
	onLoad() {
		GPSLocation().then((res) => {
			this.address = res;
			this.$refs.paging.reload();
		});
	},
	methods: {
		...mapMutations(['SET_SELECTED_ADDRESS']),
		onSelectCity() {
			this.onJump({
				url: '/pages/home/<USER>',
				animationType: 'slide-in-bottom',
				// animationType: 'fade-in',
				events: {
					didSelectedCity: (res) => {
						this.address = res;
						this.$refs.paging.reload();
					}
				}
			});
		},
		onSearchTextChange(e) {},
		onStartSearch() {
			this.$refs.paging.reload();
		},
		// 将选择的地址格式化为同一结构
		formatAddress(e) {
			const res = {
				provinceId: '',
				provinceName: '',
				cityId: '',
				cityName: '',
				districtId: '',
				districtName: '',
				address: '',
				name: '',
				distance: '',
				lat: '',
				lng: ''
			};
			// 定义键名映射关系
			const mappings = {
				provinceName: ['province_name', 'provinceName'],
				provinceId: ['province_code', 'provinceId'],
				cityName: ['title', 'cityName'],
				cityId: ['city_code', 'cityId'],
				districtName: ['area_code', 'areaId'],
				districtId: ['area_code', 'areaId'],
				address: ['address'],
				name: ['name'],
				distance: ['distance'],
				lat: ['lat'],
				lng: ['lng']
			};
			// 遍历映射关系，根据实际存在的键进行赋值
			for (const [resKey, possibleKeys] of Object.entries(mappings)) {
				for (const key of possibleKeys) {
					if (key in e) {
						res[resKey] = e[key];
						break;
					}
				}
			}
			return res;
		},
		onCommonListItemClick(item) {
			const res = this.formatAddress(item);
			this.getOpenerEventChannel().emit('didSelectedAddress', res);
			this.SET_SELECTED_ADDRESS(res);
			this.onBack();
		},
		onNearbyListItemClick(item) {
			const res = this.formatAddress(item);
			this.getOpenerEventChannel().emit('didSelectedAddress', res);
			this.SET_SELECTED_ADDRESS(res);
			this.onBack();
		},
		queryAddress(pageNo, pageSize) {
			// uni.$u.http.post('/api/v2.order/getList', {
			// 	page: pageNo,
			// 	limit: pageSize,
			// }).then(res => {
			// 	this.$refs.paging.complete([{}, {}, {}, {}, {}])
			// }).catch(err => {
			// 	this.$refs.paging.complete(false);
			// })
			// let params = {
			// 	keywords: '中房富力城',
			// 	city_limit: true,
			// 	city: '640104',
			// 	pageSize: pageSize,
			// 	pageNum: pageNo
			// };
			// placeSearch(params).then((res) => {
			// 	this.$refs.paging.complete(res);
			// 	this.commonList = [{}];
			// });
			let params = {
				lng: this.address.lng,
				lat: this.address.lat,
				pageSize: pageSize,
				pageNum: pageNo
			};
			placeAround(params).then((res) => {
				this.$refs.paging.complete(res);
				this.commonList = [{}];
			});
		}
	}
};
</script>
<style>
page {
	background-color: #f5f5f5;
}
</style>
<style lang="scss" scoped>
.wrap-search {
	width: 750rpx;
	height: 100rpx;
	background: #ffffff;
	&__left {
		max-width: 200rpx;
		height: 100%;
		padding: 0 30rpx;

		&__text {
			margin-right: 5rpx;
			font-weight: 500;
			font-size: 30rpx;
			color: #333333;
		}
	}
	&__input {
		flex: 1;
		margin-right: 30rpx;
	}
}

.list-section {
	width: 750rpx;
	flex-direction: column;
	&__header {
		width: 750rpx;
		padding: 30rpx;
		background-color: #f5f5f5;
		align-items: flex-start;
		&__name {
			font-weight: 500;
			font-size: 24rpx;
			color: #333333;
		}
	}
	&__item1 {
		width: 750rpx;
		padding: 30rpx;
		background-color: #ffffff;
		flex-direction: column;
		align-items: flex-start;
		&__wrap {
			&__ic {
				width: 35rpx;
				height: 35rpx;
			}
			&__name {
				margin-left: 10rpx;
				font-size: 28rpx;
				color: #333333;
			}
		}
		&__detail {
			margin-left: 40rpx;
			margin-top: 15rpx;
			font-weight: 600;
			font-size: 30rpx;
			color: #333333;
		}
	}

	&__item2 {
		width: 750rpx;
		padding: 30rpx;
		background-color: #ffffff;
		flex-direction: column;
		align-items: flex-start;
		border-bottom: 0.8px solid #ebebeb;
		&__wrap {
			&__ic {
				width: 35rpx;
				height: 35rpx;
			}
			&__mark {
				margin-left: 10rpx;
				font-weight: 600;
				font-size: 28rpx;
				color: #444BF1;
			}
			&__name {
				margin-left: 10rpx;
				font-weight: 600;
				font-size: 28rpx;
				color: #333333;
			}
		}
		&__detail {
			width: 100%;
			margin-top: 15rpx;
			padding-left: 40rpx;
			justify-content: space-between;

			&__text {
				font-size: 26rpx;
				color: #666666;
			}
			&__distance {
				font-size: 26rpx;
				color: #666666;
			}
		}
	}
}

/* 加载动画化 */
.loading {
	width: 120rpx;
	margin: 80rpx auto 80rpx auto;
	justify-content: space-between;
	&__pulse-bubble {
		width: 25rpx;
		height: 25rpx;
		border-radius: 50%;
		background: #007aff;
	}
	&__pulse-bubble-1 {
		background: #ec008c;
		animation: pulse 0.4s ease 0s infinite alternate;
	}

	&__pulse-bubble-2 {
		background: #cc2b5e;
		animation: pulse 0.4s ease 0.2s infinite alternate;
	}

	&__pulse-bubble-3 {
		background: #ff6a34;
		animation: pulse 0.4s ease 0.4s infinite alternate;
	}
}

@keyframes pulse {
	from {
		opacity: 1;
		transform: scale(1);
	}

	to {
		opacity: 0.25;
		transform: scale(0.75);
	}
}
</style>
