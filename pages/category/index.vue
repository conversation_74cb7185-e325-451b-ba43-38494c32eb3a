<template>
	<view class="flex wrap">
		<public-module></public-module>
		<!-- 顶部导航 -->
		<view slot="top" class="flex nav-wrap">
			<view class="flex nav-wrap__content" :style="{ height: `${navBarHeight}px`, marginTop: `${statusBarHeight}px` }">
				<view class="flex nav-wrap__content__left"></view>
				<view class="flex nav-wrap__content__search" :style="{ marginRight: `${mpMenuRect.width}px` }" @click="onSearch">
					<image class="nav-wrap__content__search__icon" :src="iImage('ic_search.png')" mode="aspectFit" />
					<input class="nav-wrap__content__search__input" type="text" placeholder="搜索您想要的服务" cursor="30" v-model="searchWord" disabled confirm-type="search" @confirm="onSearch" />
				</view>
				<view class="flex nav-wrap__content__right"></view>
			</view>
		</view>
		<!-- 联动分类列表 -->
		<view class="category-wrap" :style="{ height: `${contentHeight}px` }">
			<!-- 左边一级分类 -->
			<scroll-view scroll-y scroll-with-animation class="left-scroll category-wrap__left">
				<view v-for="(item, index) in categoryList" :key="index" @click="onFirstCategoryClick(index)">
					<view :id="`category-wrap__left__item${item.ownerId}`" class="flex category-wrap__left__item" :class="{ active: activeFirst == index }">
						<image class="category-wrap__left__item__indicator" :src="iImage('img_category_indicator.png')" v-if="activeFirst == index"></image>
						<text class="line1">{{ item.displayName }}</text>
					</view>
				</view>
			</scroll-view>
			<!-- 右边二级分类 -->
			<scroll-view scroll-y scroll-with-animation class="category-wrap__right" :scroll-into-view="scrollToRightSectionView">
				<view class="category-wrap__right__container">
					<view class="category-wrap__right__container__section" :id="`category-wrap__right__container__section--${item.ownerId}`" v-for="(item, index) in categoryList" :key="index">
						<view class="category-wrap__right__container__section__title">
							<text>{{ item.displayName }}</text>
						</view>
						<view class="flex category-wrap__right__container__section__content">
							<view class="flex category-wrap__right__container__section__content__item" v-for="(subitem, subindex) in item.children" :key="subindex" @click.stop="onSecondCategoryClick(subindex, subitem)">
								<image :src="subitem.mediaUrl" mode="aspectFit"></image>
								<text class="line1">{{ subitem.displayName }}</text>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
		<!-- 底部TabBar -->
		<view class="bottom">
			<x-tabbar ref="xTabbar"></x-tabbar>
		</view>
	</view>
</template>

<script>
import { handleTree } from '@/common/tool.js';
export default {
	data() {
		return {
			// 小程序胶囊
			mpMenuRect: {},
			// 状态栏高度
			statusBarHeight: 0,
			// 导航栏高度
			navBarHeight: 50,
			// 内容高度
			contentHeight: 0,
			// 检索输入关键词
			searchWord: '',

			leftScrollTop: 0,

			// 分类数据（
			categoryList: [],

			// 激活的一级分类
			activeFirst: 0
		};
	},
	computed: {
		scrollToRightSectionView() {
			const value = this.categoryList[this.activeFirst];
			return `category-wrap__right__container__section--${value?.ownerId}`;
		}
		/**
		 * 右边滚动区域最后一个 section 的高度
		 */
		// sectionStyle() {
		// 	return {
		// 		'--dynamic-height': this.contentHeight - 30 + 'px'
		// 	};
		// }
	},
	watch: {},
	onLoad(options) {
		uni.hideTabBar();
		const sys = uni.getSystemInfoSync();
		this.statusBarHeight = sys.statusBarHeight;
		// 内容高度 = 屏幕高度 - 状态栏高度 - 导航栏高度 - 底部安全区域高度 - TabBar高度
		this.contentHeight = sys.windowHeight - sys.statusBarHeight - this.navBarHeight - sys.safeAreaInsets.bottom - 50;
		// #ifdef MP
		this.mpMenuRect = uni.getMenuButtonBoundingClientRect();
		// #endif

		this.getList();
	},
	onShow() {},
	methods: {
		onSearch(e) {
			this.onJump({
				url: '/pages/search/search',
				params: { keyword: e.detail.value }
			});
		},
		// 左边分类点击
		onFirstCategoryClick(index) {
			this.activeFirst = index;
		},
		// 右边分类点击
		onSecondCategoryClick(index, item) {
			this.onJump({
				url: '/pagesHome/list/index',
				params: {
					ownerType: item.ownerType,
					ownerId: item.ownerId,
					parentOwnerId: item.parentOwnerId
				}
			});
		},
		// 查询分类列表
		getList() {
			uni.$u.http
				.get('/category/display/list-by-scene', { params: { displayScene: this.cons.CATEGORY_DISPLAY_SCENE_CENTER } })
				.then((res) => {
					this.categoryList = handleTree(res, 'ownerId', 'parentOwnerId');
				})
		}
	}
};
</script>

<style>
page {
	background: #ffffff;
}
</style>
<style lang="scss" scoped>
.wrap {
	height: 100vh;
	flex-direction: column;
}
/* 导航栏 */
.nav-wrap {
	width: 750rpx;
	background: transparent;

	&__content {
		flex: 1;
		justify-content: center;

		&__left {
			width: 30rpx;
		}

		&__search {
			flex: 1;
			height: 80rpx;
			padding: 0 30rpx;
			background: #f5f5f5;
			border-radius: 15rpx;

			&__icon {
				width: 37rpx;
				height: 37rpx;
			}

			&__input {
				flex: 1;
				margin-left: 15rpx;
				font-size: 30rpx;
				color: #b3b2b2;
			}
		}

		&__right {
			width: 30rpx;
			height: 100%;
		}
	}
}

.category-wrap {
	display: flex;
	overflow: hidden;
	background: #f3f3f5;

	&__left {
		width: 220rpx;
		min-width: 220rpx;
		height: 100%;
		background-color: #ffffff;

		&__item {
			height: 100rpx;
			justify-content: center;
			font-size: 30rpx;
			color: #5f6069;

			&__indicator {
				width: 54rpx;
				height: 54rpx;
				position: absolute;
				top: 15rpx;
				left: 35rpx;
			}

			&.active {
				background: #f3f3f5;
				font-weight: bold;
				font-size: 30rpx;
				color: #1e2134;
			}
		}
	}

	&__right {
		width: 550rpx;
		background-color: #f3f3f5;

		&__container {
			padding: 16rpx;

			&__section {
				padding: 16rpx;
				margin-bottom: 20rpx;
				border-radius: 15rpx;
				background-color: #ffffff;

				// &:last-child {
				// 	min-height: var(--dynamic-height);
				// }

				&__title {
					font-weight: 800;
					font-size: 30rpx;
					color: #333333;
				}

				&__content {
					flex-wrap: wrap;

					&__item {
						width: 145.3rpx;
						margin-top: 20rpx;
						margin-right: 25rpx;
						justify-content: center;
						flex-direction: column;

						&:nth-child(3n) {
							margin-right: 0;
						}

						image {
							width: 145.3rpx;
							height: 145.3rpx;
							background: #f7f7f7;
							border-radius: 10rpx;
						}

						text {
							margin-top: 15rpx;
							font-size: 26rpx;
							color: #333333;
						}
					}
				}
			}
		}
	}
}

/* 底部Tabbar */
.bottom {
	width: 100%;
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
}
</style>
