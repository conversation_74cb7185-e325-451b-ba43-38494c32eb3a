<template>
	<view>
		<public-module></public-module>
		<u-navbar :fixed="true" :statusBar="true" :placeholder="true" :autoBack="true"></u-navbar>
		<view class="content">
			<view class="tab">
				<text class="tab-item" :class="{ active: tabIndex == 0 }" @click="onTabChange(0)">验证码登录</text>
				<text class="tab-item" :class="{ active: tabIndex == 1 }" @click="onTabChange(1)">密码登录</text>
			</view>
			<!-- 验证码表单 -->
			<view v-if="tabIndex == 0" class="input code">
				<view class="input-item">
					<text class="input-title">手机号</text>
					<u--input :customStyle="{ fontWeight: '500' }" v-model="account" type="number" placeholder="请输入手机号" fontSize="30rpx" border="none" clearable></u--input>
				</view>
				<u-gap height="50rpx"></u-gap>
				<view class="input-item">
					<text class="input-title">验证码</text>
					<u--input :customStyle="{ fontWeight: '500' }" v-model="phoneCode" type="number" maxlength="6" placeholder="请输入验证码" fontSize="30rpx" border="none"></u--input>
				</view>
				<view class="code-btn">
					<button color="获取验证码" size="mini" :disabled="!codeAvailable" @click="sendCode">{{ codeText }}</button>
				</view>
			</view>
			<!-- 密码登录表单 -->
			<view v-if="tabIndex == 1" class="input password">
				<view class="input-item">
					<text class="input-title">手机号</text>
					<u--input :customStyle="{ fontWeight: '500' }" v-model="account" type="number" placeholder="请输入手机号" fontSize="30rpx" border="none" @blur="inputBlur" autoBlur clearable></u--input>
				</view>
				<u-gap height="50rpx"></u-gap>
				<view class="input-item">
					<text class="input-title" decode>密&ensp;&ensp;码</text>
					<u--input :customStyle="{ fontWeight: '500' }" v-model="password" type="password" maxlength="16" placeholder="请输入密码" fontSize="30rpx" border="none" @blur="inputBlur" autoBlur clearable></u--input>
				</view>
				<u-gap height="30rpx"></u-gap>
				<!-- 忘记密码 -->
				<!-- 	<view class="edit-password" @click="onPageJump('/pages/login/frorgot-password')">
					<u--text iconStyle="margin-left:5rpx;color:#999999;" text="忘记密码" size="28rpx" color="#999999"
						suffixIcon="question-circle" align="right">
					</u--text>
				</view> -->
			</view>

			<!-- 提交按钮 -->
			<view class="login" @click="onLogin">登录</view>
		</view>

		<!-- #ifdef APP-PLUS -->
		<!-- 协议 -->
		<view class="protocol-view">
			<u-checkbox-group v-model="protocolValues" activeColor="#5673FF">
				<u-checkbox name="procotol" shape="circle" label="我已阅读并同意"></u-checkbox>
			</u-checkbox-group>
			<text style="font-size: 14px; color: #5673ff" @click="onJump({url:'/pages/other/protocol', params:{ type: `${constant.PROTOCOL_USER}` }})">《用户协议》</text>
			<text style="font-size: 14px; color: #666666">、</text>
			<text style="font-size: 14px; color: #5673ff" @click="onJump({url:'/pages/other/protocol', params:{ type: `${constant.PROTOCOL_PRIVACY}` }})">《隐私政策》</text>
		</view>
		<!-- #endif -->

		<!-- 协议确认 -->
		<u-popup :show="showPrivacyAlertPopup" :zIndex="999999" bgColor="transparent" mode="bottom" @close="showPrivacyAlertPopup = false" :closeable="true">
			<popup-privacy-alert :protocolType="[constant.PROTOCOL_USER, constant.PROTOCOL_PRIVACY]" @confirm="onAgreeProtocol()"></popup-privacy-alert>
		</u-popup>
		<!-- Toast -->
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import { aesEncrypt } from '@/common/utils.js';
var timer;
export default {
	data() {
		return {
			// 0 验证码登录   1密码登录
			tabIndex: 0,
			// 登录账号
			account: '',
			// 登录面膜
			password: '',
			// 登录验证码
			phoneCode: '',

			//验证码
			codeText: '获取验证码',
			codeAvailable: true,

			// 协议判定
			protocolValues: [],

			// 协议确认
			showPrivacyAlertPopup: false
		};
	},
	onUnload() {
		if (timer != null) {
			clearInterval(timer);
		}
	},
	//方法
	methods: {
		...mapMutations(['setUserInfo']),
		inputBlur(e) {
			console.log(e);
			setTimeout(() => {
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 300
				});
			}, 100);
		},
		//Tab索引改变
		onTabChange(e) {
			this.tabIndex = e;
		},
		//验证码按钮文字状态
		getCodeState() {
			const _that = this;
			_that.codeText = '60S后获取';
			_that.codeAvailable = false;
			var s = 60;
			timer = setInterval(() => {
				s--;
				_that.codeText = s + 'S后获取';
				if (s <= 0) {
					clearInterval(timer);
					_that.codeText = '获取验证码';
					_that.codeAvailable = true;
				}
			}, 1000);
		},
		// 发送验证码
		sendCode() {
			if (uni.$u.test.isEmpty(this.account)) {
				uni.$u.toast('请输入手机号码');
				return;
			}
			let httpParams = {
				mobile: this.account,
				event: 'mobilelogin'
			};
			uni.$u.http
				.post(
					'/sms/sendv2',
					{
						params: aesEncrypt(JSON.stringify(httpParams))
					},
					{
						custom: {
							load: true
						}
					}
				)
				.then((res) => {
					this.getCodeState();
					uni.$u.toast('验证码已发送成功,请注意查收');
				})
				.catch((err) => {});
		},
		// 确认阅读协议
		onAgreeProtocol() {
			this.showPrivacyAlertPopup = false;
			this.protocolValues = ['procotol'];
			this.onLogin();
		},
		// 登录
		onLogin() {
			if (uni.$u.test.isEmpty(this.account)) {
				uni.$u.toast('请输入手机号码');
				return;
			}
			if (this.tabIndex == 0) {
				if (uni.$u.test.isEmpty(this.phoneCode)) {
					uni.$u.toast('请输入验证码');
					return;
				}
			} else {
				if (uni.$u.test.isEmpty(this.password)) {
					uni.$u.toast('请输入密码');
					return;
				}
			}

			if (uni.$u.test.isEmpty(this.protocolValues)) {
				this.showPrivacyAlertPopup = true;
				return;
			}

			if (this.tabIndex == 0) {
				// 手机号验证码登录
				let httpParams = {
					mobile: this.account,
					code: this.phoneCode
				};
				uni.$u.http
					.post(
						'/user/mobilelogin',
						{
							params: encodeURIComponent(aesEncrypt(JSON.stringify(httpParams)))
						},
						{
							custom: {
								load: true
							}
						}
					)
					.then((res) => {
						this.loginFinish(res.data);
					})
					.catch((err) => {});
			} else {
				// 手机号密码登录
				let httpParams = {
					account: this.account,
					password: encodeURIComponent(this.password)
				};
				uni.$u.http
					.post(
						'/user/login',
						{
							params: encodeURIComponent(aesEncrypt(JSON.stringify(httpParams)))
						},
						{
							custom: {
								load: true
							}
						}
					)
					.then((res) => {
						this.loginFinish(res.data);
					})
					.catch((err) => {});
			}
		},
		// 登录成功
		loginFinish(res) {
			this.setUserInfo(res || {});
			this.$refs.uToast.show({
				type: 'default',
				message: '登录成功',
				duration: 800,
				complete() {
					uni.navigateBack();
				}
			});
		}
	}
};
</script>

<style>
page {
	height: 100%;
	background-color: #ffffff;
}
</style>
<style lang="scss" scoped>
.content {
	padding: 0 75rpx;
	background-color: #ffffff;
}

.tab {
	margin-top: 100rpx;
	display: flex;
	flex-direction: row;
	justify-content: flex-start;
	align-items: baseline;
}

.tab .tab-item {
	margin-right: 40rpx;
	font-size: 30rpx;
	font-weight: 500;
	color: #757575;

	&.active {
		font-size: 42rpx;
		font-weight: bold;
		color: #333333;
	}
}

.input {
	margin-top: 120rpx;
	position: relative;
}

.input-item {
	height: 100rpx;
	// padding: 0 30rpx;
	border-bottom: 1px solid #e6e6e6;
	border-radius: 10rpx;
	display: flex;
	align-items: center;
}

.input-title {
	margin-right: 30rpx;
	font-size: 30rpx;
	font-weight: 500;
	color: #333333;
}

.code-btn {
	width: 200rpx;
	height: 60rpx;
	position: absolute;
	bottom: 20rpx;
	right: 0;
	background: #f7f7f7;
	color: #5794f8;
}

.edit-password {
	width: 150rpx;
	height: 80rpx;
	margin-left: auto;
	display: flex;
	flex-direction: row;
	justify-content: flex-end;
}

.login {
	width: 100%;
	height: 100rpx;
	margin-top: 120rpx;
	font-size: 32rpx;
	font-weight: 500;
	color: #ffffff;
	border-radius: 50rpx;
	background-color: #5794f8;
	display: flex;
	justify-content: center;
	align-items: center;
}

.protocol-view {
	position: fixed;
	bottom: 100rpx;
	left: 0rpx;
	right: 0rpx;
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
}
</style>
