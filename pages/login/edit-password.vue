<template>
	<view>
		<public-module></public-module>
		<u-navbar :fixed="true" :statusBar="true" :placeholder="true" :autoBack="true"></u-navbar>
		<view class="content">
			<view class="title">重置密码</view>
			<!-- 手机号码 -->
			<view class="input-item">
				<u--input :customStyle="{ fontWeight: '500' }" v-model="account" type="number" placeholder="请输入手机号" fontSize="30rpx" border="none" :maxlength="11"></u--input>
			</view>
			<u-gap height="30rpx"></u-gap>
			<!-- 验证码 -->
			<view class="input-item">
				<u--input :customStyle="{ fontWeight: '500' }" v-model="phoneCode" type="number" maxlength="6" placeholder="请输入验证码" fontSize="30rpx" border="none"></u--input>
				<view class="code-btn">
					<u-button color="#444BF1" size="small" :text="codeText" :disabled="!codeAvailable" plain @click="sendCode"></u-button>
				</view>
			</view>
			<u-gap height="30rpx"></u-gap>
			<!-- 设置新密码 -->
			<view class="input-item">
				<u--input :customStyle="{ fontWeight: '500' }" v-model="password" type="password" placeholder="请输入新密码" fontSize="30rpx" border="none" :maxlength="8"></u--input>
			</view>
			<u-gap height="30rpx"></u-gap>
			<!-- 再次输入新密码 -->
			<view class="input-item">
				<u--input :customStyle="{ fontWeight: '500' }" v-model="againPassword" type="password" placeholder="请再次输入新密码" fontSize="30rpx" border="none" :maxlength="8"></u--input>
			</view>
			<!-- 提交按钮 -->
			<view class="submit" @click="onSubmit">确认</view>
		</view>
	</view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import { aesEncrypt } from '@/common/aesHelper.js';
var timer;
export default {
	data() {
		return {
			account: '',
			phoneCode: '',
			password: '',
			againPassword: '',

			//验证码
			codeText: '获取验证码',
			codeAvailable: true
		};
	},
	onUnload() {
		if (timer != null) {
			clearInterval(timer);
		}
	},
	//方法
	methods: {
		//验证码按钮文字状态
		getCodeState() {
			const _that = this;
			_that.codeText = '60S后获取';
			_that.codeAvailable = false;
			var s = 60;
			timer = setInterval(() => {
				s--;
				_that.codeText = s + 'S后获取';
				if (s <= 0) {
					clearInterval(timer);
					_that.codeText = '获取验证码';
					_that.codeAvailable = true;
				}
			}, 1000);
		},
		// 发送验证码
		sendCode() {
			if (uni.$u.test.isEmpty(this.account)) {
				uni.$u.toast('请输入手机号码');
				return;
			}
			const _that = this;
			let httpParams = {
				mobile: _that.account,
				event: 'resetpwd'
			};
			uni.$u.http
				.post(
					'/sms/sendv2',
					{
						params: encodeURIComponent(aesEncrypt(JSON.stringify(httpParams)))
					},
					{
						custom: {
							load: true
						}
					}
				)
				.then((res) => {
					_that.getCodeState();
					uni.$u.toast('验证码已发送成功,请注意查收');
				})
				.catch((err) => {});
		},
		// 提交
		onSubmit() {
			if (uni.$u.test.isEmpty(this.account)) {
				uni.$u.toast('请输入手机号码');
				return;
			}
			if (uni.$u.test.isEmpty(this.phoneCode)) {
				uni.$u.toast('请输入验证码');
				return;
			}
			if (uni.$u.test.isEmpty(this.password)) {
				uni.$u.toast('请输入密码');
				return;
			}
			if (uni.$u.test.isEmpty(this.againPassword)) {
				uni.$u.toast('请再次输入密码');
				return;
			}
			if (this.password != this.againPassword) {
				uni.$u.toast('两次密码输入不一致,请重新输入');
				return;
			}
			// 重置密码
			uni.$u.http
				.post(
					'/user/resetpwd',
					{
						mobile: this.account,
						code: this.phoneCode,
						password: encodeURIComponent(this.password)
					},
					{
						custom: {
							load: true
						}
					}
				)
				.then((res) => {
					uni.showToast({
						title: '密码修改成功',
						icon: 'none'
					});
					setTimeout(() => {
						this.onBack();
					}, 800);
				})
				.catch((err) => {});
		}
	}
};
</script>

<style>
page {
	background-color: #ffffff;
}
</style>
<style lang="scss" scoped>
.content {
	padding: 100rpx 75rpx;
	background-color: #ffffff;
}

.title {
	margin-bottom: 100rpx;
	font-weight: bold;
	font-size: 46rpx;
	color: #333333;
}

.input-item {
	height: 100rpx;
	padding: 0 15rpx;
	border-bottom: 0.6px solid #e6e6e6;
	border-radius: 10rpx;
	display: flex;
	align-items: center;
	position: relative;
}

.code-btn {
	width: 180rpx;
	height: 60rpx;
	position: absolute;
	bottom: 20rpx;
	right: 0;
}

.submit {
	width: 100%;
	height: 100rpx;
	margin-top: 130rpx;
	font-size: 30rpx;
	font-weight: 500;
	color: #ffffff;
	border-radius: 30rpx;
	background-color: #444BF1;
	display: flex;
	justify-content: center;
	align-items: center;
}
</style>
