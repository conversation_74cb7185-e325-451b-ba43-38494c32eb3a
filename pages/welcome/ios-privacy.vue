<template>
	<view class="mpage">
		<!-- ios端的隐私政策弹窗【初次安装会展示】 -->
		<view class="popup-content">
			<view class="title">温馨提示</view>
			<view class="message">
				<u-parse :content="`${cons.kPrivacy_Policy_Tip_Text}`"></u-parse>
			</view>
			<view class="bottom-view">
				<view class="btn cancle" @click="cancel">不同意</view>
				<view class="btn confirm" @click="confirm">同意</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {}
		},
		onShow() {
			uni.hideLoading()
		},
		methods: {
			// 不同意
			cancel() {
				if (uni.getSystemInfoSync().platform == 'ios') {
					plus.ios.import("UIApplication").sharedApplication().performSelector("exit")
				} else if (uni.getSystemInfoSync().platform == 'android') {
					plus.runtime.quit();
				}
			},
			// 同意
			confirm() {
				plus.runtime.agreePrivacy()
				uni.reLaunch({
					url: '/pages/home/<USER>',
					success() {}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>

	.mpage {
		width: 100vw;
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}

	.popup-content {
		width: 600rpx !important;
		background: #FFFFFF;
		border-radius: 20rpx;
		box-shadow: 0rpx 0rpx 8rpx 0rpx rgba(171, 171, 171, 0.32);
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}

	.title {
		margin-top: 50rpx;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}

	.message {
		margin: 40rpx 40rpx 0rpx 40rpx;
		text-align: justify;
		font-size: 32rpx;
		font-weight: 400;
		color: #333333;
		line-height: 40rpx;
	}

	.bottom-view {
		width: 100%;
		padding: 30rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
	}

	.bottom-view .btn {
		width: 260rpx;
		height: 90rpx;
		border-radius: 10rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.bottom-view .cancle {
		background: #f4f4f5;
		font-size: 32rpx;
		font-weight: 600;
		color: #666666;
	}

	.bottom-view .confirm {
		background: #6C68F0;
		font-size: 32rpx;
		font-weight: 600;
		color: #FFFFFF;
	}
</style>