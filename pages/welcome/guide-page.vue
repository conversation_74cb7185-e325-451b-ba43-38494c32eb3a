<template>
	<view class="content">
		<!-- <swiper class="content__swiper" :current="currentIndex"  :indicator-dots="true" indicator-active-color="#6C68F0" indicator-color="#DEDEDE"
			@change="onChange">
			<swiper-item v-for="(item,index) in items" :key="index">
				<view class="content__swiper__item">
					<image class="content__swiper__item__image" :src="item.image" mode="aspectFit"></image>
				</view>
			</swiper-item> 
		</swiper> -->
		<u-swiper height="100vh" :current="currentIndex" :list="items" keyName="image" imgMode="aspectFit"
			:autoplay="currentIndex !== items.length - 1" indicator indicatorMode="dot" indicatorActiveColor="#6C68F0"
			indicatorInactiveColor="#DEDEDE" :indicatorStyle="{bottom:'180rpx'}"
			@change="e => currentIndex = e.current"></u-swiper>
		<u-transition class="content__start" mode="fade" :show="currentIndex == items.length - 1">
			<image class="content__start__image" :src="iImage('img_start.png')" @tap="launchFlag"></image>
		</u-transition>
	</view>
</template>

<script>
	export default {
		name: "guide-page",
		data() {
			return {
				items: [],
				currentIndex: 0, 
			}
		},
		onLoad() {
			this.items = [{
					image: this.iImage('img_guide_page1.png')
				},
				{
					image: this.iImage('img_guide_page2.png')
				},
				{
					image: this.iImage('img_guide_page3.png')
				},
				{
					image: this.iImage('img_guide_page4.png')
				},
			]
		},
		methods: {
			// onChange(e) {
			// 	this.currentIndex = e.detail.current
			// },
			launchFlag: function() {
				/**
				 * 向本地存储中设置launchFlag的值，即启动标识；
				 */
				uni.setStorage({
					key: 'launchFlag',
					data: true,
				});
				uni.reLaunch({
					url: '/pages/home/<USER>',
					success() {}
				})

			}
		}
	}
</script>
<style lang="scss">
	.content {
		width: 100vw;
		height: 100vh;
		padding: 0;
		background: #F7F7F7;
		background-size: 100% auto;
		position: relative;

		// &__swiper {
		// 	width: 100%;
		// 	height: 100%;

		// 	&__item {
		// 		width: 100%;
		// 		height: 100%;
		// 		text-align: center;
		// 		display: flex;
		// 		align-items: flex-end;
		// 		flex-direction: column-reverse;

		// 		&__image {
		// 			width: 100%;
		// 			height: 100%;
		// 			margin: 0 auto;
		// 		}
		// 	}
		// }

		&__start {
			width: 300rpx;
			height: 90rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			position: absolute;
			left: 50%;
			bottom: 120rpx;
			transform: translateX(-50%);

			&__image {
				width: 100%;
				height: 100%;
			}
		}
	}
</style>