// nvue不能用标签命名样式，不能放在微信组件中，否则微信开发工具会报警告，无法使用标签名当做选择器
/* #ifndef APP-NVUE */
view,
page,
text,
button,
image,
textarea,
scroll-view,
input {
	box-sizing: border-box;
}
image {
	display: block;
}
button {
	margin: 0;
	padding: 0;
	background-color: #ffff;
}
button::after {
	border: none;
}
/* #endif */
/* #ifdef MP-ALIPAY */
input {
	background: transparent;
}
button {
	border: none;
}
/* #endif */
.disable {
	opacity: 0.6;
	filter: grayscale(100%);
	pointer-events: none;
}
.flex {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: row;
	align-items: center;
}
.flex-row {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: row;
}
.flex-col {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: column;
}
.u-flex-m {
	/* #ifndef APP-NVUE */
	-webkit-box-flex: 1;
	-webkit-flex: 1;
	/* #endif */
	flex: 1;
	overflow: hidden;
}
.scroll-view_H {
	/* #ifndef APP-NVUE */
	white-space: nowrap;
	/* #endif */
}

// 定义flex等分
// @for $i from 0 through 12 {
// 	.u-flex-#{$i},
// 	.flex-#{$i} {
// 		flex: $i;
// 	}
// }

// 定义内外边距，历遍1-80
// @for $i from 0 through 80 {
// 	// 定义字体大小
// 	.u-f-s-#{$i},
// 	.fs#{$i} {
// 		font-size: $i + rpx !important;
// 	}
// 	// 只要双数和能被5除尽的数
// 	@if $i % 2 == 0 or $i % 5 == 0 {
// 		// 得出：u-margin-30或者u-m-30
// 		.u-margin-#{$i},
// 		.u-m-#{$i},
// 		.m#{$i} {
// 			margin: $i + rpx !important;
// 		}

// 		// 得出：u-padding-30或者u-p-30
// 		.u-padding-#{$i},
// 		.u-p-#{$i},
// 		.p#{$i} {
// 			padding: $i + rpx !important;
// 		}

// 		@each $short, $long in l left, t top, r right, b bottom {
// 			// 缩写版，结果如： u-m-l-30
// 			// 定义外边距
// 			.u-m-#{$short}-#{$i},
// 			.m#{$short}#{$i} {
// 				margin-#{$long}: $i + rpx !important;
// 			}

// 			// 定义内边距
// 			.u-p-#{$short}-#{$i},
// 			.p#{$short}#{$i} {
// 				padding-#{$long}: $i + rpx !important;
// 			}

// 			// 完整版，结果如：u-margin-left-30
// 			// 定义外边距
// 			.u-margin-#{$long}-#{$i},
// 			.margin#{$long}#{$i} {
// 				margin-#{$long}: $i + rpx !important;
// 			}

// 			// 定义内边距
// 			.u-padding-#{$long}-#{$i},
// 			.padding#{$long}#{$i} {
// 				padding-#{$long}: $i + rpx !important;
// 			}
// 		}
// 	}
// }
//定位
@each $p in relative, absolute, fixed {
	.u-#{$p} {
		position: #{$p} !important;
	}
}
//层级
// @for $i from 1 through 100 {
// 	.z#{$i},
// 	.zIndex#{$i} {
// 		z-index: #{$i} !important;
// 	}
// } 
//对齐方式
@each $t in left, center, right {
	.text-#{$t} {
		text-align: #{$t} !important;
	}
}
//行数限制
@for $i from 1 through 5 {
	.line#{$i} {
		@if $i == '1' {
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		} @else {
			display: -webkit-box !important;
			overflow: hidden;
			text-overflow: ellipsis;
			word-break: break-all;
			-webkit-line-clamp: $i;
			-webkit-box-orient: vertical !important;
		}
	}
}

//宽高
.w100 {
	width: 100% !important;
}
.h100 {
	height: 100% !important;
}
.vw100 {
	height: 100vw !important;
}
.vh100 {
	height: 100vh !important;
}

@keyframes show {
	0% {
		opacity: 0;
		transform: translateY(50px);
	}
	100% {
		opacity: 1;
		transform: translateY(0px);
	}
}
